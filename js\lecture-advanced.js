// Advanced Interactive Lectures JavaScript
class InteractiveLecture {
    constructor() {
        this.participants = [];
        this.polls = [];
        this.questions = [];
        this.resources = [];
        this.whiteboardData = [];
        this.isRecording = false;
        this.breakoutRooms = [];
        this.chatMessages = [];
        
        this.initializeEventListeners();
        this.initializeWebRTC();
        this.initializeWebSocket();
    }

    // WebRTC Integration for Video Conferencing
    initializeWebRTC() {
        this.localStream = null;
        this.remoteStreams = [];
        this.peerConnections = new Map();
        
        // Configuration for STUN/TURN servers
        this.rtcConfiguration = {
            iceServers: [
                { urls: 'stun:stun.l.google.com:19302' },
                { urls: 'stun:stun1.l.google.com:19302' }
            ]
        };
    }

    // WebSocket for real-time communication
    initializeWebSocket() {
        this.socket = null;
        // In a real implementation, you would connect to your WebSocket server
        // this.socket = io('ws://localhost:3000');
        
        this.simulateWebSocket();
    }

    // Simulate WebSocket functionality for demo
    simulateWebSocket() {
        setInterval(() => {
            this.simulateRealTimeUpdates();
        }, 5000);
    }

    // Advanced Video Controls
    async startAdvancedVideo() {
        try {
            const constraints = {
                video: {
                    width: { min: 640, ideal: 1280, max: 1920 },
                    height: { min: 480, ideal: 720, max: 1080 },
                    frameRate: { ideal: 30 }
                },
                audio: {
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: true
                }
            };

            this.localStream = await navigator.mediaDevices.getUserMedia(constraints);
            this.displayLocalVideo();
            
            // Enable advanced video features
            this.enableVirtualBackgrounds();
            this.enableVideoFilters();
            
            this.showNotification('Advanced video features enabled!', 'success');
        } catch (error) {
            console.error('Error accessing media devices:', error);
            this.showNotification('Could not access camera/microphone', 'error');
        }
    }

    displayLocalVideo() {
        const videoContainer = document.getElementById('videoContainer');
        const videoElement = document.createElement('video');
        videoElement.srcObject = this.localStream;
        videoElement.autoplay = true;
        videoElement.muted = true;
        videoElement.style.width = '100%';
        videoElement.style.height = '100%';
        videoElement.style.objectFit = 'cover';
        videoElement.style.borderRadius = '10px';
        
        videoContainer.innerHTML = '';
        videoContainer.appendChild(videoElement);
    }

    // Virtual Backgrounds
    enableVirtualBackgrounds() {
        const backgrounds = [
            'classroom.jpg',
            'laboratory.jpg',
            'library.jpg',
            'space.jpg',
            'none'
        ];
        
        this.createBackgroundSelector(backgrounds);
    }

    createBackgroundSelector(backgrounds) {
        const selector = document.createElement('div');
        selector.className = 'background-selector';
        selector.innerHTML = `
            <h4>Virtual Backgrounds</h4>
            <div class="background-options">
                ${backgrounds.map(bg => `
                    <div class="background-option" data-bg="${bg}">
                        <div class="background-preview" style="background-image: url('assets/backgrounds/${bg}')"></div>
                        <span>${bg.replace('.jpg', '').replace('_', ' ')}</span>
                    </div>
                `).join('')}
            </div>
        `;
        
        // Add to controls panel
        const controlsPanel = document.querySelector('.controls-panel');
        controlsPanel.appendChild(selector);
        
        // Add event listeners
        selector.addEventListener('click', (e) => {
            const option = e.target.closest('.background-option');
            if (option) {
                this.applyVirtualBackground(option.dataset.bg);
            }
        });
    }

    applyVirtualBackground(background) {
        // In a real implementation, you would use ML models like BodyPix or MediaPipe
        // For demo purposes, we'll just show a notification
        this.showNotification(`Virtual background applied: ${background}`, 'success');
    }

    // Advanced Whiteboard Features
    initializeAdvancedWhiteboard() {
        this.whiteboardLayers = [];
        this.whiteboardHistory = [];
        this.collaborativeDrawing = true;
        
        this.addWhiteboardTools();
        this.enableWhiteboardSync();
    }

    addWhiteboardTools() {
        const toolsContainer = document.querySelector('.whiteboard-tools');
        const advancedTools = `
            <button class="tool-btn" onclick="lectureApp.selectTool('shapes')" data-tool="shapes">
                <i class="fas fa-shapes"></i>
            </button>
            <button class="tool-btn" onclick="lectureApp.selectTool('highlighter')" data-tool="highlighter">
                <i class="fas fa-highlighter"></i>
            </button>
            <button class="tool-btn" onclick="lectureApp.selectTool('sticky')" data-tool="sticky">
                <i class="fas fa-sticky-note"></i>
            </button>
            <button class="tool-btn" onclick="lectureApp.insertImage()">
                <i class="fas fa-image"></i>
            </button>
            <button class="tool-btn" onclick="lectureApp.undoWhiteboard()">
                <i class="fas fa-undo"></i>
            </button>
            <button class="tool-btn" onclick="lectureApp.redoWhiteboard()">
                <i class="fas fa-redo"></i>
            </button>
        `;
        
        toolsContainer.innerHTML += advancedTools;
    }

    // Advanced Polling System
    createAdvancedPoll() {
        const pollModal = this.createModal('Create Advanced Poll');
        const pollForm = `
            <form id="pollForm">
                <div class="form-group">
                    <label>Poll Question:</label>
                    <input type="text" id="pollQuestion" required>
                </div>
                <div class="form-group">
                    <label>Poll Type:</label>
                    <select id="pollType">
                        <option value="multiple">Multiple Choice</option>
                        <option value="rating">Rating Scale</option>
                        <option value="text">Text Response</option>
                        <option value="word-cloud">Word Cloud</option>
                    </select>
                </div>
                <div class="form-group" id="optionsContainer">
                    <label>Options:</label>
                    <div class="options-list">
                        <input type="text" placeholder="Option 1" class="poll-option-input">
                        <input type="text" placeholder="Option 2" class="poll-option-input">
                    </div>
                    <button type="button" onclick="lectureApp.addPollOption()">Add Option</button>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="allowMultiple"> Allow multiple selections
                    </label>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="showResults"> Show results immediately
                    </label>
                </div>
                <div class="form-actions">
                    <button type="submit">Create Poll</button>
                    <button type="button" onclick="lectureApp.closeModal()">Cancel</button>
                </div>
            </form>
        `;
        
        pollModal.innerHTML = pollForm;
        document.body.appendChild(pollModal);
        
        // Add form submission handler
        document.getElementById('pollForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.submitAdvancedPoll();
        });
    }

    submitAdvancedPoll() {
        const question = document.getElementById('pollQuestion').value;
        const type = document.getElementById('pollType').value;
        const options = Array.from(document.querySelectorAll('.poll-option-input'))
            .map(input => input.value)
            .filter(value => value.trim());
        
        const poll = {
            id: Date.now(),
            question,
            type,
            options,
            responses: [],
            active: true,
            createdAt: new Date()
        };
        
        this.polls.push(poll);
        this.displayPoll(poll);
        this.closeModal();
        
        this.showNotification('Advanced poll created successfully!', 'success');
    }

    // Breakout Rooms
    createBreakoutRooms() {
        const modal = this.createModal('Create Breakout Rooms');
        const roomForm = `
            <form id="breakoutForm">
                <div class="form-group">
                    <label>Number of Rooms:</label>
                    <input type="number" id="roomCount" min="2" max="10" value="3">
                </div>
                <div class="form-group">
                    <label>Assignment Method:</label>
                    <select id="assignmentMethod">
                        <option value="random">Random Assignment</option>
                        <option value="manual">Manual Assignment</option>
                        <option value="self-select">Self Selection</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Room Duration (minutes):</label>
                    <input type="number" id="roomDuration" min="5" max="60" value="10">
                </div>
                <div class="form-actions">
                    <button type="submit">Create Rooms</button>
                    <button type="button" onclick="lectureApp.closeModal()">Cancel</button>
                </div>
            </form>
        `;
        
        modal.innerHTML = roomForm;
        document.body.appendChild(modal);
        
        document.getElementById('breakoutForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.setupBreakoutRooms();
        });
    }

    setupBreakoutRooms() {
        const roomCount = parseInt(document.getElementById('roomCount').value);
        const method = document.getElementById('assignmentMethod').value;
        const duration = parseInt(document.getElementById('roomDuration').value);
        
        // Create rooms
        for (let i = 1; i <= roomCount; i++) {
            const room = {
                id: i,
                name: `Room ${i}`,
                participants: [],
                maxParticipants: Math.ceil(this.participants.length / roomCount),
                duration: duration,
                active: true
            };
            this.breakoutRooms.push(room);
        }
        
        // Assign participants
        this.assignParticipantsToRooms(method);
        
        this.closeModal();
        this.showNotification(`${roomCount} breakout rooms created!`, 'success');
    }

    // Screen Annotation
    enableScreenAnnotation() {
        const annotationOverlay = document.createElement('div');
        annotationOverlay.id = 'annotationOverlay';
        annotationOverlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.1);
            z-index: 9999;
            pointer-events: none;
        `;
        
        const annotationCanvas = document.createElement('canvas');
        annotationCanvas.width = window.innerWidth;
        annotationCanvas.height = window.innerHeight;
        annotationCanvas.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            pointer-events: auto;
            cursor: crosshair;
        `;
        
        annotationOverlay.appendChild(annotationCanvas);
        document.body.appendChild(annotationOverlay);
        
        this.setupAnnotationTools(annotationCanvas);
    }

    // Real-time Collaboration
    enableRealTimeCollaboration() {
        // Simulate real-time cursor tracking
        document.addEventListener('mousemove', (e) => {
            this.broadcastCursorPosition(e.clientX, e.clientY);
        });
        
        // Simulate collaborative editing
        this.setupCollaborativeEditing();
    }

    broadcastCursorPosition(x, y) {
        // In a real implementation, you would broadcast this through WebSocket
        this.simulateCursorUpdate(x, y);
    }

    // Analytics and Insights
    generateLectureAnalytics() {
        const analytics = {
            duration: this.getLectureDuration(),
            participantEngagement: this.calculateEngagement(),
            pollResponses: this.aggregatePollData(),
            questionActivity: this.analyzeQuestionActivity(),
            whiteboardUsage: this.analyzeWhiteboardUsage(),
            attendanceRate: this.calculateAttendanceRate()
        };
        
        this.displayAnalytics(analytics);
        return analytics;
    }

    calculateEngagement() {
        // Calculate engagement based on various factors
        const factors = {
            pollParticipation: this.polls.reduce((acc, poll) => acc + poll.responses.length, 0),
            questionActivity: this.questions.length,
            whiteboardInteraction: this.whiteboardData.length,
            videoDuration: this.getVideoWatchTime()
        };
        
        return factors;
    }

    // Recording and Playback
    startRecording() {
        this.isRecording = true;
        this.recordingData = {
            startTime: Date.now(),
            video: [],
            audio: [],
            whiteboard: [],
            polls: [],
            questions: []
        };
        
        this.showNotification('Recording started', 'info');
    }

    stopRecording() {
        this.isRecording = false;
        this.recordingData.endTime = Date.now();
        
        // Process and save recording
        this.processRecording();
        this.showNotification('Recording stopped and saved', 'success');
    }

    // Accessibility Features
    enableAccessibility() {
        this.addClosedCaptions();
        this.addKeyboardNavigation();
        this.addScreenReaderSupport();
        this.addHighContrastMode();
    }

    addClosedCaptions() {
        const captionsContainer = document.createElement('div');
        captionsContainer.id = 'closedCaptions';
        captionsContainer.style.cssText = `
            position: absolute;
            bottom: 60px;
            left: 20px;
            right: 20px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 16px;
            text-align: center;
            display: none;
        `;
        
        document.querySelector('.video-container').appendChild(captionsContainer);
    }

    // Utility Methods
    createModal(title) {
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        `;
        
        const modalContent = document.createElement('div');
        modalContent.className = 'modal-content';
        modalContent.style.cssText = `
            background: white;
            padding: 20px;
            border-radius: 10px;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        `;
        
        modalContent.innerHTML = `<h3>${title}</h3>`;
        modal.appendChild(modalContent);
        
        return modalContent;
    }

    closeModal() {
        const modal = document.querySelector('.modal');
        if (modal) {
            modal.remove();
        }
    }

    showNotification(message, type = 'info') {
        const notification = document.getElementById('notification');
        notification.textContent = message;
        notification.className = `notification ${type} show`;
        
        setTimeout(() => {
            notification.classList.remove('show');
        }, 3000);
    }

    // Initialize all event listeners
    initializeEventListeners() {
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });
        
        window.addEventListener('beforeunload', (e) => {
            if (this.isRecording) {
                e.preventDefault();
                e.returnValue = 'Recording in progress. Are you sure you want to leave?';
            }
        });
    }

    handleKeyboardShortcuts(e) {
        // Ctrl/Cmd + M: Toggle mute
        if ((e.ctrlKey || e.metaKey) && e.key === 'm') {
            e.preventDefault();
            toggleMute();
        }
        
        // Ctrl/Cmd + D: Toggle video
        if ((e.ctrlKey || e.metaKey) && e.key === 'd') {
            e.preventDefault();
            toggleVideo();
        }
        
        // Ctrl/Cmd + Shift + A: Enable annotation
        if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'A') {
            e.preventDefault();
            this.enableScreenAnnotation();
        }
    }

    // Simulate real-time updates for demo
    simulateRealTimeUpdates() {
        // Simulate new participants joining
        if (Math.random() > 0.8) {
            this.addRandomParticipant();
        }
        
        // Simulate poll responses
        if (this.polls.length > 0 && Math.random() > 0.7) {
            this.addRandomPollResponse();
        }
        
        // Simulate questions
        if (Math.random() > 0.9) {
            this.addRandomQuestion();
        }
    }

    addRandomParticipant() {
        const names = ['Alex Johnson', 'Maria Garcia', 'David Chen', 'Sarah Wilson', 'Michael Brown'];
        const randomName = names[Math.floor(Math.random() * names.length)];
        
        const participant = {
            id: Date.now(),
            name: randomName,
            avatar: randomName.split(' ').map(n => n[0]).join(''),
            joinTime: new Date(),
            status: 'online'
        };
        
        this.participants.push(participant);
        this.updateParticipantsList();
    }

    addRandomPollResponse() {
        const activePoll = this.polls.find(p => p.active);
        if (activePoll) {
            const randomOption = Math.floor(Math.random() * activePoll.options.length);
            activePoll.responses.push({
                participantId: Math.random(),
                option: randomOption,
                timestamp: new Date()
            });
            this.updatePollDisplay(activePoll);
        }
    }

    addRandomQuestion() {
        const questions = [
            'Can you explain that concept again?',
            'What are the practical applications?',
            'How does this relate to the previous topic?',
            'Are there any examples we can try?'
        ];
        
        const randomQuestion = questions[Math.floor(Math.random() * questions.length)];
        const randomAuthor = `Student ${Math.floor(Math.random() * 100)}`;
        
        addQuestion(randomAuthor, randomQuestion);
    }

    updateParticipantsList() {
        const participantsList = document.querySelector('.participants-list');
        const participantsCount = document.querySelector('.control-group h4');
        
        participantsCount.innerHTML = `<i class="fas fa-users"></i> Participants (${this.participants.length})`;
        
        // Update the list (you would implement this based on your UI needs)
    }
}

// Initialize the application
let lectureApp;

document.addEventListener('DOMContentLoaded', function() {
    lectureApp = new InteractiveLecture();
    
    // Add advanced features to existing functions
    window.startAdvancedVideo = () => lectureApp.startAdvancedVideo();
    window.createAdvancedPoll = () => lectureApp.createAdvancedPoll();
    window.createBreakoutRooms = () => lectureApp.createBreakoutRooms();
    window.startRecording = () => lectureApp.startRecording();
    window.stopRecording = () => lectureApp.stopRecording();
    window.generateAnalytics = () => lectureApp.generateLectureAnalytics();
    window.enableAccessibility = () => lectureApp.enableAccessibility();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = InteractiveLecture;
}