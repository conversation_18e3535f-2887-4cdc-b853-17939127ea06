<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Lectures - SimLab HUB</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            overflow-x: hidden;
        }

        .lecture-container {
            display: grid;
            grid-template-areas: 
                "header header header"
                "video sidebar controls"
                "whiteboard sidebar controls";
            grid-template-columns: 2fr 1fr 300px;
            grid-template-rows: 60px 1fr 1fr;
            height: 100vh;
            gap: 10px;
            padding: 10px;
        }

        .header {
            grid-area: header;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            color: #2563eb;
            font-size: 1.5rem;
            font-weight: 600;
        }

        .header-controls {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .btn-primary {
            background: #2563eb;
            color: white;
        }

        .btn-primary:hover {
            background: #1d4ed8;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        .video-section {
            grid-area: video;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .video-container {
            position: relative;
            width: 100%;
            height: 100%;
            background: #000;
            border-radius: 10px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .video-placeholder {
            color: #fff;
            font-size: 1.2rem;
            text-align: center;
        }

        .video-controls {
            position: absolute;
            bottom: 10px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 10px;
            background: rgba(0, 0, 0, 0.7);
            padding: 10px;
            border-radius: 25px;
        }

        .video-controls button {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .video-controls button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .whiteboard-section {
            grid-area: whiteboard;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .whiteboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .whiteboard-tools {
            display: flex;
            gap: 10px;
        }

        .tool-btn {
            width: 35px;
            height: 35px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .tool-btn.active {
            background: #2563eb;
            color: white;
        }

        .tool-btn:not(.active) {
            background: #f3f4f6;
            color: #6b7280;
        }

        .whiteboard-canvas {
            width: 100%;
            height: calc(100% - 60px);
            border: 2px solid #e5e7eb;
            border-radius: 10px;
            background: white;
            cursor: crosshair;
        }

        .sidebar {
            grid-area: sidebar;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .sidebar-section {
            background: #f8fafc;
            border-radius: 10px;
            padding: 15px;
        }

        .sidebar-section h3 {
            color: #2563eb;
            margin-bottom: 10px;
            font-size: 1.1rem;
        }

        .poll-container {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .poll-option {
            background: white;
            padding: 10px;
            border-radius: 8px;
            border: 2px solid #e5e7eb;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .poll-option:hover {
            border-color: #2563eb;
            background: #eff6ff;
        }

        .poll-option.selected {
            border-color: #2563eb;
            background: #2563eb;
            color: white;
        }

        .poll-results {
            font-size: 0.9rem;
            color: #6b7280;
        }

        .qa-section {
            max-height: 200px;
            overflow-y: auto;
        }

        .question {
            background: white;
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 10px;
            border-left: 4px solid #2563eb;
        }

        .question-author {
            font-weight: 600;
            color: #2563eb;
            font-size: 0.9rem;
        }

        .question-text {
            margin-top: 5px;
            color: #4b5563;
        }

        .question-input {
            width: 100%;
            padding: 10px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            margin-top: 10px;
            font-size: 14px;
        }

        .question-input:focus {
            outline: none;
            border-color: #2563eb;
        }

        .controls-panel {
            grid-area: controls;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .control-group {
            background: #f8fafc;
            border-radius: 10px;
            padding: 15px;
        }

        .control-group h4 {
            color: #2563eb;
            margin-bottom: 10px;
            font-size: 1rem;
        }

        .participants-list {
            max-height: 150px;
            overflow-y: auto;
        }

        .participant {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 5px 0;
            border-bottom: 1px solid #e5e7eb;
        }

        .participant:last-child {
            border-bottom: none;
        }

        .participant-avatar {
            width: 25px;
            height: 25px;
            border-radius: 50%;
            background: #2563eb;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .participant-status {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #10b981;
            margin-left: auto;
        }

        .resource-item {
            background: white;
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .resource-item:hover {
            background: #eff6ff;
        }

        .resource-icon {
            width: 35px;
            height: 35px;
            border-radius: 8px;
            background: #2563eb;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .resource-info {
            flex: 1;
        }

        .resource-name {
            font-weight: 600;
            color: #1f2937;
            font-size: 0.9rem;
        }

        .resource-type {
            color: #6b7280;
            font-size: 0.8rem;
        }

        .upload-area {
            border: 2px dashed #d1d5db;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            color: #6b7280;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .upload-area:hover {
            border-color: #2563eb;
            background: #eff6ff;
        }

        .upload-area.dragover {
            border-color: #2563eb;
            background: #eff6ff;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #10b981;
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            transform: translateX(100%);
            transition: transform 0.3s ease;
            z-index: 1000;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.error {
            background: #ef4444;
        }

        .notification.info {
            background: #3b82f6;
        }

        @media (max-width: 1024px) {
            .lecture-container {
                grid-template-areas: 
                    "header"
                    "video"
                    "sidebar"
                    "whiteboard"
                    "controls";
                grid-template-columns: 1fr;
                grid-template-rows: 60px 1fr auto auto auto;
            }
        }

        @media (max-width: 768px) {
            .lecture-container {
                padding: 5px;
                gap: 5px;
            }
            
            .header {
                padding: 0 10px;
            }
            
            .header h1 {
                font-size: 1.2rem;
            }
            
            .video-section,
            .whiteboard-section,
            .sidebar,
            .controls-panel {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="lecture-container">
        <!-- Header -->
        <div class="header">
            <h1><i class="fas fa-chalkboard-teacher"></i> Interactive Lectures</h1>
            <div class="header-controls">
                <button class="btn btn-secondary" onclick="toggleFullscreen()">
                    <i class="fas fa-expand"></i> Fullscreen
                </button>
                <button class="btn btn-primary" onclick="startLecture()">
                    <i class="fas fa-play"></i> Start Lecture
                </button>
            </div>
        </div>

        <!-- Video Section -->
        <div class="video-section">
            <div class="video-container" id="videoContainer">
                <div class="video-placeholder">
                    <i class="fas fa-video" style="font-size: 3rem; margin-bottom: 10px;"></i>
                    <p>Video stream will appear here</p>
                    <button class="btn btn-primary" onclick="startVideo()" style="margin-top: 15px;">
                        <i class="fas fa-video"></i> Start Video
                    </button>
                </div>
                <div class="video-controls">
                    <button onclick="toggleMute()" id="muteBtn">
                        <i class="fas fa-microphone"></i>
                    </button>
                    <button onclick="toggleVideo()" id="videoBtn">
                        <i class="fas fa-video"></i>
                    </button>
                    <button onclick="shareScreen()">
                        <i class="fas fa-desktop"></i>
                    </button>
                    <button onclick="toggleSettings()">
                        <i class="fas fa-cog"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Whiteboard Section -->
        <div class="whiteboard-section">
            <div class="whiteboard-header">
                <h3><i class="fas fa-marker"></i> Whiteboard</h3>
                <div class="whiteboard-tools">
                    <button class="tool-btn active" onclick="selectTool('pen')" data-tool="pen">
                        <i class="fas fa-pen"></i>
                    </button>
                    <button class="tool-btn" onclick="selectTool('eraser')" data-tool="eraser">
                        <i class="fas fa-eraser"></i>
                    </button>
                    <button class="tool-btn" onclick="selectTool('text')" data-tool="text">
                        <i class="fas fa-font"></i>
                    </button>
                    <button class="tool-btn" onclick="clearWhiteboard()">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            <canvas class="whiteboard-canvas" id="whiteboard"></canvas>
        </div>

        <!-- Sidebar -->
        <div class="sidebar">
            <!-- Live Poll -->
            <div class="sidebar-section">
                <h3><i class="fas fa-poll"></i> Live Poll</h3>
                <div class="poll-container">
                    <div class="poll-option" onclick="votePoll(1)">
                        <span>Option A</span>
                        <span class="poll-results">45%</span>
                    </div>
                    <div class="poll-option" onclick="votePoll(2)">
                        <span>Option B</span>
                        <span class="poll-results">30%</span>
                    </div>
                    <div class="poll-option" onclick="votePoll(3)">
                        <span>Option C</span>
                        <span class="poll-results">25%</span>
                    </div>
                    <button class="btn btn-primary" style="margin-top: 10px;" onclick="createPoll()">
                        <i class="fas fa-plus"></i> New Poll
                    </button>
                </div>
            </div>

            <!-- Q&A Section -->
            <div class="sidebar-section">
                <h3><i class="fas fa-question-circle"></i> Q&A</h3>
                <div class="qa-section">
                    <div class="question">
                        <div class="question-author">John Doe</div>
                        <div class="question-text">What is the molecular structure of water?</div>
                    </div>
                    <div class="question">
                        <div class="question-author">Jane Smith</div>
                        <div class="question-text">Can you explain the concept of entropy?</div>
                    </div>
                </div>
                <input type="text" class="question-input" placeholder="Ask a question..." 
                       onkeypress="handleQuestionSubmit(event)">
            </div>
        </div>

        <!-- Controls Panel -->
        <div class="controls-panel">
            <!-- Participants -->
            <div class="control-group">
                <h4><i class="fas fa-users"></i> Participants (24)</h4>
                <div class="participants-list">
                    <div class="participant">
                        <div class="participant-avatar">JD</div>
                        <span>John Doe</span>
                        <div class="participant-status"></div>
                    </div>
                    <div class="participant">
                        <div class="participant-avatar">JS</div>
                        <span>Jane Smith</span>
                        <div class="participant-status"></div>
                    </div>
                    <div class="participant">
                        <div class="participant-avatar">MB</div>
                        <span>Mike Brown</span>
                        <div class="participant-status"></div>
                    </div>
                    <div class="participant">
                        <div class="participant-avatar">AL</div>
                        <span>Alice Lee</span>
                        <div class="participant-status"></div>
                    </div>
                </div>
            </div>

            <!-- Resources -->
            <div class="control-group">
                <h4><i class="fas fa-folder"></i> Resources</h4>
                <div class="resources-list">
                    <div class="resource-item">
                        <div class="resource-icon">
                            <i class="fas fa-file-pdf"></i>
                        </div>
                        <div class="resource-info">
                            <div class="resource-name">Chemistry Notes.pdf</div>
                            <div class="resource-type">PDF Document</div>
                        </div>
                    </div>
                    <div class="resource-item">
                        <div class="resource-icon">
                            <i class="fas fa-link"></i>
                        </div>
                        <div class="resource-info">
                            <div class="resource-name">Simulation Link</div>
                            <div class="resource-type">External Link</div>
                        </div>
                    </div>
                </div>
                <div class="upload-area" onclick="uploadResource()">
                    <i class="fas fa-cloud-upload-alt" style="font-size: 1.5rem; margin-bottom: 5px;"></i>
                    <p>Drop files or click to upload</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification -->
    <div class="notification" id="notification"></div>

    <script>
        // Global variables
        let isLectureActive = false;
        let isMuted = false;
        let isVideoOn = true;
        let currentTool = 'pen';
        let isDrawing = false;
        let whiteboard = null;
        let ctx = null;

        // Initialize application
        document.addEventListener('DOMContentLoaded', function() {
            initWhiteboard();
            initDragDrop();
            showNotification('Welcome to Interactive Lectures!', 'info');
        });

        // Lecture controls
        function startLecture() {
            isLectureActive = !isLectureActive;
            const btn = document.querySelector('.header .btn-primary');
            
            if (isLectureActive) {
                btn.innerHTML = '<i class="fas fa-stop"></i> Stop Lecture';
                btn.style.background = '#ef4444';
                showNotification('Lecture started successfully!', 'success');
            } else {
                btn.innerHTML = '<i class="fas fa-play"></i> Start Lecture';
                btn.style.background = '#2563eb';
                showNotification('Lecture stopped', 'info');
            }
        }

        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }

        // Video controls
        function startVideo() {
            const placeholder = document.querySelector('.video-placeholder');
            placeholder.style.display = 'none';
            
            // Simulate video stream
            const videoContainer = document.getElementById('videoContainer');
            videoContainer.style.background = 'linear-gradient(45deg, #667eea, #764ba2)';
            videoContainer.innerHTML += '<div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 1.2rem;">🎥 Live Video Stream</div>';
            
            showNotification('Video stream started', 'success');
        }

        function toggleMute() {
            isMuted = !isMuted;
            const btn = document.getElementById('muteBtn');
            btn.innerHTML = isMuted ? '<i class="fas fa-microphone-slash"></i>' : '<i class="fas fa-microphone"></i>';
            btn.style.background = isMuted ? '#ef4444' : 'rgba(255, 255, 255, 0.2)';
            showNotification(isMuted ? 'Microphone muted' : 'Microphone unmuted', 'info');
        }

        function toggleVideo() {
            isVideoOn = !isVideoOn;
            const btn = document.getElementById('videoBtn');
            btn.innerHTML = isVideoOn ? '<i class="fas fa-video"></i>' : '<i class="fas fa-video-slash"></i>';
            btn.style.background = isVideoOn ? 'rgba(255, 255, 255, 0.2)' : '#ef4444';
            showNotification(isVideoOn ? 'Video turned on' : 'Video turned off', 'info');
        }

        function shareScreen() {
            showNotification('Screen sharing started', 'success');
        }

        function toggleSettings() {
            showNotification('Settings panel opened', 'info');
        }

        // Whiteboard functionality
        function initWhiteboard() {
            whiteboard = document.getElementById('whiteboard');
            ctx = whiteboard.getContext('2d');
            
            // Set canvas size
            const rect = whiteboard.getBoundingClientRect();
            whiteboard.width = rect.width;
            whiteboard.height = rect.height;
            
            // Set drawing properties
            ctx.lineWidth = 2;
            ctx.lineCap = 'round';
            ctx.strokeStyle = '#2563eb';
            
            // Add event listeners
            whiteboard.addEventListener('mousedown', startDrawing);
            whiteboard.addEventListener('mousemove', draw);
            whiteboard.addEventListener('mouseup', stopDrawing);
            whiteboard.addEventListener('mouseout', stopDrawing);
        }

        function startDrawing(e) {
            isDrawing = true;
            draw(e);
        }

        function draw(e) {
            if (!isDrawing) return;
            
            const rect = whiteboard.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            ctx.globalCompositeOperation = currentTool === 'eraser' ? 'destination-out' : 'source-over';
            ctx.lineWidth = currentTool === 'eraser' ? 10 : 2;
            
            ctx.lineTo(x, y);
            ctx.stroke();
            ctx.beginPath();
            ctx.moveTo(x, y);
        }

        function stopDrawing() {
            if (isDrawing) {
                isDrawing = false;
                ctx.beginPath();
            }
        }

        function selectTool(tool) {
            currentTool = tool;
            
            // Update tool buttons
            document.querySelectorAll('.tool-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-tool="${tool}"]`).classList.add('active');
            
            // Update cursor
            whiteboard.style.cursor = tool === 'eraser' ? 'crosshair' : 'crosshair';
        }

        function clearWhiteboard() {
            ctx.clearRect(0, 0, whiteboard.width, whiteboard.height);
            showNotification('Whiteboard cleared', 'info');
        }

        // Poll functionality
        function votePoll(option) {
            document.querySelectorAll('.poll-option').forEach(opt => {
                opt.classList.remove('selected');
            });
            
            event.target.closest('.poll-option').classList.add('selected');
            showNotification(`Voted for option ${option}`, 'success');
        }

        function createPoll() {
            const question = prompt('Enter poll question:');
            if (question) {
                showNotification('New poll created successfully!', 'success');
            }
        }

        // Q&A functionality
        function handleQuestionSubmit(event) {
            if (event.key === 'Enter') {
                const question = event.target.value.trim();
                if (question) {
                    addQuestion('You', question);
                    event.target.value = '';
                    showNotification('Question submitted!', 'success');
                }
            }
        }

        function addQuestion(author, text) {
            const qaSection = document.querySelector('.qa-section');
            const questionDiv = document.createElement('div');
            questionDiv.className = 'question';
            questionDiv.innerHTML = `
                <div class="question-author">${author}</div>
                <div class="question-text">${text}</div>
            `;
            qaSection.appendChild(questionDiv);
            qaSection.scrollTop = qaSection.scrollHeight;
        }

        // Resource sharing
        function uploadResource() {
            const input = document.createElement('input');
            input.type = 'file';
            input.multiple = true;
            input.accept = '.pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.jpg,.jpeg,.png,.gif,.mp4,.mp3';
            
            input.onchange = function(event) {
                const files = event.target.files;
                for (let file of files) {
                    addResource(file.name, getFileType(file.name));
                }
                showNotification(`${files.length} file(s) uploaded successfully!`, 'success');
            };
            
            input.click();
        }

        function addResource(name, type) {
            const resourcesList = document.querySelector('.resources-list');
            const resourceDiv = document.createElement('div');
            resourceDiv.className = 'resource-item';
            resourceDiv.innerHTML = `
                <div class="resource-icon">
                    <i class="fas ${getFileIcon(type)}"></i>
                </div>
                <div class="resource-info">
                    <div class="resource-name">${name}</div>
                    <div class="resource-type">${type}</div>
                </div>
            `;
            resourcesList.appendChild(resourceDiv);
        }

        function getFileType(filename) {
            const ext = filename.split('.').pop().toLowerCase();
            const types = {
                'pdf': 'PDF Document',
                'doc': 'Word Document',
                'docx': 'Word Document',
                'ppt': 'PowerPoint Presentation',
                'pptx': 'PowerPoint Presentation',
                'xls': 'Excel Spreadsheet',
                'xlsx': 'Excel Spreadsheet',
                'jpg': 'Image',
                'jpeg': 'Image',
                'png': 'Image',
                'gif': 'Image',
                'mp4': 'Video',
                'mp3': 'Audio'
            };
            return types[ext] || 'Unknown';
        }

        function getFileIcon(type) {
            const icons = {
                'PDF Document': 'fa-file-pdf',
                'Word Document': 'fa-file-word',
                'PowerPoint Presentation': 'fa-file-powerpoint',
                'Excel Spreadsheet': 'fa-file-excel',
                'Image': 'fa-file-image',
                'Video': 'fa-file-video',
                'Audio': 'fa-file-audio'
            };
            return icons[type] || 'fa-file';
        }

        // Drag and drop functionality
        function initDragDrop() {
            const uploadArea = document.querySelector('.upload-area');
            
            uploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                this.classList.add('dragover');
            });
            
            uploadArea.addEventListener('dragleave', function(e) {
                e.preventDefault();
                this.classList.remove('dragover');
            });
            
            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                this.classList.remove('dragover');
                
                const files = e.dataTransfer.files;
                for (let file of files) {
                    addResource(file.name, getFileType(file.name));
                }
                showNotification(`${files.length} file(s) uploaded successfully!`, 'success');
            });
        }

        // Notification system
        function showNotification(message, type = 'success') {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification ${type} show`;
            
            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }

        // Simulate real-time updates
        setInterval(() => {
            if (isLectureActive) {
                // Simulate new questions
                if (Math.random() > 0.95) {
                    const questions = [
                        'Can you explain this concept again?',
                        'What about the practical applications?',
                        'How does this relate to the previous topic?',
                        'Are there any real-world examples?'
                    ];
                    const authors = ['Student A', 'Student B', 'Student C', 'Student D'];
                    const randomQuestion = questions[Math.floor(Math.random() * questions.length)];
                    const randomAuthor = authors[Math.floor(Math.random() * authors.length)];
                    addQuestion(randomAuthor, randomQuestion);
                }
            }
        }, 2000);

        // Handle window resize
        window.addEventListener('resize', function() {
            if (whiteboard) {
                const rect = whiteboard.getBoundingClientRect();
                whiteboard.width = rect.width;
                whiteboard.height = rect.height;
            }
        });
    </script>
</body>
</html>