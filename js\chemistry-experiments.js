// Chemistry Experiments Engine
// Comprehensive system for virtual chemistry experiments with real-time reactions

class ChemistryEngine {
    constructor() {
        this.experiments = new Map();
        this.compounds = new Map();
        this.reactions = new Map();
        this.safetyDatabase = new Map();
        
        this.initializeCompounds();
        this.initializeReactions();
        this.initializeSafetyData();
        this.initializeExperiments();
    }

    initializeCompounds() {
        const compounds = [
            {
                formula: 'HCl',
                name: 'Hydrochloric Acid',
                molarMass: 36.46,
                density: 1.18,
                boilingPoint: 110,
                color: 'colorless',
                hazards: ['corrosive', 'toxic']
            },
            {
                formula: 'NaOH',
                name: 'Sodium Hydroxide',
                molarMass: 40.00,
                density: 2.13,
                meltingPoint: 318,
                color: 'white',
                hazards: ['corrosive', 'caustic']
            },
            {
                formula: 'NaCl',
                name: 'Sodium Chloride',
                molarMass: 58.44,
                density: 2.16,
                meltingPoint: 801,
                color: 'white',
                hazards: []
            },
            {
                formula: 'H2O',
                name: 'Water',
                molarMass: 18.02,
                density: 1.00,
                boilingPoint: 100,
                color: 'colorless',
                hazards: []
            },
            {
                formula: 'C7H6O3',
                name: 'Salicylic Acid',
                molarMass: 138.12,
                density: 1.44,
                meltingPoint: 159,
                color: 'white',
                hazards: ['irritant']
            },
            {
                formula: 'C9H8O4',
                name: 'Aspirin',
                molarMass: 180.16,
                density: 1.40,
                meltingPoint: 135,
                color: 'white',
                hazards: []
            }
        ];

        compounds.forEach(compound => {
            this.compounds.set(compound.formula, compound);
        });
    }

    initializeReactions() {
        const reactions = [
            {
                id: 'acid_base_neutralization',
                reactants: ['HCl', 'NaOH'],
                products: ['NaCl', 'H2O'],
                stoichiometry: [1, 1, 1, 1],
                enthalpy: -57.3, // kJ/mol
                type: 'neutralization',
                rate: 'fast'
            },
            {
                id: 'aspirin_synthesis',
                reactants: ['C7H6O3', 'C4H6O3'],
                products: ['C9H8O4', 'CH3COOH'],
                stoichiometry: [1, 1, 1, 1],
                enthalpy: -25.0,
                type: 'synthesis',
                rate: 'slow',
                catalyst: 'H2SO4',
                temperature: 80
            },
            {
                id: 'calcium_oxide_hydration',
                reactants: ['CaO', 'H2O'],
                products: ['Ca(OH)2'],
                stoichiometry: [1, 1, 1],
                enthalpy: -65.2,
                type: 'hydration',
                rate: 'fast'
            }
        ];

        reactions.forEach(reaction => {
            this.reactions.set(reaction.id, reaction);
        });
    }

    initializeSafetyData() {
        const safetyData = [
            {
                compound: 'HCl',
                hazards: ['corrosive', 'toxic'],
                ppe: ['safety_goggles', 'gloves', 'lab_coat'],
                firstAid: 'Flush with water for 15 minutes',
                disposal: 'Neutralize before disposal'
            },
            {
                compound: 'NaOH',
                hazards: ['corrosive', 'caustic'],
                ppe: ['safety_goggles', 'gloves', 'lab_coat'],
                firstAid: 'Flush with water immediately',
                disposal: 'Neutralize before disposal'
            }
        ];

        safetyData.forEach(data => {
            this.safetyDatabase.set(data.compound, data);
        });
    }

    initializeExperiments() {
        // Titration Experiment
        this.experiments.set('titration', new TitrationExperiment());
        
        // Calorimetry Experiment
        this.experiments.set('calorimetry', new CalorimetryExperiment());
        
        // Synthesis Experiment
        this.experiments.set('synthesis', new SynthesisExperiment());
        
        // Kinetics Experiment
        this.experiments.set('kinetics', new KineticsExperiment());
        
        // Equilibrium Experiment
        this.experiments.set('equilibrium', new EquilibriumExperiment());
    }

    getExperiment(type) {
        return this.experiments.get(type);
    }

    getCompound(formula) {
        return this.compounds.get(formula);
    }

    getReaction(id) {
        return this.reactions.get(id);
    }

    getSafetyInfo(compound) {
        return this.safetyDatabase.get(compound);
    }
}

// Base Experiment Class
class BaseChemistryExperiment {
    constructor(name, type) {
        this.name = name;
        this.type = type;
        this.isRunning = false;
        this.data = [];
        this.parameters = {};
        this.results = {};
        this.startTime = 0;
    }

    start(parameters = {}) {
        this.parameters = { ...this.parameters, ...parameters };
        this.isRunning = true;
        this.startTime = Date.now();
        this.data = [];
        this.onStart();
    }

    stop() {
        this.isRunning = false;
        this.onStop();
    }

    update() {
        if (!this.isRunning) return;
        this.onUpdate();
    }

    logData(timestamp, values) {
        this.data.push({
            timestamp: timestamp || Date.now(),
            relativeTime: (timestamp || Date.now()) - this.startTime,
            values: { ...values }
        });
    }

    onStart() {
        // Override in subclasses
    }

    onStop() {
        // Override in subclasses
    }

    onUpdate() {
        // Override in subclasses
    }

    calculateResults() {
        // Override in subclasses
        return {};
    }
}

// Titration Experiment
class TitrationExperiment extends BaseChemistryExperiment {
    constructor() {
        super('Acid-Base Titration', 'analytical');
        this.parameters = {
            titrantConcentration: 0.1, // M
            sampleVolume: 25.0, // mL
            unknownConcentration: 0.08, // M (to be determined)
            currentVolume: 0.0, // mL of titrant added
            pH: 1.0
        };
        this.equivalencePoint = false;
        this.endPoint = false;
    }

    onStart() {
        this.calculateInitialPH();
        this.equivalencePoint = false;
        this.endPoint = false;
        this.parameters.currentVolume = 0.0;
    }

    addTitrant(volume = 0.1) {
        if (!this.isRunning) return;
        
        this.parameters.currentVolume += volume;
        this.calculatePH();
        this.checkEndpoint();
        
        this.logData(Date.now(), {
            volume: this.parameters.currentVolume,
            pH: this.parameters.pH,
            equivalencePoint: this.equivalencePoint
        });

        return {
            volume: this.parameters.currentVolume,
            pH: this.parameters.pH,
            color: this.getIndicatorColor(),
            equivalencePoint: this.equivalencePoint
        };
    }

    calculateInitialPH() {
        // pH = -log[H+] for strong acid
        const concentration = this.parameters.unknownConcentration;
        this.parameters.pH = -Math.log10(concentration);
    }

    calculatePH() {
        const Va = this.parameters.sampleVolume; // mL of acid
        const Ca = this.parameters.unknownConcentration; // M
        const Vb = this.parameters.currentVolume; // mL of base added
        const Cb = this.parameters.titrantConcentration; // M

        const molesAcid = (Va / 1000) * Ca;
        const molesBase = (Vb / 1000) * Cb;
        const totalVolume = (Va + Vb) / 1000; // L

        if (molesBase < molesAcid) {
            // Excess acid
            const excessAcid = molesAcid - molesBase;
            const concentration = excessAcid / totalVolume;
            this.parameters.pH = -Math.log10(concentration);
        } else if (molesBase > molesAcid) {
            // Excess base
            const excessBase = molesBase - molesAcid;
            const concentration = excessBase / totalVolume;
            const pOH = -Math.log10(concentration);
            this.parameters.pH = 14 - pOH;
        } else {
            // Equivalence point
            this.parameters.pH = 7.0;
            this.equivalencePoint = true;
        }
    }

    checkEndpoint() {
        // Phenolphthalein endpoint around pH 8.2
        if (this.parameters.pH >= 8.2 && !this.endPoint) {
            this.endPoint = true;
        }
    }

    getIndicatorColor() {
        if (this.parameters.pH < 8.2) {
            return 'colorless';
        } else {
            return 'pink';
        }
    }

    calculateResults() {
        const equivalenceVolume = this.getEquivalenceVolume();
        const calculatedConcentration = this.calculateConcentration(equivalenceVolume);
        
        return {
            equivalenceVolume: equivalenceVolume,
            calculatedConcentration: calculatedConcentration,
            actualConcentration: this.parameters.unknownConcentration,
            percentError: Math.abs((calculatedConcentration - this.parameters.unknownConcentration) / this.parameters.unknownConcentration) * 100
        };
    }

    getEquivalenceVolume() {
        // Find the volume at equivalence point from data
        const equivalenceData = this.data.find(point => point.values.equivalencePoint);
        return equivalenceData ? equivalenceData.values.volume : this.parameters.currentVolume;
    }

    calculateConcentration(volume) {
        // Ca = (Cb * Vb) / Va
        return (this.parameters.titrantConcentration * volume) / this.parameters.sampleVolume;
    }
}

// Calorimetry Experiment
class CalorimetryExperiment extends BaseChemistryExperiment {
    constructor() {
        super('Calorimetry', 'thermodynamics');
        this.parameters = {
            initialTemp: 25.0, // °C
            currentTemp: 25.0,
            mass: 1.0, // g
            specificHeat: 4.18, // J/g°C (water)
            heatCapacity: 100, // J/°C (calorimeter)
            reactionEnthalpy: -65.2 // kJ/mol (CaO + H2O)
        };
        this.reactionStarted = false;
    }

    onStart() {
        this.parameters.currentTemp = this.parameters.initialTemp;
        this.reactionStarted = false;
    }

    startReaction() {
        if (!this.isRunning || this.reactionStarted) return;
        
        this.reactionStarted = true;
        this.simulateHeatRelease();
    }

    simulateHeatRelease() {
        const moles = this.parameters.mass / 56.08; // CaO molar mass
        const heatReleased = Math.abs(this.parameters.reactionEnthalpy * 1000 * moles); // J
        
        // q = mcΔT + CΔT
        const waterMass = 100; // g (assume 100g water)
        const totalHeatCapacity = (waterMass * this.parameters.specificHeat) + this.parameters.heatCapacity;
        const tempIncrease = heatReleased / totalHeatCapacity;
        
        this.animateTemperatureChange(tempIncrease);
    }

    animateTemperatureChange(totalIncrease) {
        const steps = 50;
        const increment = totalIncrease / steps;
        const interval = 100; // ms
        
        let step = 0;
        const animate = () => {
            if (step < steps && this.isRunning) {
                this.parameters.currentTemp += increment;
                
                this.logData(Date.now(), {
                    temperature: this.parameters.currentTemp,
                    heatReleased: (step / steps) * totalIncrease * 
                                 ((100 * this.parameters.specificHeat) + this.parameters.heatCapacity)
                });
                
                step++;
                setTimeout(animate, interval);
            }
        };
        
        animate();
    }

    calculateResults() {
        const maxTemp = Math.max(...this.data.map(point => point.values.temperature));
        const tempChange = maxTemp - this.parameters.initialTemp;
        const heatReleased = tempChange * ((100 * this.parameters.specificHeat) + this.parameters.heatCapacity);
        const moles = this.parameters.mass / 56.08;
        const calculatedEnthalpy = -heatReleased / (1000 * moles); // kJ/mol
        
        return {
            maxTemperature: maxTemp,
            temperatureChange: tempChange,
            heatReleased: heatReleased,
            calculatedEnthalpy: calculatedEnthalpy,
            theoreticalEnthalpy: this.parameters.reactionEnthalpy,
            percentError: Math.abs((calculatedEnthalpy - this.parameters.reactionEnthalpy) / this.parameters.reactionEnthalpy) * 100
        };
    }
}

// Synthesis Experiment
class SynthesisExperiment extends BaseChemistryExperiment {
    constructor() {
        super('Aspirin Synthesis', 'organic');
        this.parameters = {
            salicylicAcidMass: 2.0, // g
            aceticAnhydrideVolume: 5.0, // mL
            temperature: 80, // °C
            reactionTime: 30, // minutes
            currentTime: 0,
            yield: 0
        };
        this.reactionComplete = false;
    }

    onStart() {
        this.parameters.currentTime = 0;
        this.parameters.yield = 0;
        this.reactionComplete = false;
        this.simulateReaction();
    }

    simulateReaction() {
        const totalTime = this.parameters.reactionTime * 60 * 1000; // ms
        const interval = 1000; // Update every second
        
        const updateReaction = () => {
            if (this.parameters.currentTime < totalTime && this.isRunning) {
                this.parameters.currentTime += interval;
                
                // Calculate yield based on time and temperature
                const progress = this.parameters.currentTime / totalTime;
                const tempFactor = Math.min(this.parameters.temperature / 80, 1.2);
                this.parameters.yield = Math.min(progress * tempFactor * 85, 85); // Max 85% yield
                
                this.logData(Date.now(), {
                    time: this.parameters.currentTime / 1000,
                    temperature: this.parameters.temperature,
                    yield: this.parameters.yield
                });
                
                setTimeout(updateReaction, interval);
            } else {
                this.reactionComplete = true;
            }
        };
        
        updateReaction();
    }

    heatMixture(temperature) {
        this.parameters.temperature = temperature;
    }

    calculateResults() {
        const theoreticalYield = this.calculateTheoreticalYield();
        const actualYield = (this.parameters.yield / 100) * theoreticalYield;
        
        return {
            theoreticalYield: theoreticalYield,
            actualYield: actualYield,
            percentYield: this.parameters.yield,
            reactionTime: this.parameters.currentTime / 1000,
            finalTemperature: this.parameters.temperature
        };
    }

    calculateTheoreticalYield() {
        // Calculate based on limiting reagent
        const salicylicMoles = this.parameters.salicylicAcidMass / 138.12; // mol
        const aspirinMolarMass = 180.16; // g/mol
        return salicylicMoles * aspirinMolarMass; // g
    }
}

// Kinetics Experiment
class KineticsExperiment extends BaseChemistryExperiment {
    constructor() {
        super('Chemical Kinetics', 'kinetics');
        this.parameters = {
            initialConcentration: 1.0, // M
            currentConcentration: 1.0,
            temperature: 25, // °C
            rateConstant: 0.1, // s^-1
            order: 1
        };
    }

    onStart() {
        this.parameters.currentConcentration = this.parameters.initialConcentration;
        this.simulateReaction();
    }

    simulateReaction() {
        const interval = 1000; // ms
        
        const updateConcentration = () => {
            if (this.isRunning && this.parameters.currentConcentration > 0.01) {
                const dt = interval / 1000; // s
                const rate = this.calculateRate();
                this.parameters.currentConcentration -= rate * dt;
                
                this.logData(Date.now(), {
                    concentration: this.parameters.currentConcentration,
                    rate: rate,
                    temperature: this.parameters.temperature
                });
                
                setTimeout(updateConcentration, interval);
            }
        };
        
        updateConcentration();
    }

    calculateRate() {
        // Rate = k[A]^n
        return this.parameters.rateConstant * 
               Math.pow(this.parameters.currentConcentration, this.parameters.order);
    }

    changeTemperature(newTemp) {
        const oldTemp = this.parameters.temperature;
        this.parameters.temperature = newTemp;
        
        // Arrhenius equation approximation
        const tempRatio = (newTemp + 273.15) / (oldTemp + 273.15);
        this.parameters.rateConstant *= Math.pow(2, (newTemp - oldTemp) / 10);
    }

    calculateResults() {
        const halfLife = this.calculateHalfLife();
        
        return {
            rateConstant: this.parameters.rateConstant,
            reactionOrder: this.parameters.order,
            halfLife: halfLife,
            finalConcentration: this.parameters.currentConcentration
        };
    }

    calculateHalfLife() {
        if (this.parameters.order === 1) {
            return Math.log(2) / this.parameters.rateConstant;
        } else if (this.parameters.order === 0) {
            return this.parameters.initialConcentration / (2 * this.parameters.rateConstant);
        } else {
            return 1 / ((this.parameters.order - 1) * this.parameters.rateConstant * 
                       Math.pow(this.parameters.initialConcentration, this.parameters.order - 1));
        }
    }
}

// Equilibrium Experiment
class EquilibriumExperiment extends BaseChemistryExperiment {
    constructor() {
        super('Chemical Equilibrium', 'equilibrium');
        this.parameters = {
            temperature: 25, // °C
            pressure: 1.0, // atm
            concentrations: {
                N2: 1.0,
                H2: 3.0,
                NH3: 0.0
            },
            equilibriumConstant: 0.5
        };
    }

    onStart() {
        this.simulateEquilibrium();
    }

    simulateEquilibrium() {
        const interval = 500; // ms
        let time = 0;
        
        const updateEquilibrium = () => {
            if (this.isRunning && time < 60000) { // Run for 1 minute
                this.calculateEquilibriumShift();
                
                this.logData(Date.now(), {
                    time: time / 1000,
                    N2: this.parameters.concentrations.N2,
                    H2: this.parameters.concentrations.H2,
                    NH3: this.parameters.concentrations.NH3,
                    temperature: this.parameters.temperature,
                    pressure: this.parameters.pressure
                });
                
                time += interval;
                setTimeout(updateEquilibrium, interval);
            }
        };
        
        updateEquilibrium();
    }

    calculateEquilibriumShift() {
        // Simplified equilibrium calculation
        const Q = this.calculateReactionQuotient();
        const K = this.parameters.equilibriumConstant;
        
        if (Q < K) {
            // Shift right (forward reaction)
            const shift = 0.01;
            this.parameters.concentrations.N2 -= shift;
            this.parameters.concentrations.H2 -= 3 * shift;
            this.parameters.concentrations.NH3 += 2 * shift;
        } else if (Q > K) {
            // Shift left (reverse reaction)
            const shift = 0.01;
            this.parameters.concentrations.N2 += shift;
            this.parameters.concentrations.H2 += 3 * shift;
            this.parameters.concentrations.NH3 -= 2 * shift;
        }
        
        // Ensure concentrations don't go negative
        Object.keys(this.parameters.concentrations).forEach(key => {
            this.parameters.concentrations[key] = Math.max(0, this.parameters.concentrations[key]);
        });
    }

    calculateReactionQuotient() {
        const { N2, H2, NH3 } = this.parameters.concentrations;
        return (NH3 * NH3) / (N2 * H2 * H2 * H2);
    }

    changeTemperature(newTemp) {
        this.parameters.temperature = newTemp;
        // Le Chatelier's principle: increase temp favors endothermic direction
        // For N2 + 3H2 ⇌ 2NH3 (exothermic), higher temp decreases K
        const tempFactor = Math.exp((25 - newTemp) / 100);
        this.parameters.equilibriumConstant *= tempFactor;
    }

    changePressure(newPressure) {
        this.parameters.pressure = newPressure;
        // Higher pressure favors side with fewer gas molecules (NH3)
        const pressureFactor = newPressure / 1.0;
        Object.keys(this.parameters.concentrations).forEach(key => {
            this.parameters.concentrations[key] *= pressureFactor;
        });
    }

    calculateResults() {
        return {
            equilibriumConstant: this.parameters.equilibriumConstant,
            finalConcentrations: { ...this.parameters.concentrations },
            reactionQuotient: this.calculateReactionQuotient()
        };
    }
}

// Global chemistry engine instance
let globalChemistryEngine;

document.addEventListener('DOMContentLoaded', function() {
    globalChemistryEngine = new ChemistryEngine();
});
