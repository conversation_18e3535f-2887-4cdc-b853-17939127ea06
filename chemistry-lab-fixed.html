<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مختبر الكيمياء التفاعلي - Chemistry Lab | SimLab HUB</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: '<PERSON><PERSON>wal', Arial, sans-serif;
            background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
            color: #e2e8f0;
            overflow-x: hidden;
        }

        .lab-interface {
            display: grid;
            grid-template-columns: 280px 1fr 320px;
            grid-template-rows: 70px 1fr 90px;
            height: 100vh;
            gap: 2px;
        }

        .header-bar {
            grid-column: 1 / -1;
            background: rgba(26, 32, 44, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid #4a5568;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 25px;
        }

        .lab-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: #90cdf4;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .safety-indicator {
            display: flex;
            align-items: center;
            gap: 10px;
            background: rgba(72, 187, 120, 0.2);
            padding: 8px 15px;
            border-radius: 20px;
            border: 1px solid #48bb78;
        }

        .safety-light {
            width: 10px;
            height: 10px;
            background: #48bb78;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .header-controls {
            display: flex;
            gap: 10px;
        }

        .header-btn {
            background: linear-gradient(135deg, #4299e1, #3182ce);
            color: white;
            border: none;
            padding: 10px 18px;
            border-radius: 20px;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .header-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(66, 153, 225, 0.3);
        }

        .left-panel {
            background: rgba(26, 32, 44, 0.95);
            backdrop-filter: blur(10px);
            border-left: 1px solid #4a5568;
            padding: 20px;
            overflow-y: auto;
        }

        .right-panel {
            background: rgba(26, 32, 44, 0.95);
            backdrop-filter: blur(10px);
            border-right: 1px solid #4a5568;
            padding: 20px;
            overflow-y: auto;
        }

        .lab-workspace {
            background: linear-gradient(135deg, #2d3748, #4a5568);
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .workspace-toolbar {
            background: rgba(45, 55, 72, 0.9);
            padding: 15px;
            border-bottom: 1px solid #4a5568;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .lab-bench {
            flex: 1;
            background: linear-gradient(135deg, #edf2f7, #e2e8f0);
            position: relative;
            overflow: hidden;
            background-image: 
                radial-gradient(circle at 25% 25%, rgba(102, 126, 234, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(118, 75, 162, 0.1) 0%, transparent 50%);
        }

        .footer-bar {
            grid-column: 1 / -1;
            background: rgba(26, 32, 44, 0.95);
            backdrop-filter: blur(10px);
            border-top: 1px solid #4a5568;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 25px;
        }

        .panel-section {
            margin-bottom: 25px;
        }

        .panel-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #90cdf4;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
            padding-bottom: 8px;
            border-bottom: 1px solid #4a5568;
        }

        .experiment-selector {
            background: rgba(45, 55, 72, 0.8);
            border: 1px solid #4a5568;
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .experiment-option {
            display: block;
            width: 100%;
            background: transparent;
            border: 1px solid #4a5568;
            border-radius: 8px;
            padding: 12px;
            color: #e2e8f0;
            text-align: right;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .experiment-option:hover {
            border-color: #4299e1;
            background: rgba(66, 153, 225, 0.1);
        }

        .experiment-option.active {
            background: rgba(66, 153, 225, 0.2);
            border-color: #4299e1;
            box-shadow: 0 0 0 2px rgba(66, 153, 225, 0.2);
        }

        .virtual-instruments {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            margin-bottom: 20px;
        }

        .instrument {
            background: rgba(45, 55, 72, 0.9);
            border: 1px solid #4a5568;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .instrument:hover {
            border-color: #90cdf4;
            box-shadow: 0 4px 15px rgba(144, 205, 244, 0.2);
        }

        .instrument.active {
            border-color: #90cdf4;
            background: rgba(144, 205, 244, 0.1);
        }

        .instrument-icon {
            font-size: 2rem;
            color: #90cdf4;
            margin-bottom: 8px;
        }

        .instrument-name {
            font-size: 0.8rem;
            color: #a0aec0;
        }

        .variable-controls {
            background: rgba(45, 55, 72, 0.8);
            border: 1px solid #4a5568;
            border-radius: 12px;
            padding: 15px;
        }

        .control-group {
            margin-bottom: 15px;
        }

        .control-label {
            display: block;
            font-size: 0.9rem;
            color: #a0aec0;
            margin-bottom: 5px;
        }

        .control-slider {
            width: 100%;
            height: 6px;
            border-radius: 3px;
            background: #4a5568;
            outline: none;
            transition: all 0.3s ease;
        }

        .control-slider::-webkit-slider-thumb {
            appearance: none;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: #4299e1;
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(66, 153, 225, 0.3);
        }

        .control-input {
            width: 100%;
            background: rgba(45, 55, 72, 0.8);
            border: 1px solid #4a5568;
            border-radius: 6px;
            padding: 8px 10px;
            color: #e2e8f0;
            font-size: 0.9rem;
        }

        .control-button {
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
            border: none;
            padding: 10px 18px;
            border-radius: 8px;
            font-size: 0.9rem;
            cursor: pointer;
            width: 100%;
            margin-bottom: 10px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .control-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(72, 187, 120, 0.3);
        }

        .control-button.danger {
            background: linear-gradient(135deg, #f56565, #e53e3e);
        }

        .control-button.warning {
            background: linear-gradient(135deg, #ed8936, #dd6b20);
        }

        .data-display {
            background: rgba(45, 55, 72, 0.9);
            border: 1px solid #4a5568;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .data-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 0.9rem;
        }

        .data-label {
            color: #a0aec0;
        }

        .data-value {
            color: #90cdf4;
            font-weight: 600;
        }

        .graph-container {
            background: rgba(45, 55, 72, 0.9);
            border: 1px solid #4a5568;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            height: 250px;
            position: relative;
        }

        .graph-title {
            color: #90cdf4;
            font-size: 0.9rem;
            margin-bottom: 15px;
            text-align: center;
        }

        .graph-canvas {
            width: 100%;
            height: calc(100% - 30px);
            background: rgba(26, 32, 44, 0.5);
            border-radius: 6px;
            position: relative;
        }

        /* Chemical Equipment Styles */
        .beaker {
            width: 60px;
            height: 80px;
            background: linear-gradient(180deg, transparent 0%, rgba(66, 153, 225, 0.3) 100%);
            border: 3px solid #4a5568;
            border-radius: 0 0 30px 30px;
            position: absolute;
            display: flex;
            align-items: flex-end;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .beaker:hover {
            border-color: #90cdf4;
            box-shadow: 0 0 20px rgba(144, 205, 244, 0.3);
        }

        .beaker-solution {
            width: 90%;
            height: 60%;
            background: var(--solution-color, #4299e1);
            border-radius: 0 0 25px 25px;
            animation: bubbling 2s ease-in-out infinite;
        }

        @keyframes bubbling {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.02); }
        }

        .burette {
            width: 20px;
            height: 200px;
            background: linear-gradient(180deg, 
                transparent 0%, 
                rgba(246, 173, 85, 0.7) var(--fill-level, 50%), 
                transparent var(--fill-level, 50%));
            border: 2px solid #4a5568;
            border-radius: 10px;
            position: absolute;
            cursor: pointer;
        }

        .thermometer {
            width: 15px;
            height: 120px;
            background: linear-gradient(180deg, 
                #f56565 0%, 
                #f56565 var(--temp-level, 25%), 
                #e2e8f0 var(--temp-level, 25%), 
                #e2e8f0 100%);
            border: 2px solid #4a5568;
            border-radius: 8px;
            position: absolute;
            cursor: pointer;
        }

        .ph-meter {
            width: 80px;
            height: 40px;
            background: rgba(45, 55, 72, 0.9);
            border: 2px solid #4a5568;
            border-radius: 8px;
            position: absolute;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #90cdf4;
            font-weight: bold;
            cursor: pointer;
        }

        .molecular-model {
            position: absolute;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .atom {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            position: absolute;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.7rem;
            font-weight: bold;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
        }

        .atom.carbon { background: #2d3748; color: white; }
        .atom.hydrogen { background: #f7fafc; color: #2d3748; }
        .atom.oxygen { background: #f56565; color: white; }
        .atom.nitrogen { background: #4299e1; color: white; }

        .bond {
            position: absolute;
            height: 2px;
            background: #4a5568;
            transform-origin: left center;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9rem;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .status-dot.safe { background: #48bb78; }
        .status-dot.warning { background: #ed8936; }
        .status-dot.danger { background: #f56565; }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(74, 85, 104, 0.5);
            border-radius: 3px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #48bb78, #38a169);
            border-radius: 3px;
            transition: width 0.3s ease;
        }

        .safety-alert {
            background: rgba(245, 101, 101, 0.1);
            border: 1px solid #f56565;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 15px;
            color: #fed7d7;
        }

        .safety-alert.warning {
            background: rgba(237, 137, 54, 0.1);
            border-color: #ed8936;
            color: #fbd38d;
        }

        .safety-alert.info {
            background: rgba(66, 153, 225, 0.1);
            border-color: #4299e1;
            color: #bee3f8;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.1); }
        }

        @keyframes reaction-flash {
            0%, 100% { background: rgba(66, 153, 225, 0.1); }
            50% { background: rgba(246, 173, 85, 0.3); }
        }

        .reacting {
            animation: reaction-flash 0.5s ease-in-out 3;
        }

        @media (max-width: 1024px) {
            .lab-interface {
                grid-template-columns: 1fr;
                grid-template-rows: 70px 200px 1fr 90px;
            }
            
            .left-panel, .right-panel {
                border: none;
                border-bottom: 1px solid #4a5568;
                overflow-x: auto;
                white-space: nowrap;
            }
        }

        /* Virtual Instrument Overlays */
        .instrument-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .instrument-window {
            background: rgba(26, 32, 44, 0.95);
            border: 1px solid #4a5568;
            border-radius: 15px;
            padding: 25px;
            max-width: 500px;
            width: 90%;
            backdrop-filter: blur(10px);
        }

        .instrument-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .instrument-close {
            background: #f56565;
            border: none;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .spectrophotometer-display {
            background: rgba(45, 55, 72, 0.9);
            border: 1px solid #4a5568;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
        }

        .wavelength-selector {
            margin: 15px 0;
        }

        .absorbance-reading {
            font-size: 2rem;
            font-weight: bold;
            color: #90cdf4;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <!-- Virtual Instrument Overlays -->
    <div class="instrument-overlay" id="spectrophotometerOverlay">
        <div class="instrument-window">
            <div class="instrument-header">
                <h3 style="color: #90cdf4;">مطياف الضوء - Spectrophotometer</h3>
                <button class="instrument-close" onclick="closeInstrument('spectrophotometer')">✕</button>
            </div>
            <div class="spectrophotometer-display">
                <div class="wavelength-selector">
                    <label class="control-label">طول الموجة (نانومتر)</label>
                    <input type="range" class="control-slider" id="wavelengthSlider" 
                           min="400" max="700" value="550" oninput="updateSpectrophotometer()">
                    <div style="color: #a0aec0; margin-top: 5px;"><span id="wavelengthValue">550</span> nm</div>
                </div>
                <div class="absorbance-reading" id="absorbanceReading">0.000</div>
                <div style="color: #a0aec0;">معامل الامتصاص</div>
                <button class="control-button" onclick="takeSpectroReading()">
                    <i class="fas fa-eye"></i> أخذ قراءة
                </button>
            </div>
        </div>
    </div>

    <div class="instrument-overlay" id="phmeterOverlay">
        <div class="instrument-window">
            <div class="instrument-header">
                <h3 style="color: #90cdf4;">مقياس الحموضة - pH Meter</h3>
                <button class="instrument-close" onclick="closeInstrument('phmeter')">✕</button>
            </div>
            <div class="spectrophotometer-display">
                <div class="absorbance-reading" id="phReading">7.00</div>
                <div style="color: #a0aec0; margin-bottom: 20px;">درجة الحموضة pH</div>
                <div style="background: rgba(66, 153, 225, 0.1); padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                    <div style="font-size: 0.9rem; color: #a0aec0;">نوع المحلول:</div>
                    <div style="color: #90cdf4; font-weight: 600;" id="solutionType">متعادل</div>
                </div>
                <button class="control-button" onclick="takePHReading()">
                    <i class="fas fa-thermometer-half"></i> قياس pH
                </button>
            </div>
        </div>
    </div>

    <div class="lab-interface">
        <!-- Header Bar -->
        <div class="header-bar">
            <div class="lab-title">
                <i class="fas fa-flask"></i>
                <span>مختبر الكيمياء التفاعلي</span>
            </div>
            
            <div class="safety-indicator">
                <div class="safety-light"></div>
                <span>بيئة آمنة</span>
            </div>

            <div class="header-controls">
                <button class="header-btn" onclick="saveExperiment()">
                    <i class="fas fa-save"></i> حفظ
                </button>
                <button class="header-btn" onclick="exportData()">
                    <i class="fas fa-download"></i> تصدير
                </button>
                <button class="header-btn" onclick="resetLab()">
                    <i class="fas fa-redo"></i> إعادة تعيين
                </button>
                <button class="header-btn" onclick="showSafetyProtocol()">
                    <i class="fas fa-shield-alt"></i> السلامة
                </button>
                <button class="header-btn" onclick="goBack()">
                    <i class="fas fa-arrow-right"></i> العودة
                </button>
            </div>
        </div>

        <!-- Left Panel - Experiments & Instruments -->
        <div class="left-panel">
            <div class="experiment-selector">
                <div class="panel-title">
                    <i class="fas fa-list"></i>
                    التجارب المتاحة
                </div>
                
                <button class="experiment-option active" onclick="selectExperiment('titration')">
                    معايرة حمض-قاعدة
                </button>
                <button class="experiment-option" onclick="selectExperiment('calorimetry')">
                    قياس السعرات الحرارية
                </button>
                <button class="experiment-option" onclick="selectExperiment('synthesis')">
                    تصنيع الأسبرين
                </button>
                <button class="experiment-option" onclick="selectExperiment('kinetics')">
                    حركية التفاعل
                </button>
                <button class="experiment-option" onclick="selectExperiment('equilibrium')">
                    الاتزان الكيميائي
                </button>
                <button class="experiment-option" onclick="selectExperiment('gas-laws')">
                    قوانين الغازات
                </button>
                <button class="experiment-option" onclick="selectExperiment('stoichiometry')">
                    الحسابات الكيميائية
                </button>
                <button class="experiment-option" onclick="selectExperiment('redox')">
                    تفاعلات الأكسدة والاختزال
                </button>
                <button class="experiment-option" onclick="selectExperiment('electrochemistry')">
                    الكيمياء الكهربية
                </button>
                <button class="experiment-option" onclick="selectExperiment('spectrophotometry')">
                    طيف الضوء
                </button>
                <button class="experiment-option" onclick="selectExperiment('qualitative')">
                    التحليل النوعي
                </button>
                <button class="experiment-option" onclick="selectExperiment('molecular-geometry')">
                    الهندسة الجزيئية
                </button>
                <button class="experiment-option" onclick="selectExperiment('acids-bases')">
                    الأحماض والقواعد
                </button>
                <button class="experiment-option" onclick="selectExperiment('solutions')">
                    تحضير المحاليل
                </button>
                <button class="experiment-option" onclick="selectExperiment('safety')">
                    سيناريوهات السلامة
                </button>
            </div>

            <div class="panel-section">
                <div class="panel-title">
                    <i class="fas fa-tools"></i>
                    الأدوات الافتراضية
                </div>
                <div class="virtual-instruments">
                    <div class="instrument" onclick="openInstrument('thermometer')">
                        <div class="instrument-icon">
                            <i class="fas fa-thermometer-half"></i>
                        </div>
                        <div class="instrument-name">مقياس الحرارة</div>
                    </div>
                    
                    <div class="instrument" onclick="openInstrument('phmeter')">
                        <div class="instrument-icon">
                            <i class="fas fa-tint"></i>
                        </div>
                        <div class="instrument-name">مقياس pH</div>
                    </div>
                    
                    <div class="instrument" onclick="openInstrument('balance')">
                        <div class="instrument-icon">
                            <i class="fas fa-balance-scale"></i>
                        </div>
                        <div class="instrument-name">الميزان</div>
                    </div>
                    
                    <div class="instrument" onclick="openInstrument('spectrophotometer')">
                        <div class="instrument-icon">
                            <i class="fas fa-eye"></i>
                        </div>
                        <div class="instrument-name">مطياف الضوء</div>
                    </div>
                    
                    <div class="instrument" onclick="openInstrument('calipers')">
                        <div class="instrument-icon">
                            <i class="fas fa-ruler"></i>
                        </div>
                        <div class="instrument-name">الكاليبر</div>
                    </div>
                    
                    <div class="instrument" onclick="openInstrument('voltmeter')">
                        <div class="instrument-icon">
                            <i class="fas fa-bolt"></i>
                        </div>
                        <div class="instrument-name">الفولتميتر</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Lab Workspace -->
        <div class="lab-workspace">
            <div class="workspace-toolbar">
                <div style="display: flex; gap: 15px; align-items: center;">
                    <span style="color: #a0aec0;">التجربة الحالية:</span>
                    <span style="color: #90cdf4; font-weight: 600;" id="currentExperiment">معايرة حمض-قاعدة</span>
                </div>
                
                <div style="display: flex; gap: 10px;">
                    <button class="control-button" style="width: auto; margin: 0;" onclick="startExperiment()">
                        <i class="fas fa-play"></i> تشغيل
                    </button>
                    <button class="control-button warning" style="width: auto; margin: 0;" onclick="pauseExperiment()">
                        <i class="fas fa-pause"></i> إيقاف
                    </button>
                    <button class="control-button danger" style="width: auto; margin: 0;" onclick="stopExperiment()">
                        <i class="fas fa-stop"></i> إنهاء
                    </button>
                </div>
            </div>
            
            <div class="lab-bench" id="labBench">
                <!-- Titration Setup -->
                <div id="titrationSetup" class="experiment-setup">
                    <!-- Burette -->
                    <div class="burette" style="top: 50px; right: 300px; --fill-level: 80%;" 
                         title="سحاحة - تحتوي على NaOH 0.1M">
                    </div>
                    
                    <!-- Beaker with indicator -->
                    <div class="beaker" style="bottom: 100px; right: 270px; --solution-color: #f56565;" 
                         title="دورق - يحتوي على HCl + مؤشر">
                        <div class="beaker-solution"></div>
                    </div>
                    
                    <!-- pH Meter -->
                    <div class="ph-meter" style="bottom: 200px; right: 200px;"
                         title="مقياس pH">
                        <span id="phDisplay">3.2</span>
                    </div>
                    
                    <!-- Thermometer -->
                    <div class="thermometer" style="bottom: 150px; right: 150px; --temp-level: 30%;"
                         title="مقياس الحرارة - 22°C">
                    </div>
                </div>

                <!-- Molecular Geometry Display -->
                <div id="molecularDisplay" class="experiment-setup" style="display: none;">
                    <div class="molecular-model" style="top: 50%; left: 50%; transform: translate(-50%, -50%);">
                        <!-- Water molecule example -->
                        <div class="atom oxygen" style="top: 0; left: 50px;">O</div>
                        <div class="atom hydrogen" style="top: 30px; left: 20px;">H</div>
                        <div class="atom hydrogen" style="top: 30px; left: 80px;">H</div>
                        <div class="bond" style="top: 15px; left: 40px; width: 25px; transform: rotate(-30deg);"></div>
                        <div class="bond" style="top: 15px; left: 65px; width: 25px; transform: rotate(30deg);"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Panel - Controls & Data -->
        <div class="right-panel">
            <div class="panel-section">
                <div class="panel-title">
                    <i class="fas fa-sliders-h"></i>
                    متغيرات التجربة
                </div>
                
                <div class="variable-controls" id="experimentControls">
                    <!-- Titration Controls -->
                    <div id="titrationControls" class="experiment-controls">
                        <div class="control-group">
                            <label class="control-label">تركيز المعاير (مول/لتر)</label>
                            <input type="range" class="control-slider" id="titrantConcentration" 
                                   min="0.01" max="0.5" value="0.1" step="0.01" oninput="updateTitration()">
                            <div style="color: #a0aec0; font-size: 0.8rem;" id="titrantValue">0.10 M</div>
                        </div>
                        
                        <div class="control-group">
                            <label class="control-label">حجم العينة (مل)</label>
                            <input type="range" class="control-slider" id="sampleVolume" 
                                   min="10" max="100" value="25" oninput="updateTitration()">
                            <div style="color: #a0aec0; font-size: 0.8rem;" id="volumeValue">25 مل</div>
                        </div>
                        
                        <div class="control-group">
                            <label class="control-label">سرعة الإضافة (مل/ثانية)</label>
                            <input type="range" class="control-slider" id="additionRate" 
                                   min="0.1" max="2" value="0.5" step="0.1" oninput="updateTitration()">
                            <div style="color: #a0aec0; font-size: 0.8rem;" id="rateValue">0.5 مل/ث</div>
                        </div>
                        
                        <button class="control-button" onclick="addTitrant()">
                            <i class="fas fa-plus"></i> إضافة معاير
                        </button>
                        
                        <button class="control-button warning" onclick="addIndicator()">
                            <i class="fas fa-tint"></i> إضافة مؤشر
                        </button>
                    </div>
                </div>
            </div>

            <div class="panel-section">
                <div class="panel-title">
                    <i class="fas fa-chart-line"></i>
                    البيانات الحية
                </div>
                
                <div class="data-display" id="liveData">
                    <div class="data-row">
                        <span class="data-label">الحجم المضاف:</span>
                        <span class="data-value" id="volumeAdded">0.0 مل</span>
                    </div>
                    <div class="data-row">
                        <span class="data-label">pH الحالي:</span>
                        <span class="data-value" id="currentPH">3.2</span>
                    </div>
                    <div class="data-row">
                        <span class="data-label">درجة الحرارة:</span>
                        <span class="data-value" id="temperature">22°C</span>
                    </div>
                    <div class="data-row">
                        <span class="data-label">نقطة التكافؤ:</span>
                        <span class="data-value" id="equivalencePoint">-- مل</span>
                    </div>
                    <div class="data-row">
                        <span class="data-label">التركيز المحسوب:</span>
                        <span class="data-value" id="calculatedConcentration">-- M</span>
                    </div>
                </div>
            </div>

            <div class="panel-section">
                <div class="panel-title">
                    <i class="fas fa-chart-area"></i>
                    منحنى المعايرة
                </div>
                
                <div class="graph-container">
                    <div class="graph-title">pH مقابل الحجم المضاف</div>
                    <div class="graph-canvas" id="titrationGraph">
                        <canvas id="phCurve" width="280" height="180"></canvas>
                    </div>
                </div>
            </div>

            <div class="panel-section">
                <div class="panel-title">
                    <i class="fas fa-exclamation-triangle"></i>
                    تنبيهات السلامة
                </div>
                
                <div class="safety-alert info" id="safetyAlert">
                    <i class="fas fa-info-circle"></i>
                    تأكد من ارتداء نظارات الأمان وقفازات المختبر
                </div>
                
                <div style="margin-top: 15px;">
                    <div style="color: #a0aec0; font-size: 0.8rem; margin-bottom: 5px;">تقدم التجربة</div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="experimentProgress" style="width: 0%;"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer Bar -->
        <div class="footer-bar">
            <div class="status-indicator">
                <div class="status-dot safe"></div>
                <span>النظام آمن - جاهز للتجريب</span>
            </div>
            
            <div style="display: flex; gap: 20px; align-items: center; font-size: 0.9rem; color: #a0aec0;">
                <span>درجة الحرارة: <span style="color: #90cdf4;">22°C</span></span>
                <span>الضغط: <span style="color: #90cdf4;">1 atm</span></span>
                <span>الرطوبة: <span style="color: #90cdf4;">45%</span></span>
                <span>الإصدار: 3.0.0</span>
            </div>
        </div>
    </div>

    <script src="js/chemistry-engine.js"></script>
    <script>
        // Global variables
        let currentExperiment = 'titration';
        let isExperimentRunning = false;
        let experimentData = [];
        let titrationData = { volumeAdded: 0, ph: 3.2, temperature: 22 };
        let animationFrameId = null;

        // Initialize chemistry lab
        function initializeChemistryLab() {
            setupTitrationCurve();
            updateTitrationDisplay();
        }

        function selectExperiment(experimentType) {
            // Update active experiment
            document.querySelectorAll('.experiment-option').forEach(btn => {
                btn.classList.remove('active');
            });
            
            event.target.classList.add('active');
            currentExperiment = experimentType;
            
            // Update experiment display
            const experimentNames = {
                'titration': 'معايرة حمض-قاعدة',
                'calorimetry': 'قياس السعرات الحرارية',
                'synthesis': 'تصنيع الأسبرين',
                'kinetics': 'حركية التفاعل',
                'equilibrium': 'الاتزان الكيميائي',
                'gas-laws': 'قوانين الغازات',
                'stoichiometry': 'الحسابات الكيميائية',
                'redox': 'تفاعلات الأكسدة والاختزال',
                'electrochemistry': 'الكيمياء الكهربية',
                'spectrophotometry': 'طيف الضوء',
                'qualitative': 'التحليل النوعي',
                'molecular-geometry': 'الهندسة الجزيئية',
                'acids-bases': 'الأحماض والقواعد',
                'solutions': 'تحضير المحاليل',
                'safety': 'سيناريوهات السلامة'
            };
            
            document.getElementById('currentExperiment').textContent = experimentNames[experimentType];
            
            // Show/hide experiment setups
            showExperimentSetup(experimentType);
            updateSafetyAlert(experimentType);
        }

        function showExperimentSetup(experimentType) {
            // Hide all setups
            document.querySelectorAll('.experiment-setup').forEach(setup => {
                setup.style.display = 'none';
            });
            
            // Show relevant setup
            if (experimentType === 'titration') {
                document.getElementById('titrationSetup').style.display = 'block';
            } else if (experimentType === 'molecular-geometry') {
                document.getElementById('molecularDisplay').style.display = 'block';
            }
            // Add more experiment setups as needed
        }

        function updateSafetyAlert(experimentType) {
            const safetyMessages = {
                'titration': 'تأكد من ارتداء نظارات الأمان وقفازات المختبر',
                'calorimetry': 'احذر من السطوح الساخنة والمواد المتفاعلة',
                'synthesis': 'استخدم غطاء المحرك وتجنب استنشاق الأبخرة',
                'electrochemistry': 'احذر من التيار الكهربي والمحاليل الموصلة',
                'safety': 'تعلم كيفية التعامل مع حالات الطوارئ'
            };
            
            const alertElement = document.getElementById('safetyAlert');
            alertElement.innerHTML = `<i class="fas fa-info-circle"></i> ${safetyMessages[experimentType] || 'اتبع بروتوكولات السلامة العامة'}`;
        }

        function updateTitration() {
            const concentration = document.getElementById('titrantConcentration').value;
            const volume = document.getElementById('sampleVolume').value;
            const rate = document.getElementById('additionRate').value;
            
            document.getElementById('titrantValue').textContent = parseFloat(concentration).toFixed(2) + ' M';
            document.getElementById('volumeValue').textContent = volume + ' مل';
            document.getElementById('rateValue').textContent = parseFloat(rate).toFixed(1) + ' مل/ث';
        }

        function startExperiment() {
            if (isExperimentRunning) return;
            
            isExperimentRunning = true;
            
            if (currentExperiment === 'titration') {
                startTitration();
            }
            
            updateProgress(0);
        }

        function startTitration() {
            // Reset titration data
            titrationData = { volumeAdded: 0, ph: 3.2, temperature: 22 };
            experimentData = [];
            
            // Start animation loop
            animateExperiment();
        }

        function animateExperiment() {
            if (!isExperimentRunning) return;
            
            if (currentExperiment === 'titration') {
                // Simulate titration progress
                const rate = parseFloat(document.getElementById('additionRate').value);
                titrationData.volumeAdded += rate * 0.1; // 100ms intervals
                
                // Calculate pH based on titration curve
                titrationData.ph = calculatePH(titrationData.volumeAdded);
                
                // Update display
                updateTitrationDisplay();
                updateTitrationCurve();
                
                // Check for equivalence point
                if (titrationData.volumeAdded >= 25) {
                    isExperimentRunning = false;
                    showCompletionMessage();
                    return;
                }
                
                updateProgress((titrationData.volumeAdded / 25) * 100);
            }
            
            animationFrameId = requestAnimationFrame(animateExperiment);
        }

        function calculatePH(volume) {
            // Simplified pH calculation for strong acid-strong base titration
            const initialAcidMoles = 0.1 * 0.025; // 0.1M HCl, 25mL
            const addedBaseMoles = 0.1 * (volume / 1000); // 0.1M NaOH
            
            if (addedBaseMoles < initialAcidMoles) {
                // Before equivalence point
                const excessAcid = initialAcidMoles - addedBaseMoles;
                const totalVolume = 0.025 + (volume / 1000);
                const concentration = excessAcid / totalVolume;
                return -Math.log10(concentration);
            } else if (addedBaseMoles > initialAcidMoles) {
                // After equivalence point
                const excessBase = addedBaseMoles - initialAcidMoles;
                const totalVolume = 0.025 + (volume / 1000);
                const concentration = excessBase / totalVolume;
                const pOH = -Math.log10(concentration);
                return 14 - pOH;
            } else {
                // At equivalence point
                return 7;
            }
        }

        function updateTitrationDisplay() {
            document.getElementById('volumeAdded').textContent = titrationData.volumeAdded.toFixed(1) + ' مل';
            document.getElementById('currentPH').textContent = titrationData.ph.toFixed(2);
            document.getElementById('phDisplay').textContent = titrationData.ph.toFixed(1);
            
            // Update beaker color based on pH
            const beaker = document.querySelector('.beaker');
            if (beaker) {
                if (titrationData.ph < 6) {
                    beaker.style.setProperty('--solution-color', '#f56565'); // Red (acidic)
                } else if (titrationData.ph > 8) {
                    beaker.style.setProperty('--solution-color', '#4299e1'); // Blue (basic)
                } else {
                    beaker.style.setProperty('--solution-color', '#48bb78'); // Green (neutral)
                }
            }
            
            // Add to experiment data
            experimentData.push({
                volume: titrationData.volumeAdded,
                ph: titrationData.ph,
                timestamp: Date.now()
            });
        }

        function setupTitrationCurve() {
            const canvas = document.getElementById('phCurve');
            const ctx = canvas.getContext('2d');
            
            // Clear canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // Draw axes
            ctx.strokeStyle = '#4a5568';
            ctx.lineWidth = 1;
            
            // X-axis
            ctx.beginPath();
            ctx.moveTo(30, canvas.height - 20);
            ctx.lineTo(canvas.width - 10, canvas.height - 20);
            ctx.stroke();
            
            // Y-axis
            ctx.beginPath();
            ctx.moveTo(30, 10);
            ctx.lineTo(30, canvas.height - 20);
            ctx.stroke();
            
            // Labels
            ctx.fillStyle = '#a0aec0';
            ctx.font = '10px Tajawal';
            ctx.fillText('الحجم (مل)', canvas.width - 50, canvas.height - 5);
            ctx.save();
            ctx.translate(15, canvas.height / 2);
            ctx.rotate(-Math.PI / 2);
            ctx.fillText('pH', 0, 0);
            ctx.restore();
        }

        function updateTitrationCurve() {
            const canvas = document.getElementById('phCurve');
            const ctx = canvas.getContext('2d');
            
            if (experimentData.length < 2) return;
            
            // Clear previous curve
            setupTitrationCurve();
            
            // Draw curve
            ctx.strokeStyle = '#90cdf4';
            ctx.lineWidth = 2;
            ctx.beginPath();
            
            experimentData.forEach((point, index) => {
                const x = 30 + (point.volume / 30) * (canvas.width - 40);
                const y = canvas.height - 20 - ((point.ph / 14) * (canvas.height - 30));
                
                if (index === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            });
            
            ctx.stroke();
            
            // Mark current point
            if (experimentData.length > 0) {
                const lastPoint = experimentData[experimentData.length - 1];
                const x = 30 + (lastPoint.volume / 30) * (canvas.width - 40);
                const y = canvas.height - 20 - ((lastPoint.ph / 14) * (canvas.height - 30));
                
                ctx.fillStyle = '#f6ad55';
                ctx.beginPath();
                ctx.arc(x, y, 4, 0, 2 * Math.PI);
                ctx.fill();
            }
        }

        function addTitrant() {
            if (!isExperimentRunning) {
                startExperiment();
            }
        }

        function addIndicator() {
            const alertElement = document.getElementById('safetyAlert');
            alertElement.className = 'safety-alert info';
            alertElement.innerHTML = '<i class="fas fa-tint"></i> تمت إضافة مؤشر الفينولفثالين';
            
            // Flash the beaker
            const beaker = document.querySelector('.beaker');
            if (beaker) {
                beaker.classList.add('reacting');
                setTimeout(() => beaker.classList.remove('reacting'), 1500);
            }
        }

        function pauseExperiment() {
            isExperimentRunning = false;
            if (animationFrameId) {
                cancelAnimationFrame(animationFrameId);
            }
        }

        function stopExperiment() {
            pauseExperiment();
            resetExperiment();
        }

        function resetExperiment() {
            titrationData = { volumeAdded: 0, ph: 3.2, temperature: 22 };
            experimentData = [];
            updateTitrationDisplay();
            setupTitrationCurve();
            updateProgress(0);
        }

        function updateProgress(percentage) {
            document.getElementById('experimentProgress').style.width = percentage + '%';
        }

        function showCompletionMessage() {
            const alertElement = document.getElementById('safetyAlert');
            alertElement.className = 'safety-alert';
            alertElement.style.background = 'rgba(72, 187, 120, 0.1)';
            alertElement.style.borderColor = '#48bb78';
            alertElement.style.color = '#c6f6d5';
            alertElement.innerHTML = '<i class="fas fa-check-circle"></i> تمت التجربة بنجاح! نقطة التكافؤ عند ' + titrationData.volumeAdded.toFixed(1) + ' مل';
            
            // Calculate concentration
            const concentration = (0.1 * titrationData.volumeAdded) / 25;
            document.getElementById('calculatedConcentration').textContent = concentration.toFixed(3) + ' M';
            document.getElementById('equivalencePoint').textContent = titrationData.volumeAdded.toFixed(1) + ' مل';
        }

        // Virtual Instruments
        function openInstrument(instrumentType) {
            if (instrumentType === 'spectrophotometer') {
                document.getElementById('spectrophotometerOverlay').style.display = 'flex';
            } else if (instrumentType === 'phmeter') {
                document.getElementById('phmeterOverlay').style.display = 'flex';
            }
            // Add more instruments as needed
        }

        function closeInstrument(instrumentType) {
            if (instrumentType === 'spectrophotometer') {
                document.getElementById('spectrophotometerOverlay').style.display = 'none';
            } else if (instrumentType === 'phmeter') {
                document.getElementById('phmeterOverlay').style.display = 'none';
            }
        }

        function updateSpectrophotometer() {
            const wavelength = document.getElementById('wavelengthSlider').value;
            document.getElementById('wavelengthValue').textContent = wavelength;
            
            // Simulate absorbance calculation
            const absorbance = Math.random() * 2; // Mock data
            document.getElementById('absorbanceReading').textContent = absorbance.toFixed(3);
        }

        function takeSpectroReading() {
            const wavelength = document.getElementById('wavelengthSlider').value;
            const absorbance = document.getElementById('absorbanceReading').textContent;
            
            // Add to data log
            console.log(`Spectrophotometer reading: λ=${wavelength}nm, A=${absorbance}`);
            
            // Flash the display
            const reading = document.getElementById('absorbanceReading');
            reading.style.color = '#48bb78';
            setTimeout(() => reading.style.color = '#90cdf4', 500);
        }

        function takePHReading() {
            const ph = titrationData.ph;
            document.getElementById('phReading').textContent = ph.toFixed(2);
            
            // Determine solution type
            let solutionType = 'متعادل';
            if (ph < 7) solutionType = 'حمضي';
            else if (ph > 7) solutionType = 'قاعدي';
            
            document.getElementById('solutionType').textContent = solutionType;
        }

        function showSafetyProtocol() {
            alert('بروتوكولات السلامة:\n\n1. ارتدِ نظارات الأمان دائماً\n2. استخدم القفازات المناسبة\n3. تأكد من التهوية الجيدة\n4. اقرأ تعليمات السلامة لكل مادة\n5. احتفظ بمطفأة الحريق قريباً\n6. تعرف على مخارج الطوارئ');
        }

        function saveExperiment() {
            const experimentState = {
                type: currentExperiment,
                data: experimentData,
                timestamp: new Date().toISOString()
            };
            
            localStorage.setItem('savedChemistryExperiment', JSON.stringify(experimentState));
            alert('تم حفظ التجربة بنجاح!');
        }

        function exportData() {
            if (experimentData.length === 0) {
                alert('لا توجد بيانات للتصدير');
                return;
            }
            
            let csv = 'الحجم (مل),pH,الوقت\n';
            experimentData.forEach(row => {
                csv += `${row.volume.toFixed(2)},${row.ph.toFixed(2)},${new Date(row.timestamp).toLocaleTimeString()}\n`;
            });
            
            downloadCSV(csv, `${currentExperiment}_data.csv`);
        }

        function downloadCSV(content, filename) {
            const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            
            if (link.download !== undefined) {
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', filename);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }
        }

        function resetLab() {
            pauseExperiment();
            resetExperiment();
            
            // Reset all instruments
            document.querySelectorAll('.instrument').forEach(inst => {
                inst.classList.remove('active');
            });
            
            // Reset safety alert
            updateSafetyAlert(currentExperiment);
        }

        function goBack() {
            window.location.href = 'virtual-labs.html';
        }

        // Advanced chemistry calculations
        function performAdvancedCalculations() {
            // Thermodynamics calculations
            calculateGibbsFreeEnergy();
            calculateEnthalpyChange();
            calculateEntropy();
            
            // Kinetics calculations
            calculateReactionRate();
            calculateActivationEnergy();
            calculateHalfLife();
            
            // Equilibrium calculations
            calculateEquilibriumConstant();
            applyLeChatelier();
            
            // Electrochemistry calculations
            calculateCellPotential();
            performNernstCalculation();
        }

        function calculateGibbsFreeEnergy() {
            // ΔG = ΔH - TΔS
            const deltaH = -100; // kJ/mol (example)
            const temperature = 298; // K
            const deltaS = 0.2; // kJ/mol·K
            
            const deltaG = deltaH - (temperature * deltaS);
            return deltaG;
        }

        function calculateEnthalpyChange() {
            // ΔH = Σ(ΔHf products) - Σ(ΔHf reactants)
            const products = [{ compound: 'H2O', moles: 2, enthalpy: -285.8 }];
            const reactants = [{ compound: 'H2', moles: 2, enthalpy: 0 }, { compound: 'O2', moles: 1, enthalpy: 0 }];
            
            let deltaH = 0;
            products.forEach(p => deltaH += p.moles * p.enthalpy);
            reactants.forEach(r => deltaH -= r.moles * r.enthalpy);
            
            return deltaH;
        }

        function performSpectrophotometryAnalysis() {
            // Beer's Law: A = εcl
            const molarExtinctivity = 1000; // L/mol·cm
            const pathLength = 1; // cm
            const concentration = 0.001; // mol/L
            
            const absorbance = molarExtinctivity * concentration * pathLength;
            return absorbance;
        }

        function simulateElectrochemicalCell() {
            // Nernst equation: E = E° - (RT/nF)ln(Q)
            const standardPotential = 1.1; // V
            const gasConstant = 8.314; // J/mol·K
            const temperature = 298; // K
            const electronsTransferred = 2;
            const faradayConstant = 96485; // C/mol
            const reactionQuotient = 1;
            
            const cellPotential = standardPotential - (gasConstant * temperature / (electronsTransferred * faradayConstant)) * Math.log(reactionQuotient);
            return cellPotential;
        }

        function simulateGasChromatography() {
            // Simulate GC analysis for compound identification
            const retentionTimes = {
                'methanol': 2.3,
                'ethanol': 3.7,
                'acetone': 1.8,
                'benzene': 5.2
            };
            
            return retentionTimes;
        }

        function calculateMolecularOrbitalTheory() {
            // LCAO-MO calculations for simple molecules
            const orbitalEnergies = {
                'H2': { bonding: -1.1, antibonding: 0.7 },
                'He2': { bonding: -1.8, antibonding: -0.2 },
                'Li2': { bonding: -1.0, antibonding: 0.5 }
            };
            
            return orbitalEnergies;
        }

        // Quantum chemistry calculations
        function calculateQuantumProperties() {
            // Schrödinger equation solutions for simple systems
            const hydrogenLevels = [];
            for (let n = 1; n <= 5; n++) {
                const energy = -13.6 / (n * n); // eV
                hydrogenLevels.push({ n: n, energy: energy });
            }
            
            return hydrogenLevels;
        }

        // Nuclear chemistry calculations
        function calculateRadioactiveDecay() {
            const halfLife = 5730; // years (Carbon-14)
            const decayConstant = Math.log(2) / halfLife;
            
            function remainingAmount(initialAmount, time) {
                return initialAmount * Math.exp(-decayConstant * time);
            }
            
            return { halfLife, decayConstant, remainingAmount };
        }

        // Advanced safety protocols
        function implementAdvancedSafety() {
            const safetyProtocols = {
                chemicalStorage: {
                    acids: 'Store in acid cabinet, separate from bases',
                    bases: 'Store in base cabinet, separate from acids',
                    organics: 'Store in flammable cabinet, away from heat',
                    oxidizers: 'Store separately from flammables'
                },
                emergencyProcedures: {
                    fire: ['Evacuate area', 'Alert emergency services', 'Use appropriate extinguisher'],
                    spill: ['Contain spill', 'Neutralize if safe', 'Clean according to SDS'],
                    exposure: ['Remove from source', 'Flush with water', 'Seek medical attention']
                },
                personalProtectiveEquipment: {
                    eyes: 'Safety goggles always required',
                    hands: 'Chemical-resistant gloves',
                    body: 'Lab coat or chemical suit',
                    respiratory: 'Fume hood or respirator if needed'
                }
            };
            
            return safetyProtocols;
        }

        // Machine learning for reaction prediction
        function predictReactionOutcome(reactants, conditions) {
            // Simplified ML model for reaction prediction
            const reactionDatabase = {
                'acid_base': { products: ['salt', 'water'], probability: 0.95 },
                'combustion': { products: ['CO2', 'H2O'], probability: 0.90 },
                'precipitation': { products: ['solid', 'solution'], probability: 0.85 }
            };
            
            // Classify reaction type based on reactants
            let reactionType = 'unknown';
            if (reactants.includes('acid') && reactants.includes('base')) {
                reactionType = 'acid_base';
            }
            
            return reactionDatabase[reactionType] || { products: ['unknown'], probability: 0.5 };
        }

        // Initialize chemistry lab
        document.addEventListener('DOMContentLoaded', function() {
            initializeChemistryLab();
            performAdvancedCalculations();
            implementAdvancedSafety();
        });
    </script>
</body>
</html>
