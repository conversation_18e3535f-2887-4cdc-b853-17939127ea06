// Bilingual Language System for SimLab HUB
// Supports English and Arabic with RTL layout

class LanguageSystem {
    constructor() {
        this.currentLanguage = 'en';
        this.translations = {};
        this.rtlLanguages = ['ar'];
        this.storageKey = 'simlab_language';
        
        this.init();
    }

    init() {
        this.loadTranslations();
        this.loadSavedLanguage();
        this.createLanguageSwitcher();
        this.applyLanguage();
    }

    loadTranslations() {
        this.translations = {
            en: {
                // Navigation
                'nav.home': 'Home',
                'nav.labs': 'Virtual Labs',
                'nav.physics': 'Physics Lab',
                'nav.chemistry': 'Chemistry Lab',
                'nav.electronics': 'Electronics Lab',
                'nav.tools': 'Interactive Tools',
                'nav.experiments': 'Experiments',
                'nav.lectures': 'Lectures',
                'nav.modules': 'Learning Modules',
                'nav.showcase': 'Complete Lab Suite',

                // Common UI Elements
                'ui.start': 'Start',
                'ui.stop': 'Stop',
                'ui.reset': 'Reset',
                'ui.save': 'Save',
                'ui.load': 'Load',
                'ui.export': 'Export',
                'ui.import': 'Import',
                'ui.close': 'Close',
                'ui.cancel': 'Cancel',
                'ui.confirm': 'Confirm',
                'ui.next': 'Next',
                'ui.previous': 'Previous',
                'ui.settings': 'Settings',
                'ui.help': 'Help',
                'ui.about': 'About',

                // Laboratory Common
                'lab.title': 'Virtual Laboratory',
                'lab.experiment': 'Experiment',
                'lab.instruments': 'Instruments',
                'lab.measurements': 'Measurements',
                'lab.results': 'Results',
                'lab.analysis': 'Analysis',
                'lab.controls': 'Controls',
                'lab.safety': 'Safety',
                'lab.equipment': 'Equipment',
                'lab.workspace': 'Workspace',

                // Physics Lab
                'physics.title': 'Enhanced Physics Laboratory',
                'physics.description': 'Complete physics laboratory with integrated virtual instruments and real-time data logging',
                'physics.caliper': 'Digital Caliper',
                'physics.thermometer': 'Digital Thermometer',
                'physics.voltmeter': 'Digital Voltmeter',
                'physics.oscilloscope': 'Digital Oscilloscope',
                'physics.temperature': 'Temperature',
                'physics.voltage': 'Voltage',
                'physics.distance': 'Distance',
                'physics.time': 'Time',

                // Chemistry Lab
                'chemistry.title': 'Virtual Chemistry Laboratory',
                'chemistry.description': 'Interactive chemistry lab with molecular visualization and safety protocols',
                'chemistry.beaker': 'Beaker',
                'chemistry.flask': 'Flask',
                'chemistry.burette': 'Burette',
                'chemistry.pipette': 'Pipette',
                'chemistry.titration': 'Acid-Base Titration',
                'chemistry.calorimetry': 'Calorimetry',
                'chemistry.synthesis': 'Synthesis',
                'chemistry.safety_alert': 'Safety Alert',
                'chemistry.chemical_inventory': 'Chemical Inventory',

                // Electronics Lab
                'electronics.title': 'Virtual Electronics Laboratory',
                'electronics.description': 'Comprehensive electronics lab for circuit analysis and component testing',
                'electronics.breadboard': 'Breadboard',
                'electronics.components': 'Components',
                'electronics.resistor': 'Resistor',
                'electronics.capacitor': 'Capacitor',
                'electronics.inductor': 'Inductor',
                'electronics.diode': 'Diode',
                'electronics.transistor': 'Transistor',
                'electronics.multimeter': 'Multimeter',
                'electronics.oscilloscope': 'Oscilloscope',
                'electronics.circuit_diagram': 'Circuit Diagram',
                'electronics.simulation': 'Simulation',

                // Experiments
                'exp.ohms_law': "Ohm's Law",
                'exp.series_parallel': 'Series and Parallel Circuits',
                'exp.kirchhoff': "Kirchhoff's Laws",
                'exp.rc_circuit': 'RC Circuits',
                'exp.diode_char': 'Diode Characteristics',
                'exp.rectifier': 'Rectifier Circuits',
                'exp.amplifier': 'Amplifier Circuits',
                'exp.logic_gates': 'Logic Gates',

                // Safety
                'safety.warning': 'Warning',
                'safety.danger': 'Danger',
                'safety.caution': 'Caution',
                'safety.emergency_stop': 'Emergency Stop',
                'safety.ppe_required': 'PPE Required',
                'safety.ventilation': 'Ensure Adequate Ventilation',

                // Footer
                'footer.author': 'Author: Dr. Mohammed Yagoub Esmail, SUST - BME, © 2025',
                'footer.copyright': 'Copyright',
                'footer.email': '<EMAIL>',
                'footer.phone': 'Phone: +249912867327, +966538076790',
                'footer.privacy': 'Privacy Policy',
                'footer.terms': 'Terms of Use',
                'footer.contact': 'Contact Information'
            },

            ar: {
                // Navigation
                'nav.home': 'الرئيسية',
                'nav.labs': 'المختبرات الافتراضية',
                'nav.physics': 'مختبر الفيزياء',
                'nav.chemistry': 'مختبر الكيمياء',
                'nav.electronics': 'مختبر الإلكترونيات',
                'nav.tools': 'الأدوات التفاعلية',
                'nav.experiments': 'التجارب',
                'nav.lectures': 'المحاضرات',
                'nav.modules': 'وحدات التعلم',
                'nav.showcase': 'مجموعة المختبرات الكاملة',

                // Common UI Elements
                'ui.start': 'بدء',
                'ui.stop': 'إيقاف',
                'ui.reset': 'إعادة تعيين',
                'ui.save': 'حفظ',
                'ui.load': 'تحميل',
                'ui.export': 'تصدير',
                'ui.import': 'استيراد',
                'ui.close': 'إغلاق',
                'ui.cancel': 'إلغاء',
                'ui.confirm': 'تأكيد',
                'ui.next': 'التالي',
                'ui.previous': 'السابق',
                'ui.settings': 'الإعدادات',
                'ui.help': 'المساعدة',
                'ui.about': 'حول',

                // Laboratory Common
                'lab.title': 'المختبر الافتراضي',
                'lab.experiment': 'التجربة',
                'lab.instruments': 'الأجهزة',
                'lab.measurements': 'القياسات',
                'lab.results': 'النتائج',
                'lab.analysis': 'التحليل',
                'lab.controls': 'التحكم',
                'lab.safety': 'السلامة',
                'lab.equipment': 'المعدات',
                'lab.workspace': 'مساحة العمل',

                // Physics Lab
                'physics.title': 'مختبر الفيزياء المتقدم',
                'physics.description': 'مختبر فيزياء كامل مع أجهزة افتراضية متكاملة وتسجيل البيانات في الوقت الفعلي',
                'physics.caliper': 'الكاليبر الرقمي',
                'physics.thermometer': 'مقياس الحرارة الرقمي',
                'physics.voltmeter': 'مقياس الجهد الرقمي',
                'physics.oscilloscope': 'راسم الذبذبات الرقمي',
                'physics.temperature': 'درجة الحرارة',
                'physics.voltage': 'الجهد',
                'physics.distance': 'المسافة',
                'physics.time': 'الوقت',

                // Chemistry Lab
                'chemistry.title': 'مختبر الكيمياء الافتراضي',
                'chemistry.description': 'مختبر كيمياء تفاعلي مع التصور الجزيئي وبروتوكولات السلامة',
                'chemistry.beaker': 'كأس زجاجي',
                'chemistry.flask': 'دورق',
                'chemistry.burette': 'سحاحة',
                'chemistry.pipette': 'ماصة',
                'chemistry.titration': 'المعايرة الحمضية القاعدية',
                'chemistry.calorimetry': 'قياس الحرارة',
                'chemistry.synthesis': 'التخليق',
                'chemistry.safety_alert': 'تنبيه السلامة',
                'chemistry.chemical_inventory': 'مخزون المواد الكيميائية',

                // Electronics Lab
                'electronics.title': 'مختبر الإلكترونيات الافتراضي',
                'electronics.description': 'مختبر إلكترونيات شامل لتحليل الدوائر واختبار المكونات',
                'electronics.breadboard': 'لوحة التجارب',
                'electronics.components': 'المكونات',
                'electronics.resistor': 'مقاومة',
                'electronics.capacitor': 'مكثف',
                'electronics.inductor': 'ملف',
                'electronics.diode': 'ديود',
                'electronics.transistor': 'ترانزستور',
                'electronics.multimeter': 'مقياس متعدد',
                'electronics.oscilloscope': 'راسم الذبذبات',
                'electronics.circuit_diagram': 'مخطط الدائرة',
                'electronics.simulation': 'المحاكاة',

                // Experiments
                'exp.ohms_law': 'قانون أوم',
                'exp.series_parallel': 'الدوائر المتسلسلة والمتوازية',
                'exp.kirchhoff': 'قوانين كيرشوف',
                'exp.rc_circuit': 'دوائر RC',
                'exp.diode_char': 'خصائص الديود',
                'exp.rectifier': 'دوائر التقويم',
                'exp.amplifier': 'دوائر التضخيم',
                'exp.logic_gates': 'البوابات المنطقية',

                // Safety
                'safety.warning': 'تحذير',
                'safety.danger': 'خطر',
                'safety.caution': 'احتياط',
                'safety.emergency_stop': 'إيقاف طوارئ',
                'safety.ppe_required': 'معدات الحماية مطلوبة',
                'safety.ventilation': 'تأكد من التهوية الكافية',

                // Footer
                'footer.author': 'المؤلف: د. محمد يعقوب إسماعيل، جامعة السودان للعلوم والتكنولوجيا - الهندسة الطبية الحيوية، © 2025',
                'footer.copyright': 'حقوق الطبع والنشر',
                'footer.email': '<EMAIL>',
                'footer.phone': 'الهاتف: +249912867327, +966538076790',
                'footer.privacy': 'سياسة الخصوصية',
                'footer.terms': 'شروط الاستخدام',
                'footer.contact': 'معلومات الاتصال'
            }
        };
    }

    loadSavedLanguage() {
        const saved = localStorage.getItem(this.storageKey);
        if (saved && this.translations[saved]) {
            this.currentLanguage = saved;
        } else {
            // Auto-detect browser language
            const browserLang = navigator.language.split('-')[0];
            if (this.translations[browserLang]) {
                this.currentLanguage = browserLang;
            }
        }
    }

    createLanguageSwitcher() {
        // Create language switcher if it doesn't exist
        let switcher = document.getElementById('language-switcher');
        if (!switcher) {
            switcher = document.createElement('div');
            switcher.id = 'language-switcher';
            switcher.className = 'language-switcher';
            
            switcher.innerHTML = `
                <button class="lang-btn ${this.currentLanguage === 'en' ? 'active' : ''}" 
                        onclick="languageSystem.switchLanguage('en')" data-lang="en">
                    <span class="flag">🇺🇸</span>
                    <span class="lang-text">EN</span>
                </button>
                <button class="lang-btn ${this.currentLanguage === 'ar' ? 'active' : ''}" 
                        onclick="languageSystem.switchLanguage('ar')" data-lang="ar">
                    <span class="flag">🇸🇦</span>
                    <span class="lang-text">العربية</span>
                </button>
            `;
            
            // Try to add to header or create one
            let header = document.querySelector('.lab-header, .showcase-header, header');
            if (header) {
                header.appendChild(switcher);
            } else {
                // Add to body if no header found
                document.body.insertBefore(switcher, document.body.firstChild);
            }
        }
    }

    switchLanguage(lang) {
        if (!this.translations[lang]) return;
        
        this.currentLanguage = lang;
        localStorage.setItem(this.storageKey, lang);
        
        this.applyLanguage();
        this.updateLanguageSwitcher();
    }

    applyLanguage() {
        // Apply RTL/LTR direction
        document.documentElement.dir = this.isRTL() ? 'rtl' : 'ltr';
        document.documentElement.lang = this.currentLanguage;
        
        // Add language class to body
        document.body.className = document.body.className.replace(/\blang-\w+\b/g, '');
        document.body.classList.add(`lang-${this.currentLanguage}`);
        
        // Translate all elements with data-translate attribute
        this.translateElements();
        
        // Apply RTL styles if needed
        this.applyRTLStyles();
    }

    translateElements() {
        const elements = document.querySelectorAll('[data-translate]');
        elements.forEach(element => {
            const key = element.getAttribute('data-translate');
            const translation = this.getTranslation(key);
            
            if (translation) {
                if (element.tagName === 'INPUT' && element.type === 'button') {
                    element.value = translation;
                } else if (element.hasAttribute('placeholder')) {
                    element.placeholder = translation;
                } else if (element.hasAttribute('title')) {
                    element.title = translation;
                } else {
                    element.textContent = translation;
                }
            }
        });
    }

    getTranslation(key) {
        return this.translations[this.currentLanguage][key] || 
               this.translations['en'][key] || 
               key;
    }

    isRTL() {
        return this.rtlLanguages.includes(this.currentLanguage);
    }

    applyRTLStyles() {
        let rtlStylesheet = document.getElementById('rtl-styles');
        
        if (this.isRTL()) {
            if (!rtlStylesheet) {
                rtlStylesheet = document.createElement('link');
                rtlStylesheet.id = 'rtl-styles';
                rtlStylesheet.rel = 'stylesheet';
                rtlStylesheet.href = 'css/rtl-styles.css';
                document.head.appendChild(rtlStylesheet);
            }
        } else {
            if (rtlStylesheet) {
                rtlStylesheet.remove();
            }
        }
    }

    updateLanguageSwitcher() {
        const buttons = document.querySelectorAll('.lang-btn');
        buttons.forEach(btn => {
            btn.classList.toggle('active', btn.dataset.lang === this.currentLanguage);
        });
    }

    // Helper method to translate text programmatically
    t(key) {
        return this.getTranslation(key);
    }

    // Method to add translations dynamically
    addTranslations(lang, translations) {
        if (!this.translations[lang]) {
            this.translations[lang] = {};
        }
        Object.assign(this.translations[lang], translations);
    }

    // Method to translate dynamic content
    translateDynamicContent(container) {
        if (typeof container === 'string') {
            container = document.getElementById(container);
        }
        
        if (container) {
            const elements = container.querySelectorAll('[data-translate]');
            elements.forEach(element => {
                const key = element.getAttribute('data-translate');
                const translation = this.getTranslation(key);
                if (translation) {
                    element.textContent = translation;
                }
            });
        }
    }
}

// Global language system instance
let languageSystem;

document.addEventListener('DOMContentLoaded', function() {
    languageSystem = new LanguageSystem();
    
    // Auto-translate existing elements
    setTimeout(() => {
        languageSystem.translateElements();
    }, 100);
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LanguageSystem;
}
