<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مختبر الفيزياء التفاعلي - Physics Lab | SimLab HUB</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: '<PERSON><PERSON>wal', Arial, sans-serif;
            background: linear-gradient(135deg, #0f1419 0%, #1a202c 100%);
            color: #e2e8f0;
            overflow-x: hidden;
        }

        .lab-interface {
            display: grid;
            grid-template-columns: 300px 1fr 300px;
            grid-template-rows: 60px 1fr 80px;
            height: 100vh;
            gap: 2px;
        }

        .header-bar {
            grid-column: 1 / -1;
            background: rgba(26, 32, 44, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid #2d3748;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
        }

        .lab-title {
            font-size: 1.2rem;
            font-weight: 700;
            color: #90cdf4;
        }

        .header-controls {
            display: flex;
            gap: 10px;
        }

        .header-btn {
            background: linear-gradient(135deg, #4299e1, #3182ce);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .header-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(66, 153, 225, 0.3);
        }

        .left-panel {
            background: rgba(26, 32, 44, 0.95);
            backdrop-filter: blur(10px);
            border-right: 1px solid #2d3748;
            padding: 20px;
            overflow-y: auto;
        }

        .right-panel {
            background: rgba(26, 32, 44, 0.95);
            backdrop-filter: blur(10px);
            border-left: 1px solid #2d3748;
            padding: 20px;
            overflow-y: auto;
        }

        .simulation-area {
            background: linear-gradient(135deg, #1a202c, #2d3748);
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .footer-bar {
            grid-column: 1 / -1;
            background: rgba(26, 32, 44, 0.95);
            backdrop-filter: blur(10px);
            border-top: 1px solid #2d3748;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
        }

        .panel-section {
            margin-bottom: 25px;
        }

        .panel-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #90cdf4;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .control-group {
            margin-bottom: 15px;
        }

        .control-label {
            display: block;
            font-size: 0.9rem;
            color: #a0aec0;
            margin-bottom: 5px;
        }

        .control-input {
            width: 100%;
            background: rgba(45, 55, 72, 0.8);
            border: 1px solid #4a5568;
            border-radius: 8px;
            padding: 8px 12px;
            color: #e2e8f0;
            font-size: 0.9rem;
        }

        .control-input:focus {
            outline: none;
            border-color: #4299e1;
            box-shadow: 0 0 0 2px rgba(66, 153, 225, 0.2);
        }

        .control-slider {
            width: 100%;
            height: 6px;
            border-radius: 3px;
            background: #4a5568;
            outline: none;
            opacity: 0.7;
            transition: opacity 0.2s;
        }

        .control-slider:hover {
            opacity: 1;
        }

        .control-slider::-webkit-slider-thumb {
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #4299e1;
            cursor: pointer;
        }

        .control-button {
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            font-size: 0.9rem;
            cursor: pointer;
            width: 100%;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }

        .control-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(72, 187, 120, 0.3);
        }

        .control-button.danger {
            background: linear-gradient(135deg, #f56565, #e53e3e);
        }

        .control-button.danger:hover {
            box-shadow: 0 4px 15px rgba(245, 101, 101, 0.3);
        }

        .simulation-canvas {
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at center, #2d3748 0%, #1a202c 100%);
            position: relative;
            overflow: hidden;
            border-radius: 10px;
            margin: 10px;
            box-shadow: inset 0 0 50px rgba(0, 0, 0, 0.5);
        }

        .grid-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0.1;
            background-image: 
                linear-gradient(#4a5568 1px, transparent 1px),
                linear-gradient(90deg, #4a5568 1px, transparent 1px);
            background-size: 20px 20px;
        }

        .simulation-object {
            position: absolute;
            background: #4299e1;
            border-radius: 50%;
            transition: all 0.1s ease;
            box-shadow: 0 0 20px rgba(66, 153, 225, 0.6);
        }

        .trajectory-path {
            position: absolute;
            border: 2px dashed #90cdf4;
            opacity: 0.6;
            pointer-events: none;
        }

        .data-display {
            background: rgba(45, 55, 72, 0.9);
            border: 1px solid #4a5568;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .data-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 0.9rem;
        }

        .data-label {
            color: #a0aec0;
        }

        .data-value {
            color: #90cdf4;
            font-weight: 600;
        }

        .graph-container {
            background: rgba(45, 55, 72, 0.9);
            border: 1px solid #4a5568;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            height: 200px;
            position: relative;
        }

        .graph-axis {
            position: absolute;
            background: #4a5568;
        }

        .graph-axis.x {
            bottom: 30px;
            left: 40px;
            right: 10px;
            height: 1px;
        }

        .graph-axis.y {
            bottom: 30px;
            left: 40px;
            top: 10px;
            width: 1px;
        }

        .graph-line {
            position: absolute;
            height: 2px;
            background: #4299e1;
            transform-origin: left center;
        }

        .experiment-selector {
            background: rgba(45, 55, 72, 0.9);
            border: 1px solid #4a5568;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .experiment-option {
            display: block;
            width: 100%;
            background: transparent;
            border: 1px solid #4a5568;
            border-radius: 6px;
            padding: 10px;
            color: #e2e8f0;
            text-align: right;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .experiment-option:hover {
            border-color: #4299e1;
            background: rgba(66, 153, 225, 0.1);
        }

        .experiment-option.active {
            background: rgba(66, 153, 225, 0.2);
            border-color: #4299e1;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9rem;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #48bb78;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .help-section {
            background: rgba(72, 187, 120, 0.1);
            border: 1px solid #48bb78;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
        }

        .help-title {
            color: #68d391;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .help-text {
            font-size: 0.85rem;
            color: #a0aec0;
            line-height: 1.5;
        }

        @media (max-width: 1024px) {
            .lab-interface {
                grid-template-columns: 1fr;
                grid-template-rows: 60px 200px 1fr 80px;
            }
            
            .left-panel, .right-panel {
                border: none;
                border-bottom: 1px solid #2d3748;
                overflow-x: auto;
                white-space: nowrap;
            }
            
            .right-panel {
                order: 1;
            }
            
            .simulation-area {
                order: 2;
            }
            
            .footer-bar {
                order: 3;
            }
        }

        /* Physics-specific styles */
        .projectile {
            background: radial-gradient(circle, #f6ad55, #ed8936);
            box-shadow: 0 0 20px rgba(246, 173, 85, 0.6);
        }

        .pendulum-bob {
            background: radial-gradient(circle, #90cdf4, #4299e1);
            box-shadow: 0 0 20px rgba(144, 205, 244, 0.6);
        }

        .pendulum-string {
            position: absolute;
            background: #a0aec0;
            transform-origin: top center;
        }

        .spring {
            position: absolute;
            background: repeating-linear-gradient(
                90deg,
                #4a5568 0px,
                #4a5568 3px,
                transparent 3px,
                transparent 6px
            );
            height: 10px;
            border-radius: 5px;
        }

        .vector-arrow {
            position: absolute;
            background: #f6ad55;
            transform-origin: center;
        }

        .vector-arrow::after {
            content: '';
            position: absolute;
            right: -8px;
            top: 50%;
            transform: translateY(-50%);
            width: 0;
            height: 0;
            border-left: 8px solid #f6ad55;
            border-top: 4px solid transparent;
            border-bottom: 4px solid transparent;
        }

        .measurement-tool {
            position: absolute;
            background: rgba(144, 205, 244, 0.2);
            border: 1px dashed #90cdf4;
            border-radius: 4px;
            pointer-events: none;
        }

        .particle-trace {
            position: absolute;
            width: 2px;
            height: 2px;
            background: #90cdf4;
            border-radius: 50%;
            opacity: 0.8;
            animation: fadeOut 2s ease-out forwards;
        }

        @keyframes fadeOut {
            from { opacity: 0.8; }
            to { opacity: 0; }
        }

        .collision-effect {
            position: absolute;
            width: 30px;
            height: 30px;
            border: 2px solid #f6ad55;
            border-radius: 50%;
            animation: expand 0.5s ease-out forwards;
        }

        @keyframes expand {
            from {
                transform: scale(0);
                opacity: 1;
            }
            to {
                transform: scale(2);
                opacity: 0;
            }
        }
    </style>
</head>
<body>
    <div class="lab-interface">
        <!-- Header Bar -->
        <div class="header-bar">
            <div class="lab-title">
                <i class="fas fa-flask"></i>
                <span id="labTitle">مختبر الفيزياء التفاعلي</span>
            </div>
            <div class="header-controls">
                <button class="header-btn" onclick="resetExperiment()">
                    <i class="fas fa-redo"></i> إعادة تعيين
                </button>
                <button class="header-btn" onclick="saveExperiment()">
                    <i class="fas fa-save"></i> حفظ
                </button>
                <button class="header-btn" onclick="exportData()">
                    <i class="fas fa-download"></i> تصدير البيانات
                </button>
                <button class="header-btn" onclick="goBack()">
                    <i class="fas fa-arrow-right"></i> العودة
                </button>
            </div>
        </div>

        <!-- Left Panel - Controls -->
        <div class="left-panel">
            <div class="experiment-selector">
                <div class="panel-title">
                    <i class="fas fa-list"></i>
                    اختر التجربة
                </div>
                <button class="experiment-option active" onclick="selectExperiment('projectile')">
                    حركة المقذوفات
                </button>
                <button class="experiment-option" onclick="selectExperiment('pendulum')">
                    البندول البسيط
                </button>
                <button class="experiment-option" onclick="selectExperiment('collision')">
                    التصادمات
                </button>
                <button class="experiment-option" onclick="selectExperiment('spring')">
                    نظام نابض-كتلة
                </button>
                <button class="experiment-option" onclick="selectExperiment('newton')">
                    قوانين نيوتن
                </button>
            </div>

            <div class="panel-section" id="experimentControls">
                <div class="panel-title">
                    <i class="fas fa-sliders-h"></i>
                    متغيرات التجربة
                </div>
                
                <!-- Projectile Motion Controls -->
                <div id="projectileControls" class="experiment-controls">
                    <div class="control-group">
                        <label class="control-label">السرعة الابتدائية (م/ث)</label>
                        <input type="range" class="control-slider" id="initialVelocity" 
                               min="5" max="50" value="20" oninput="updateProjectileParams()">
                        <span id="velocityValue">20</span>
                    </div>
                    
                    <div class="control-group">
                        <label class="control-label">زاوية الإطلاق (درجة)</label>
                        <input type="range" class="control-slider" id="launchAngle" 
                               min="0" max="90" value="45" oninput="updateProjectileParams()">
                        <span id="angleValue">45</span>
                    </div>
                    
                    <div class="control-group">
                        <label class="control-label">الكتلة (كغ)</label>
                        <input type="range" class="control-slider" id="projectileMass" 
                               min="0.1" max="10" value="1" step="0.1" oninput="updateProjectileParams()">
                        <span id="massValue">1</span>
                    </div>
                    
                    <div class="control-group">
                        <label class="control-label">الجاذبية (م/ث²)</label>
                        <input type="range" class="control-slider" id="gravity" 
                               min="1" max="20" value="9.8" step="0.1" oninput="updateProjectileParams()">
                        <span id="gravityValue">9.8</span>
                    </div>
                </div>
            </div>

            <div class="panel-section">
                <div class="panel-title">
                    <i class="fas fa-play"></i>
                    التحكم في المحاكاة
                </div>
                <button class="control-button" id="playButton" onclick="toggleSimulation()">
                    <i class="fas fa-play"></i> تشغيل التجربة
                </button>
                <button class="control-button" onclick="pauseSimulation()">
                    <i class="fas fa-pause"></i> إيقاف مؤقت
                </button>
                <button class="control-button danger" onclick="stopSimulation()">
                    <i class="fas fa-stop"></i> إيقاف
                </button>
                
                <div class="control-group">
                    <label class="control-label">سرعة المحاكاة</label>
                    <input type="range" class="control-slider" id="simulationSpeed" 
                           min="0.1" max="2" value="1" step="0.1" oninput="updateSimulationSpeed()">
                    <span id="speedValue">1x</span>
                </div>
            </div>

            <div class="help-section">
                <div class="help-title">💡 نصائح التجربة</div>
                <div class="help-text" id="experimentHelp">
                    اضبط المتغيرات واضغط تشغيل لبدء التجربة. استخدم الأزرار للتحكم في المحاكاة ومراقبة النتائج في اللوحة اليمنى.
                </div>
            </div>
        </div>

        <!-- Simulation Area -->
        <div class="simulation-area">
            <div class="simulation-canvas" id="simulationCanvas">
                <div class="grid-background"></div>
                
                <!-- Coordinate system -->
                <div style="position: absolute; bottom: 20px; left: 20px; color: #a0aec0; font-size: 0.8rem;">
                    <div>← X (م)</div>
                    <div style="transform: rotate(90deg); transform-origin: center; margin-top: 10px;">↑ Y (م)</div>
                </div>
                
                <!-- Objects will be dynamically added here -->
            </div>
        </div>

        <!-- Right Panel - Data and Analysis -->
        <div class="right-panel">
            <div class="panel-section">
                <div class="panel-title">
                    <i class="fas fa-chart-line"></i>
                    البيانات الحية
                </div>
                <div class="data-display" id="liveData">
                    <div class="data-row">
                        <span class="data-label">الوقت:</span>
                        <span class="data-value" id="timeValue">0.0 ث</span>
                    </div>
                    <div class="data-row">
                        <span class="data-label">السرعة الأفقية:</span>
                        <span class="data-value" id="vxValue">0.0 م/ث</span>
                    </div>
                    <div class="data-row">
                        <span class="data-label">السرعة الرأسية:</span>
                        <span class="data-value" id="vyValue">0.0 م/ث</span>
                    </div>
                    <div class="data-row">
                        <span class="data-label">الموضع الأفقي:</span>
                        <span class="data-value" id="xValue">0.0 م</span>
                    </div>
                    <div class="data-row">
                        <span class="data-label">الموضع الرأسي:</span>
                        <span class="data-value" id="yValue">0.0 م</span>
                    </div>
                    <div class="data-row">
                        <span class="data-label">الطاقة الحركية:</span>
                        <span class="data-value" id="kineticEnergy">0.0 J</span>
                    </div>
                    <div class="data-row">
                        <span class="data-label">الطاقة الكامنة:</span>
                        <span class="data-value" id="potentialEnergy">0.0 J</span>
                    </div>
                </div>
            </div>

            <div class="panel-section">
                <div class="panel-title">
                    <i class="fas fa-calculator"></i>
                    النتائج المحسوبة
                </div>
                <div class="data-display" id="calculatedResults">
                    <div class="data-row">
                        <span class="data-label">المدى الأقصى:</span>
                        <span class="data-value" id="maxRange">0.0 م</span>
                    </div>
                    <div class="data-row">
                        <span class="data-label">الارتفاع الأقصى:</span>
                        <span class="data-value" id="maxHeight">0.0 م</span>
                    </div>
                    <div class="data-row">
                        <span class="data-label">زمن الطيران:</span>
                        <span class="data-value" id="flightTime">0.0 ث</span>
                    </div>
                </div>
            </div>

            <div class="panel-section">
                <div class="panel-title">
                    <i class="fas fa-chart-area"></i>
                    الرسوم البيانية
                </div>
                <div class="graph-container" id="velocityGraph">
                    <div style="font-size: 0.8rem; color: #a0aec0; margin-bottom: 10px;">السرعة مقابل الوقت</div>
                    <div class="graph-axis x"></div>
                    <div class="graph-axis y"></div>
                </div>
                
                <div class="graph-container" id="positionGraph">
                    <div style="font-size: 0.8rem; color: #a0aec0; margin-bottom: 10px;">الموضع مقابل الوقت</div>
                    <div class="graph-axis x"></div>
                    <div class="graph-axis y"></div>
                </div>
            </div>

            <div class="panel-section">
                <div class="panel-title">
                    <i class="fas fa-table"></i>
                    جدول البيانات
                </div>
                <div style="max-height: 200px; overflow-y: auto; font-size: 0.8rem;">
                    <table style="width: 100%; border-collapse: collapse;" id="dataTable">
                        <thead>
                            <tr style="background: rgba(66, 153, 225, 0.2);">
                                <th style="padding: 8px; border: 1px solid #4a5568;">الوقت (ث)</th>
                                <th style="padding: 8px; border: 1px solid #4a5568;">X (م)</th>
                                <th style="padding: 8px; border: 1px solid #4a5568;">Y (م)</th>
                                <th style="padding: 8px; border: 1px solid #4a5568;">V (م/ث)</th>
                            </tr>
                        </thead>
                        <tbody id="dataTableBody">
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Footer Bar -->
        <div class="footer-bar">
            <div class="status-indicator">
                <div class="status-dot"></div>
                <span>متصل - جاهز للتجريب</span>
            </div>
            <div style="font-size: 0.9rem; color: #a0aec0;">
                <span id="experimentStatus">تجربة: حركة المقذوفات</span>
                <span style="margin: 0 15px;">|</span>
                <span>الإصدار: 2.1.0</span>
            </div>
        </div>
    </div>

    <script src="js/physics-engine.js"></script>
    <script>
        // Global variables
        let currentExperiment = 'projectile';
        let isSimulationRunning = false;
        let simulationSpeed = 1;
        let animationFrameId = null;
        let experimentData = [];
        let startTime = 0;

        // Initialize the lab
        function initializeLab() {
            const urlParams = new URLSearchParams(window.location.search);
            const labType = urlParams.get('lab');
            const experimentType = urlParams.get('experiment');
            
            if (experimentType) {
                selectExperiment(experimentType);
            } else if (labType) {
                updateLabTitle(labType);
            }
            
            setupSimulationCanvas();
            updateProjectileParams();
        }

        function updateLabTitle(labType) {
            const titles = {
                'mechanics': 'مختبر الميكانيكا',
                'thermodynamics': 'مختبر الديناميكا الحرارية',
                'optics': 'مختبر البصريات',
                'modern-physics': 'مختبر الفيزياء الحديثة'
            };
            
            document.getElementById('labTitle').textContent = titles[labType] || 'مختبر الفيزياء التفاعلي';
        }

        function selectExperiment(experimentType) {
            // Update active experiment
            document.querySelectorAll('.experiment-option').forEach(btn => {
                btn.classList.remove('active');
            });
            
            event?.target?.classList.add('active');
            currentExperiment = experimentType;
            
            // Update experiment status
            const experimentNames = {
                'projectile': 'حركة المقذوفات',
                'pendulum': 'البندول البسيط',
                'collision': 'التصادمات',
                'spring': 'نظام نابض-كتلة',
                'newton': 'قوانين نيوتن'
            };
            
            document.getElementById('experimentStatus').textContent = 
                `تجربة: ${experimentNames[experimentType]}`;
            
            // Show/hide relevant controls
            showExperimentControls(experimentType);
            
            // Reset simulation
            resetExperiment();
            
            // Update help text
            updateHelpText(experimentType);
        }

        function showExperimentControls(experimentType) {
            // Hide all controls first
            document.querySelectorAll('.experiment-controls').forEach(control => {
                control.style.display = 'none';
            });
            
            // Show relevant controls
            const controlsId = experimentType + 'Controls';
            const controls = document.getElementById(controlsId);
            if (controls) {
                controls.style.display = 'block';
            }
        }

        function updateHelpText(experimentType) {
            const helpTexts = {
                'projectile': 'اضبط السرعة الابتدائية وزاوية الإطلاق لاستكشاف مسار المقذوف. راقب كيف تؤثر المتغيرات المختلفة على المدى والارتفاع.',
                'pendulum': 'اسحب البندول لزاوية معينة وأطلقه لدراسة الحركة التوافقية. لاحظ تأثير الطول والكتلة على الدورة.',
                'collision': 'اضبط سرعة وكتلة الكرات لدراسة التصادمات المرنة وغير المرنة. راقب حفظ الزخم والطاقة.',
                'spring': 'اسحب الكتلة المرتبطة بالنابض وأطلقها لدراسة الحركة التوافقية البسيطة. راقب تحولات الطاقة.',
                'newton': 'طبق قوى مختلفة على الأجسام لاستكشاف قوانين نيوتن الثلاثة للحركة.'
            };
            
            document.getElementById('experimentHelp').textContent = helpTexts[experimentType];
        }

        function setupSimulationCanvas() {
            const canvas = document.getElementById('simulationCanvas');
            canvas.addEventListener('click', handleCanvasClick);
            canvas.addEventListener('mousemove', handleCanvasMouseMove);
        }

        function handleCanvasClick(event) {
            if (currentExperiment === 'projectile' && !isSimulationRunning) {
                const rect = event.target.getBoundingClientRect();
                const x = event.clientX - rect.left;
                const y = event.clientY - rect.top;
                
                // Set launch position
                updateLaunchPosition(x, y);
            }
        }

        function handleCanvasMouseMove(event) {
            // Show trajectory preview for projectile motion
            if (currentExperiment === 'projectile' && !isSimulationRunning) {
                showTrajectoryPreview(event);
            }
        }

        function updateProjectileParams() {
            const velocity = document.getElementById('initialVelocity').value;
            const angle = document.getElementById('launchAngle').value;
            const mass = document.getElementById('projectileMass').value;
            const gravity = document.getElementById('gravity').value;
            
            document.getElementById('velocityValue').textContent = velocity;
            document.getElementById('angleValue').textContent = angle;
            document.getElementById('massValue').textContent = mass;
            document.getElementById('gravityValue').textContent = gravity;
            
            // Update calculated results
            calculateProjectileResults(parseFloat(velocity), parseFloat(angle), parseFloat(gravity));
        }

        function calculateProjectileResults(v0, angle, g) {
            const angleRad = angle * Math.PI / 180;
            const v0x = v0 * Math.cos(angleRad);
            const v0y = v0 * Math.sin(angleRad);
            
            const flightTime = (2 * v0y) / g;
            const maxRange = (v0 * v0 * Math.sin(2 * angleRad)) / g;
            const maxHeight = (v0y * v0y) / (2 * g);
            
            document.getElementById('flightTime').textContent = flightTime.toFixed(2) + ' ث';
            document.getElementById('maxRange').textContent = maxRange.toFixed(2) + ' م';
            document.getElementById('maxHeight').textContent = maxHeight.toFixed(2) + ' م';
        }

        function toggleSimulation() {
            if (isSimulationRunning) {
                pauseSimulation();
            } else {
                startSimulation();
            }
        }

        function startSimulation() {
            isSimulationRunning = true;
            startTime = Date.now();
            experimentData = [];
            
            document.getElementById('playButton').innerHTML = '<i class="fas fa-pause"></i> إيقاف مؤقت';
            
            // Clear previous objects
            clearSimulationObjects();
            
            // Start the appropriate simulation
            if (currentExperiment === 'projectile') {
                startProjectileSimulation();
            } else if (currentExperiment === 'pendulum') {
                startPendulumSimulation();
            }
            
            // Start animation loop
            animationFrameId = requestAnimationFrame(updateSimulation);
        }

        function pauseSimulation() {
            isSimulationRunning = false;
            document.getElementById('playButton').innerHTML = '<i class="fas fa-play"></i> تشغيل التجربة';
            
            if (animationFrameId) {
                cancelAnimationFrame(animationFrameId);
            }
        }

        function stopSimulation() {
            pauseSimulation();
            resetExperiment();
        }

        function resetExperiment() {
            pauseSimulation();
            clearSimulationObjects();
            clearDataTable();
            clearGraphs();
            resetDataDisplay();
        }

        function clearSimulationObjects() {
            const canvas = document.getElementById('simulationCanvas');
            const objects = canvas.querySelectorAll('.simulation-object, .trajectory-path, .particle-trace');
            objects.forEach(obj => obj.remove());
        }

        function clearDataTable() {
            document.getElementById('dataTableBody').innerHTML = '';
        }

        function clearGraphs() {
            document.querySelectorAll('.graph-line').forEach(line => line.remove());
        }

        function resetDataDisplay() {
            document.getElementById('timeValue').textContent = '0.0 ث';
            document.getElementById('vxValue').textContent = '0.0 م/ث';
            document.getElementById('vyValue').textContent = '0.0 م/ث';
            document.getElementById('xValue').textContent = '0.0 م';
            document.getElementById('yValue').textContent = '0.0 م';
            document.getElementById('kineticEnergy').textContent = '0.0 J';
            document.getElementById('potentialEnergy').textContent = '0.0 J';
        }

        function startProjectileSimulation() {
            const canvas = document.getElementById('simulationCanvas');
            const projectile = document.createElement('div');
            projectile.className = 'simulation-object projectile';
            projectile.style.width = '20px';
            projectile.style.height = '20px';
            projectile.style.left = '50px';
            projectile.style.bottom = '50px';
            
            canvas.appendChild(projectile);
            
            // Store initial conditions
            projectile.dataset.startTime = Date.now();
            projectile.dataset.initialVelocity = document.getElementById('initialVelocity').value;
            projectile.dataset.launchAngle = document.getElementById('launchAngle').value;
            projectile.dataset.mass = document.getElementById('projectileMass').value;
            projectile.dataset.gravity = document.getElementById('gravity').value;
        }

        function updateSimulation() {
            if (!isSimulationRunning) return;
            
            const currentTime = (Date.now() - startTime) / 1000 * simulationSpeed;
            
            if (currentExperiment === 'projectile') {
                updateProjectilePosition(currentTime);
            }
            
            // Continue animation
            animationFrameId = requestAnimationFrame(updateSimulation);
        }

        function updateProjectilePosition(t) {
            const projectile = document.querySelector('.projectile');
            if (!projectile) return;
            
            const v0 = parseFloat(projectile.dataset.initialVelocity);
            const angle = parseFloat(projectile.dataset.launchAngle) * Math.PI / 180;
            const mass = parseFloat(projectile.dataset.mass);
            const g = parseFloat(projectile.dataset.gravity);
            
            const v0x = v0 * Math.cos(angle);
            const v0y = v0 * Math.sin(angle);
            
            const x = v0x * t;
            const y = v0y * t - 0.5 * g * t * t;
            
            const vx = v0x;
            const vy = v0y - g * t;
            
            // Update position (convert to pixels)
            const scale = 10; // 10 pixels per meter
            projectile.style.left = (50 + x * scale) + 'px';
            projectile.style.bottom = (50 + y * scale) + 'px';
            
            // Update data display
            document.getElementById('timeValue').textContent = t.toFixed(2) + ' ث';
            document.getElementById('vxValue').textContent = vx.toFixed(2) + ' م/ث';
            document.getElementById('vyValue').textContent = vy.toFixed(2) + ' م/ث';
            document.getElementById('xValue').textContent = x.toFixed(2) + ' م';
            document.getElementById('yValue').textContent = y.toFixed(2) + ' م';
            
            // Calculate energies
            const speed = Math.sqrt(vx * vx + vy * vy);
            const kineticEnergy = 0.5 * mass * speed * speed;
            const potentialEnergy = mass * g * y;
            
            document.getElementById('kineticEnergy').textContent = kineticEnergy.toFixed(2) + ' J';
            document.getElementById('potentialEnergy').textContent = potentialEnergy.toFixed(2) + ' J';
            
            // Add particle trace
            createParticleTrace(projectile.style.left, projectile.style.bottom);
            
            // Add data to table
            addDataToTable(t, x, y, speed);
            
            // Store data for graphs
            experimentData.push({ t, x, y, vx, vy, speed });
            
            // Check if projectile hit ground
            if (y <= 0 && t > 0.1) {
                stopSimulation();
            }
        }

        function createParticleTrace(x, y) {
            const canvas = document.getElementById('simulationCanvas');
            const trace = document.createElement('div');
            trace.className = 'particle-trace';
            trace.style.left = x;
            trace.style.bottom = y;
            
            canvas.appendChild(trace);
            
            // Remove trace after animation
            setTimeout(() => trace.remove(), 2000);
        }

        function addDataToTable(t, x, y, v) {
            const tbody = document.getElementById('dataTableBody');
            const row = document.createElement('tr');
            row.innerHTML = `
                <td style="padding: 4px; border: 1px solid #4a5568;">${t.toFixed(2)}</td>
                <td style="padding: 4px; border: 1px solid #4a5568;">${x.toFixed(2)}</td>
                <td style="padding: 4px; border: 1px solid #4a5568;">${y.toFixed(2)}</td>
                <td style="padding: 4px; border: 1px solid #4a5568;">${v.toFixed(2)}</td>
            `;
            tbody.appendChild(row);
            
            // Scroll to bottom
            tbody.parentElement.scrollTop = tbody.parentElement.scrollHeight;
        }

        function updateSimulationSpeed() {
            simulationSpeed = parseFloat(document.getElementById('simulationSpeed').value);
            document.getElementById('speedValue').textContent = simulationSpeed + 'x';
        }

        function saveExperiment() {
            const experimentState = {
                type: currentExperiment,
                data: experimentData,
                parameters: getCurrentParameters(),
                timestamp: new Date().toISOString()
            };
            
            localStorage.setItem('savedExperiment', JSON.stringify(experimentState));
            alert('تم حفظ التجربة بنجاح!');
        }

        function getCurrentParameters() {
            if (currentExperiment === 'projectile') {
                return {
                    initialVelocity: document.getElementById('initialVelocity').value,
                    launchAngle: document.getElementById('launchAngle').value,
                    mass: document.getElementById('projectileMass').value,
                    gravity: document.getElementById('gravity').value
                };
            }
            return {};
        }

        function exportData() {
            if (experimentData.length === 0) {
                alert('لا توجد بيانات للتصدير. يرجى تشغيل التجربة أولاً.');
                return;
            }
            
            const csvContent = generateCSV();
            downloadCSV(csvContent, `${currentExperiment}_data.csv`);
        }

        function generateCSV() {
            let csv = 'الوقت (ث),الموضع X (م),الموضع Y (م),السرعة X (م/ث),السرعة Y (م/ث),السرعة الكلية (م/ث)\n';
            
            experimentData.forEach(row => {
                csv += `${row.t.toFixed(3)},${row.x.toFixed(3)},${row.y.toFixed(3)},${row.vx.toFixed(3)},${row.vy.toFixed(3)},${row.speed.toFixed(3)}\n`;
            });
            
            return csv;
        }

        function downloadCSV(content, filename) {
            const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            
            if (link.download !== undefined) {
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', filename);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }
        }

        function goBack() {
            window.location.href = 'virtual-labs.html';
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', initializeLab);
    </script>
</body>
</html>