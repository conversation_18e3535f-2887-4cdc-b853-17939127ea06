<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Interactive Virtual Simulation Hub</title>
    <script type="module" crossorigin src="/assets/index-CMZxBlkM.js"></script>
    <link rel="stylesheet" crossorigin href="/assets/index-DCpAoTnH.css">
    <style>
      /* Quick navigation overlay */
      .nav-overlay {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 1000;
        background: rgba(37, 99, 235, 0.9);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        padding: 15px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
      }
      
      .nav-overlay a {
        display: block;
        color: white;
        text-decoration: none;
        padding: 8px 12px;
        margin: 5px 0;
        border-radius: 8px;
        transition: all 0.3s ease;
      }
      
      .nav-overlay a:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: translateX(5px);
      }
      
      .nav-overlay .nav-title {
        font-weight: bold;
        font-size: 1.1rem;
        margin-bottom: 10px;
        text-align: center;
      }
    </style>
  </head>
  <body>
    <div id="root"></div>
    
    <!-- Quick Navigation -->
    <div class="nav-overlay">
      <div class="nav-title">🎓 SimLab HUB</div>
      <a href="index.html">🏠 Main App</a>
      <a href="virtual-labs.html">🧪 Virtual Labs</a>
      <a href="lectures.html">📹 Interactive Lectures</a>
      <a href="advanced-lectures.html">🚀 Advanced Lectures</a>
      <a href="learning-module-template.html">📚 Learning Modules</a>
    </div>
  </body>
</html>
