<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المختبرات الافتراضية - Virtual Labs | SimLab HUB</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: '<PERSON><PERSON><PERSON>', Arial, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            color: #2d3748;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .header h1 {
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .labs-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .lab-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .lab-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .lab-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
        }

        .lab-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            margin: 0 auto 20px;
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }

        .lab-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 15px;
            text-align: center;
            color: #2d3748;
        }

        .lab-description {
            color: #4a5568;
            line-height: 1.6;
            margin-bottom: 20px;
            text-align: center;
        }

        .experiments-list {
            background: #f7fafc;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .experiments-list h4 {
            color: #2d3748;
            margin-bottom: 10px;
            font-size: 1rem;
        }

        .experiments-list ul {
            list-style: none;
            padding: 0;
        }

        .experiments-list li {
            padding: 5px 0;
            color: #4a5568;
            font-size: 0.9rem;
            position: relative;
            padding-right: 20px;
        }

        .experiments-list li::before {
            content: '⚗️';
            position: absolute;
            right: 0;
            top: 5px;
        }

        .lab-button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .lab-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .features-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            backdrop-filter: blur(10px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }

        .features-title {
            text-align: center;
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 30px;
            color: #2d3748;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
        }

        .feature-item {
            background: linear-gradient(135deg, #f7fafc, #edf2f7);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            border: 2px solid #e2e8f0;
            transition: all 0.3s ease;
        }

        .feature-item:hover {
            border-color: #667eea;
            transform: translateY(-5px);
        }

        .feature-icon {
            font-size: 2.5rem;
            color: #667eea;
            margin-bottom: 15px;
        }

        .feature-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 10px;
            color: #2d3748;
        }

        .feature-description {
            color: #4a5568;
            line-height: 1.5;
            font-size: 0.9rem;
        }

        .quick-access {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(10px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }

        .quick-access h3 {
            text-align: center;
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            color: #2d3748;
        }

        .quick-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .quick-btn {
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .quick-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(72, 187, 120, 0.3);
        }

        .navigation-overlay {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .navigation-overlay a {
            display: block;
            color: #2d3748;
            text-decoration: none;
            padding: 8px 12px;
            margin: 5px 0;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .navigation-overlay a:hover {
            background: #667eea;
            color: white;
            transform: translateX(5px);
        }

        .stats-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            color: #667eea;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #4a5568;
            font-size: 0.9rem;
            font-weight: 500;
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .labs-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
            
            .navigation-overlay {
                position: relative;
                margin-bottom: 20px;
            }
        }

        .loading-simulation {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            z-index: 2000;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="loading-simulation" id="loadingSimulation">
        <div style="text-align: center;">
            <div class="spinner"></div>
            <div>جاري تحميل المختبر...</div>
        </div>
    </div>

    <!-- Navigation Overlay -->
    <div class="navigation-overlay">
        <div style="font-weight: bold; margin-bottom: 10px; text-align: center;">🧪 SimLab HUB</div>
        <a href="index.html">🏠 الصفحة الرئيسية</a>
        <a href="lectures.html">📹 المحاضرات</a>
        <a href="learning-module-template.html">📚 وحدات التعلم</a>
        <a href="#mechanics-lab">⚛️ مختبر الميكانيكا</a>
        <a href="#thermodynamics-lab">🌡️ الديناميكا الحرارية</a>
        <a href="#optics-lab">🔬 البصريات</a>
        <a href="#modern-physics-lab">🚀 الفيزياء الحديثة</a>
    </div>

    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>🧪 المختبرات الافتراضية</h1>
            <p>استكشف عالم الفيزياء من خلال تجارب تفاعلية متقدمة</p>
        </div>

        <!-- Statistics -->
        <div class="stats-section">
            <div class="stat-card">
                <div class="stat-number">65+</div>
                <div class="stat-label">تجربة تفاعلية</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">5</div>
                <div class="stat-label">مختبرات متخصصة</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">100%</div>
                <div class="stat-label">آمن ومجاني</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">24/7</div>
                <div class="stat-label">متاح دائماً</div>
            </div>
        </div>

        <!-- Labs Grid -->
        <div class="labs-grid">
            <!-- Mechanics Lab -->
            <div class="lab-card" id="mechanics-lab">
                <div class="lab-icon">
                    <i class="fas fa-globe"></i>
                </div>
                <h2 class="lab-title">مختبر الميكانيكا</h2>
                <p class="lab-description">
                    استكشف قوانين نيوتن وحفظ الطاقة والحركة في عالم ثلاثي الأبعاد تفاعلي
                </p>
                <div class="experiments-list">
                    <h4>التجارب المتاحة:</h4>
                    <ul>
                        <li>حركة المقذوفات</li>
                        <li>قوانين نيوتن للحركة</li>
                        <li>حفظ الطاقة</li>
                        <li>الحركة التوافقية البسيطة</li>
                        <li>التصادمات المرنة وغير المرنة</li>
                    </ul>
                </div>
                <button class="lab-button" onclick="openLab('mechanics')">
                    دخول المختبر
                    <i class="fas fa-arrow-left" style="margin-right: 10px;"></i>
                </button>
            </div>

            <!-- Chemistry Lab -->
            <div class="lab-card" id="chemistry-lab">
                <div class="lab-icon">
                    <i class="fas fa-flask"></i>
                </div>
                <h2 class="lab-title">مختبر الكيمياء</h2>
                <p class="lab-description">
                    تجارب كيميائية شاملة مع أدوات افتراضية متقدمة وبروتوكولات السلامة
                </p>
                <div class="experiments-list">
                    <h4>التجارب المتاحة:</h4>
                    <ul>
                        <li>معايرة الأحماض والقواعد</li>
                        <li>قياس السعرات الحرارية</li>
                        <li>تصنيع المركبات</li>
                        <li>حركية التفاعل الكيميائي</li>
                        <li>التحليل الطيفي</li>
                    </ul>
                </div>
                <button class="lab-button" onclick="openLab('chemistry')">
                    دخول المختبر
                    <i class="fas fa-arrow-left" style="margin-right: 10px;"></i>
                </button>
            </div>

            <!-- Thermodynamics Lab -->
            <div class="lab-card" id="thermodynamics-lab">
                <div class="lab-icon">
                    <i class="fas fa-thermometer-half"></i>
                </div>
                <h2 class="lab-title">مختبر الديناميكا الحرارية</h2>
                <p class="lab-description">
                    اكتشف قوانين الغازات وانتقال الحرارة وتغيرات الطور في بيئة محاكاة واقعية
                </p>
                <div class="experiments-list">
                    <h4>التجارب المتاحة:</h4>
                    <ul>
                        <li>قوانين الغازات المثالية</li>
                        <li>انتقال الحرارة (التوصيل، الحمل، الإشعاع)</li>
                        <li>تغيرات الطور</li>
                        <li>الآلات الحرارية</li>
                        <li>الانتروبيا والطاقة الداخلية</li>
                    </ul>
                </div>
                <button class="lab-button" onclick="openLab('thermodynamics')">
                    دخول المختبر
                    <i class="fas fa-arrow-left" style="margin-right: 10px;"></i>
                </button>
            </div>

            <!-- Optics Lab -->
            <div class="lab-card" id="optics-lab">
                <div class="lab-icon">
                    <i class="fas fa-eye"></i>
                </div>
                <h2 class="lab-title">مختبر البصريات</h2>
                <p class="lab-description">
                    اكتشف سلوك الضوء والعدسات والمرايا من خلال تجارب بصرية متقدمة
                </p>
                <div class="experiments-list">
                    <h4>التجارب المتاحة:</h4>
                    <ul>
                        <li>الانعكاس والانكسار</li>
                        <li>قانون سنيل</li>
                        <li>الحيود والتداخل</li>
                        <li>المناشير والطيف الضوئي</li>
                        <li>المجاهر والتلسكوبات</li>
                    </ul>
                </div>
                <button class="lab-button" onclick="openLab('optics')">
                    دخول المختبر
                    <i class="fas fa-arrow-left" style="margin-right: 10px;"></i>
                </button>
            </div>

            <!-- Modern Physics Lab -->
            <div class="lab-card" id="modern-physics-lab">
                <div class="lab-icon">
                    <i class="fas fa-atom"></i>
                </div>
                <h2 class="lab-title">مختبر الفيزياء الحديثة</h2>
                <p class="lab-description">
                    استكشف الظواهر الكمية والنسبية في عالم الذرات والجسيمات دون الذرية
                </p>
                <div class="experiments-list">
                    <h4>التجارب المتاحة:</h4>
                    <ul>
                        <li>التأثير الكهروضوئي</li>
                        <li>التفكك الإشعاعي</li>
                        <li>نماذج الذرة</li>
                        <li>التأثير الكومبتوني</li>
                        <li>النسبية الخاصة</li>
                    </ul>
                </div>
                <button class="lab-button" onclick="openLab('modern-physics')">
                    دخول المختبر
                    <i class="fas fa-arrow-left" style="margin-right: 10px;"></i>
                </button>
            </div>
        </div>

        <!-- Features Section -->
        <div class="features-section">
            <h2 class="features-title">🌟 مميزات المختبرات الافتراضية</h2>
            <div class="features-grid">
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-3d-rotation"></i>
                    </div>
                    <h3 class="feature-title">محاكاة ثلاثية الأبعاد</h3>
                    <p class="feature-description">تجارب واقعية في بيئة ثلاثية الأبعاد تفاعلية</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h3 class="feature-title">تحليل البيانات</h3>
                    <p class="feature-description">أدوات متقدمة لجمع وتحليل البيانات التجريبية</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-save"></i>
                    </div>
                    <h3 class="feature-title">حفظ التجارب</h3>
                    <p class="feature-description">احفظ واستعد تجاربك في أي وقت</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-share-alt"></i>
                    </div>
                    <h3 class="feature-title">مشاركة النتائج</h3>
                    <p class="feature-description">شارك نتائجك مع المعلمين والزملاء</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3 class="feature-title">آمن تماماً</h3>
                    <p class="feature-description">لا مخاطر أو مواد خطيرة - تجريب آمن 100%</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-infinity"></i>
                    </div>
                    <h3 class="feature-title">تجارب لا محدودة</h3>
                    <p class="feature-description">كرر التجارب بمعاملات مختلفة دون قيود</p>
                </div>
            </div>
        </div>

        <!-- Quick Access -->
        <div class="quick-access">
            <h3>🚀 وصول سريع</h3>
            <div class="quick-buttons">
                <button class="quick-btn" onclick="openExperiment('projectile')">
                    <i class="fas fa-rocket"></i>
                    حركة المقذوفات
                </button>
                <button class="quick-btn" onclick="openExperiment('pendulum')">
                    <i class="fas fa-clock"></i>
                    البندول البسيط
                </button>
                <button class="quick-btn" onclick="openExperiment('titration')">
                    <i class="fas fa-flask"></i>
                    معايرة كيميائية
                </button>
                <button class="quick-btn" onclick="openExperiment('snells-law')">
                    <i class="fas fa-lightbulb"></i>
                    قانون سنيل
                </button>
                <button class="quick-btn" onclick="openExperiment('photoelectric')">
                    <i class="fas fa-sun"></i>
                    التأثير الكهروضوئي
                </button>
                <button class="quick-btn" onclick="window.location.href='virtual-instruments.html'">
                    <i class="fas fa-tools"></i>
                    الأدوات الافتراضية
                </button>
            </div>
        </div>
    </div>

    <script>
        // Lab navigation functions
        function openLab(labType) {
            showLoading();
            setTimeout(() => {
                if (labType === 'chemistry') {
                    window.location.href = 'chemistry-lab.html';
                } else {
                    window.location.href = `physics-lab.html?lab=${labType}`;
                }
            }, 1500);
        }

        function openExperiment(experimentType) {
            showLoading();
            setTimeout(() => {
                if (experimentType === 'titration' || experimentType === 'calorimetry' || experimentType === 'synthesis') {
                    window.location.href = `chemistry-lab.html?experiment=${experimentType}`;
                } else {
                    window.location.href = `physics-lab.html?experiment=${experimentType}`;
                }
            }, 1500);
        }

        function showLoading() {
            document.getElementById('loadingSimulation').style.display = 'flex';
        }

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'center'
                    });
                }
            });
        });

        // Add entrance animations
        function addEntranceAnimations() {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            });

            document.querySelectorAll('.lab-card, .feature-item, .stat-card').forEach(el => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(30px)';
                el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(el);
            });
        }

        // Initialize animations when page loads
        document.addEventListener('DOMContentLoaded', function() {
            addEntranceAnimations();
        });

        // Add particle background effect
        function createParticles() {
            const particlesContainer = document.createElement('div');
            particlesContainer.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                pointer-events: none;
                z-index: -1;
            `;

            for (let i = 0; i < 50; i++) {
                const particle = document.createElement('div');
                particle.style.cssText = `
                    position: absolute;
                    width: 2px;
                    height: 2px;
                    background: rgba(255, 255, 255, 0.3);
                    border-radius: 50%;
                    animation: float ${3 + Math.random() * 4}s infinite linear;
                    left: ${Math.random() * 100}%;
                    top: ${Math.random() * 100}%;
                `;
                particlesContainer.appendChild(particle);
            }

            document.body.appendChild(particlesContainer);
        }

        // Add CSS animation for particles
        const style = document.createElement('style');
        style.textContent = `
            @keyframes float {
                0% {
                    transform: translateY(0px) rotate(0deg);
                    opacity: 1;
                }
                100% {
                    transform: translateY(-100vh) rotate(360deg);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);

        // Initialize particles
        createParticles();

        // Add interactive hover effects
        document.querySelectorAll('.lab-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.background = 'rgba(255, 255, 255, 1)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.background = 'rgba(255, 255, 255, 0.95)';
            });
        });

        // Add click ripple effect
        function addRippleEffect(element) {
            element.addEventListener('click', function(e) {
                const ripple = document.createElement('span');
                const rect = this.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;
                
                ripple.style.cssText = `
                    position: absolute;
                    width: ${size}px;
                    height: ${size}px;
                    left: ${x}px;
                    top: ${y}px;
                    background: rgba(255, 255, 255, 0.6);
                    border-radius: 50%;
                    transform: scale(0);
                    animation: ripple 0.6s ease-out;
                    pointer-events: none;
                `;
                
                this.style.position = 'relative';
                this.style.overflow = 'hidden';
                this.appendChild(ripple);
                
                setTimeout(() => ripple.remove(), 600);
            });
        }

        // Add ripple to buttons
        document.querySelectorAll('.lab-button, .quick-btn').forEach(addRippleEffect);

        // Add ripple animation CSS
        const rippleStyle = document.createElement('style');
        rippleStyle.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(2);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(rippleStyle);
    </script>
</body>
</html>