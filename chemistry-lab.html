<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Virtual Chemistry Laboratory - SimLab HUB</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="css/virtual-instruments.css" rel="stylesheet">
    <link href="css/virtual-lab-bench.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #2d3748;
            overflow-x: hidden;
        }

        .chemistry-lab {
            display: grid;
            grid-template-areas: 
                "header header header"
                "experiments equipment workspace"
                "safety controls results";
            grid-template-columns: 300px 300px 1fr;
            grid-template-rows: 80px 1fr 250px;
            height: 100vh;
            gap: 10px;
            padding: 10px;
        }

        .lab-header {
            grid-area: header;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 0 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .lab-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: #2563eb;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .lab-status {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9rem;
            font-weight: 600;
        }

        .status-led {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #e53e3e;
            box-shadow: 0 0 10px rgba(229, 62, 62, 0.6);
        }

        .status-led.safe {
            background: #48bb78;
            box-shadow: 0 0 10px rgba(72, 187, 120, 0.6);
        }

        .panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            overflow-y: auto;
        }

        .experiments-panel {
            grid-area: experiments;
        }

        .equipment-panel {
            grid-area: equipment;
        }

        .workspace-panel {
            grid-area: workspace;
        }

        .safety-panel {
            grid-area: safety;
        }

        .controls-panel {
            grid-area: controls;
        }

        .results-panel {
            grid-area: results;
        }

        .panel-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e2e8f0;
        }

        .experiment-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 10px;
        }

        .experiment-card {
            background: linear-gradient(145deg, #f7fafc, #edf2f7);
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .experiment-card:hover {
            border-color: #4299e1;
            background: linear-gradient(145deg, #ebf8ff, #bee3f8);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(66, 153, 225, 0.2);
        }

        .experiment-card.active {
            border-color: #3182ce;
            background: linear-gradient(145deg, #3182ce, #2c5282);
            color: white;
        }

        .experiment-title {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .experiment-description {
            font-size: 0.85rem;
            opacity: 0.8;
            line-height: 1.4;
        }

        .difficulty-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #48bb78;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 600;
        }

        .difficulty-badge.medium {
            background: #ed8936;
        }

        .difficulty-badge.hard {
            background: #e53e3e;
        }

        .equipment-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
            gap: 10px;
        }

        .equipment-item {
            background: linear-gradient(145deg, #f7fafc, #edf2f7);
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            padding: 10px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .equipment-item:hover {
            border-color: #4299e1;
            transform: scale(1.05);
        }

        .equipment-item.selected {
            border-color: #3182ce;
            background: linear-gradient(145deg, #3182ce, #2c5282);
            color: white;
        }

        .equipment-icon {
            font-size: 2rem;
            margin-bottom: 5px;
            color: #4299e1;
        }

        .equipment-item.selected .equipment-icon {
            color: white;
        }

        .equipment-name {
            font-size: 0.8rem;
            font-weight: 600;
        }

        .workspace {
            background: radial-gradient(circle at center, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 10px;
            height: 100%;
            border: 2px solid #dee2e6;
            position: relative;
            overflow: hidden;
        }

        .lab-bench {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: linear-gradient(145deg, #8b4513, #654321);
            border-radius: 0 0 8px 8px;
        }

        .safety-alert {
            background: linear-gradient(145deg, #fed7d7, #feb2b2);
            border: 2px solid #fc8181;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .safety-alert.warning {
            background: linear-gradient(145deg, #fefcbf, #faf089);
            border-color: #f6e05e;
        }

        .safety-alert.info {
            background: linear-gradient(145deg, #bee3f8, #90cdf4);
            border-color: #63b3ed;
        }

        .safety-title {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .safety-message {
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .control-section {
            background: #f7fafc;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            border: 2px solid #e2e8f0;
        }

        .control-title {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 10px;
            color: #2d3748;
        }

        .reaction-display {
            background: #1a202c;
            color: #00ff41;
            padding: 15px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 1.1rem;
            margin-bottom: 15px;
            border: 2px solid #4a5568;
            text-align: center;
        }

        .results-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }

        .result-card {
            background: #f7fafc;
            border-radius: 8px;
            padding: 12px;
            border: 2px solid #e2e8f0;
            text-align: center;
        }

        .result-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 5px;
        }

        .result-label {
            font-size: 0.8rem;
            color: #4a5568;
            font-weight: 500;
        }

        .action-btn {
            background: linear-gradient(145deg, #4299e1, #3182ce);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 10px;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(66, 153, 225, 0.3);
            width: 100%;
            margin-bottom: 10px;
        }

        .action-btn:hover {
            background: linear-gradient(145deg, #3182ce, #2c5282);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(66, 153, 225, 0.4);
        }

        .action-btn.danger {
            background: linear-gradient(145deg, #e53e3e, #c53030);
        }

        .action-btn.danger:hover {
            background: linear-gradient(145deg, #c53030, #9c2626);
        }

        .action-btn.success {
            background: linear-gradient(145deg, #48bb78, #38a169);
        }

        .action-btn.success:hover {
            background: linear-gradient(145deg, #38a169, #2f855a);
        }

        @media (max-width: 1200px) {
            .chemistry-lab {
                grid-template-areas: 
                    "header header"
                    "experiments equipment"
                    "workspace workspace"
                    "safety controls"
                    "results results";
                grid-template-columns: 1fr 1fr;
                grid-template-rows: 80px auto 300px auto auto;
            }
        }

        @media (max-width: 768px) {
            .chemistry-lab {
                grid-template-areas: 
                    "header"
                    "experiments"
                    "equipment"
                    "workspace"
                    "safety"
                    "controls"
                    "results";
                grid-template-columns: 1fr;
                grid-template-rows: 80px auto auto 250px auto auto auto;
            }
            
            .equipment-grid {
                grid-template-columns: repeat(4, 1fr);
            }
            
            .results-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="chemistry-lab">
        <!-- Header -->
        <div class="lab-header">
            <div class="lab-title">
                <i class="fas fa-flask"></i>
                Virtual Chemistry Laboratory
            </div>
            <div class="lab-status">
                <div class="status-indicator">
                    <div class="status-led safe" id="safety-status"></div>
                    <span>Lab Safety</span>
                </div>
                <div class="status-indicator">
                    <div class="status-led" id="experiment-status"></div>
                    <span>Experiment</span>
                </div>
            </div>
        </div>

        <!-- Experiments Panel -->
        <div class="panel experiments-panel">
            <h3 class="panel-title">
                <i class="fas fa-vial"></i>
                Available Experiments
            </h3>
            
            <div class="experiment-grid" id="experiment-list">
                <!-- Experiments will be populated here -->
            </div>
        </div>

        <!-- Equipment Panel -->
        <div class="panel equipment-panel">
            <h3 class="panel-title">
                <i class="fas fa-tools"></i>
                Laboratory Equipment
            </h3>
            
            <div class="equipment-grid" id="equipment-list">
                <!-- Equipment will be populated here -->
            </div>
        </div>

        <!-- Workspace Panel -->
        <div class="panel workspace-panel">
            <h3 class="panel-title">
                <i class="fas fa-desktop"></i>
                Laboratory Workspace
            </h3>

            <div id="virtual-lab-bench-container">
                <!-- Virtual lab bench will be loaded here -->
            </div>
        </div>

        <!-- Safety Panel -->
        <div class="panel safety-panel">
            <h3 class="panel-title">
                <i class="fas fa-shield-alt"></i>
                Safety Protocols
            </h3>
            
            <div id="safety-alerts">
                <!-- Safety alerts will be displayed here -->
            </div>
        </div>

        <!-- Controls Panel -->
        <div class="panel controls-panel">
            <h3 class="panel-title">
                <i class="fas fa-sliders-h"></i>
                Experiment Controls
            </h3>
            
            <div id="experiment-controls">
                <!-- Controls will be populated here -->
            </div>
        </div>

        <!-- Results Panel -->
        <div class="panel results-panel">
            <h3 class="panel-title">
                <i class="fas fa-chart-bar"></i>
                Results & Analysis
            </h3>
            
            <div class="reaction-display" id="reaction-equation">
                Select an experiment to begin
            </div>
            
            <div class="results-grid" id="results-display">
                <!-- Results will be displayed here -->
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/virtual-instruments.js"></script>
    <script src="js/data-logger.js"></script>
    <script src="js/variable-controls.js"></script>
    <script src="js/chemistry-experiments.js"></script>
    <script src="js/virtual-lab-bench.js"></script>
    
    <script>
        // Chemistry Lab Controller
        class ChemistryLab {
            constructor() {
                this.currentExperiment = null;
                this.selectedEquipment = [];
                this.safetyStatus = 'safe';
                this.experimentRunning = false;
                
                this.init();
            }

            init() {
                this.loadExperiments();
                this.loadEquipment();
                this.setupEventListeners();
                this.updateSafetyStatus();
            }

            loadExperiments() {
                const experiments = [
                    {
                        id: 'titration',
                        title: 'Acid-Base Titration',
                        description: 'Determine the concentration of an unknown acid using a standard base solution.',
                        difficulty: 'easy',
                        category: 'analytical'
                    },
                    {
                        id: 'calorimetry',
                        title: 'Calorimetry',
                        description: 'Measure the heat of reaction for various chemical processes.',
                        difficulty: 'medium',
                        category: 'thermodynamics'
                    },
                    {
                        id: 'synthesis',
                        title: 'Aspirin Synthesis',
                        description: 'Synthesize aspirin from salicylic acid and acetic anhydride.',
                        difficulty: 'hard',
                        category: 'organic'
                    },
                    {
                        id: 'kinetics',
                        title: 'Chemical Kinetics',
                        description: 'Investigate factors affecting reaction rates.',
                        difficulty: 'medium',
                        category: 'kinetics'
                    },
                    {
                        id: 'equilibrium',
                        title: 'Chemical Equilibrium',
                        description: 'Explore Le Chatelier\'s principle with various conditions.',
                        difficulty: 'medium',
                        category: 'equilibrium'
                    }
                ];

                const container = document.getElementById('experiment-list');
                container.innerHTML = '';

                experiments.forEach(exp => {
                    const card = document.createElement('div');
                    card.className = 'experiment-card';
                    card.dataset.experiment = exp.id;
                    
                    card.innerHTML = `
                        <div class="difficulty-badge ${exp.difficulty}">${exp.difficulty}</div>
                        <div class="experiment-title">${exp.title}</div>
                        <div class="experiment-description">${exp.description}</div>
                    `;
                    
                    card.addEventListener('click', () => this.selectExperiment(exp));
                    container.appendChild(card);
                });
            }

            loadEquipment() {
                const equipment = [
                    { id: 'beaker', name: 'Beaker', icon: 'fas fa-flask' },
                    { id: 'burette', name: 'Burette', icon: 'fas fa-vial' },
                    { id: 'pipette', name: 'Pipette', icon: 'fas fa-eyedropper' },
                    { id: 'thermometer', name: 'Thermometer', icon: 'fas fa-thermometer-half' },
                    { id: 'balance', name: 'Balance', icon: 'fas fa-balance-scale' },
                    { id: 'stirrer', name: 'Stirrer', icon: 'fas fa-sync' },
                    { id: 'ph-meter', name: 'pH Meter', icon: 'fas fa-tint' },
                    { id: 'calorimeter', name: 'Calorimeter', icon: 'fas fa-fire' }
                ];

                const container = document.getElementById('equipment-list');
                container.innerHTML = '';

                equipment.forEach(item => {
                    const element = document.createElement('div');
                    element.className = 'equipment-item';
                    element.dataset.equipment = item.id;
                    
                    element.innerHTML = `
                        <div class="equipment-icon">
                            <i class="${item.icon}"></i>
                        </div>
                        <div class="equipment-name">${item.name}</div>
                    `;
                    
                    element.addEventListener('click', () => this.toggleEquipment(item.id, element));
                    container.appendChild(element);
                });
            }

            selectExperiment(experiment) {
                // Update UI
                document.querySelectorAll('.experiment-card').forEach(card => {
                    card.classList.remove('active');
                });
                document.querySelector(`[data-experiment="${experiment.id}"]`).classList.add('active');

                this.currentExperiment = experiment;
                this.loadExperimentControls(experiment);
                this.updateReactionDisplay(experiment);
                this.checkSafety(experiment);
            }

            toggleEquipment(equipmentId, element) {
                if (element.classList.contains('selected')) {
                    element.classList.remove('selected');
                    this.selectedEquipment = this.selectedEquipment.filter(id => id !== equipmentId);
                } else {
                    element.classList.add('selected');
                    this.selectedEquipment.push(equipmentId);
                }
            }

            loadExperimentControls(experiment) {
                const container = document.getElementById('experiment-controls');
                container.innerHTML = '';

                // Create control sections based on experiment type
                switch (experiment.id) {
                    case 'titration':
                        this.createTitrationControls(container);
                        break;
                    case 'calorimetry':
                        this.createCalorimetryControls(container);
                        break;
                    case 'synthesis':
                        this.createSynthesisControls(container);
                        break;
                    default:
                        this.createGenericControls(container);
                }
            }

            createTitrationControls(container) {
                container.innerHTML = `
                    <div class="control-section">
                        <div class="control-title">Titrant Concentration</div>
                        <input type="range" min="0.01" max="1.0" step="0.01" value="0.1" 
                               oninput="chemLab.updateParameter('titrant_conc', this.value)">
                        <div>0.1 M NaOH</div>
                    </div>
                    
                    <div class="control-section">
                        <div class="control-title">Sample Volume</div>
                        <input type="range" min="10" max="100" step="5" value="25" 
                               oninput="chemLab.updateParameter('sample_volume', this.value)">
                        <div>25.0 mL</div>
                    </div>
                    
                    <button class="action-btn" onclick="chemLab.startTitration()">
                        <i class="fas fa-play"></i> Start Titration
                    </button>
                    
                    <button class="action-btn success" onclick="chemLab.addDrop()">
                        <i class="fas fa-plus"></i> Add Drop
                    </button>
                `;
            }

            createCalorimetryControls(container) {
                container.innerHTML = `
                    <div class="control-section">
                        <div class="control-title">Initial Temperature</div>
                        <input type="range" min="15" max="35" step="0.1" value="25" 
                               oninput="chemLab.updateParameter('initial_temp', this.value)">
                        <div>25.0 °C</div>
                    </div>
                    
                    <div class="control-section">
                        <div class="control-title">Reactant Mass</div>
                        <input type="range" min="0.1" max="10" step="0.1" value="1.0" 
                               oninput="chemLab.updateParameter('mass', this.value)">
                        <div>1.0 g</div>
                    </div>
                    
                    <button class="action-btn" onclick="chemLab.startReaction()">
                        <i class="fas fa-fire"></i> Start Reaction
                    </button>
                `;
            }

            createSynthesisControls(container) {
                container.innerHTML = `
                    <div class="control-section">
                        <div class="control-title">Reaction Temperature</div>
                        <input type="range" min="60" max="100" step="1" value="80" 
                               oninput="chemLab.updateParameter('reaction_temp', this.value)">
                        <div>80 °C</div>
                    </div>
                    
                    <div class="control-section">
                        <div class="control-title">Reaction Time</div>
                        <input type="range" min="5" max="60" step="5" value="30" 
                               oninput="chemLab.updateParameter('reaction_time', this.value)">
                        <div>30 minutes</div>
                    </div>
                    
                    <button class="action-btn danger" onclick="chemLab.heatReaction()">
                        <i class="fas fa-fire"></i> Heat Mixture
                    </button>
                    
                    <button class="action-btn" onclick="chemLab.coolReaction()">
                        <i class="fas fa-snowflake"></i> Cool & Crystallize
                    </button>
                `;
            }

            createGenericControls(container) {
                container.innerHTML = `
                    <div class="control-section">
                        <div class="control-title">Temperature</div>
                        <input type="range" min="0" max="100" step="1" value="25" 
                               oninput="chemLab.updateParameter('temperature', this.value)">
                        <div>25 °C</div>
                    </div>
                    
                    <button class="action-btn" onclick="chemLab.startExperiment()">
                        <i class="fas fa-play"></i> Start Experiment
                    </button>
                `;
            }

            updateReactionDisplay(experiment) {
                const display = document.getElementById('reaction-equation');
                
                const equations = {
                    titration: 'HCl + NaOH → NaCl + H₂O',
                    calorimetry: 'CaO + H₂O → Ca(OH)₂ + Heat',
                    synthesis: 'C₇H₆O₃ + (CH₃CO)₂O → C₉H₈O₄ + CH₃COOH',
                    kinetics: 'A + B → C + D',
                    equilibrium: 'N₂ + 3H₂ ⇌ 2NH₃'
                };
                
                display.textContent = equations[experiment.id] || 'Select an experiment';
            }

            checkSafety(experiment) {
                const safetyContainer = document.getElementById('safety-alerts');
                safetyContainer.innerHTML = '';

                // Add safety alerts based on experiment
                const safetyInfo = this.getSafetyInfo(experiment);
                
                safetyInfo.forEach(alert => {
                    const alertElement = document.createElement('div');
                    alertElement.className = `safety-alert ${alert.type}`;
                    
                    alertElement.innerHTML = `
                        <div class="safety-title">
                            <i class="${alert.icon}"></i>
                            ${alert.title}
                        </div>
                        <div class="safety-message">${alert.message}</div>
                    `;
                    
                    safetyContainer.appendChild(alertElement);
                });
            }

            getSafetyInfo(experiment) {
                const safetyData = {
                    titration: [
                        {
                            type: 'warning',
                            icon: 'fas fa-exclamation-triangle',
                            title: 'Caustic Warning',
                            message: 'NaOH is caustic. Wear safety goggles and gloves.'
                        }
                    ],
                    synthesis: [
                        {
                            type: 'danger',
                            icon: 'fas fa-fire',
                            title: 'Heat Hazard',
                            message: 'High temperature reaction. Use proper heating equipment.'
                        },
                        {
                            type: 'warning',
                            icon: 'fas fa-skull-crossbones',
                            title: 'Toxic Vapors',
                            message: 'Acetic anhydride produces toxic vapors. Use fume hood.'
                        }
                    ],
                    calorimetry: [
                        {
                            type: 'info',
                            icon: 'fas fa-info-circle',
                            title: 'Exothermic Reaction',
                            message: 'This reaction releases heat. Monitor temperature carefully.'
                        }
                    ]
                };

                return safetyData[experiment.id] || [];
            }

            updateSafetyStatus() {
                const statusLed = document.getElementById('safety-status');
                statusLed.className = `status-led ${this.safetyStatus}`;
            }

            setupEventListeners() {
                // Add any additional event listeners here
            }

            // Experiment methods (to be implemented)
            updateParameter(param, value) {
                console.log(`Updated ${param} to ${value}`);
            }

            startTitration() {
                console.log('Starting titration...');
                this.experimentRunning = true;
                document.getElementById('experiment-status').classList.add('safe');
            }

            addDrop() {
                console.log('Adding titrant drop...');
            }

            startReaction() {
                console.log('Starting reaction...');
                this.experimentRunning = true;
                document.getElementById('experiment-status').classList.add('safe');
            }

            heatReaction() {
                console.log('Heating reaction mixture...');
                this.safetyStatus = 'warning';
                this.updateSafetyStatus();
            }

            coolReaction() {
                console.log('Cooling reaction...');
                this.safetyStatus = 'safe';
                this.updateSafetyStatus();
            }

            startExperiment() {
                console.log('Starting experiment...');
                this.experimentRunning = true;
                document.getElementById('experiment-status').classList.add('safe');
            }
        }

        // Initialize Chemistry Lab
        let chemLab, labBench;
        document.addEventListener('DOMContentLoaded', function() {
            chemLab = new ChemistryLab();

            // Initialize virtual lab bench
            labBench = new VirtualLabBench('virtual-lab-bench-container');
        });
    </script>
</body>
</html>
