/* Footer Styles for SimLab HUB */

.simlab-footer {
    background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
    color: #e2e8f0;
    padding: 40px 0 20px;
    margin-top: 50px;
    border-top: 4px solid #4299e1;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: 40px;
    align-items: start;
}

.footer-section {
    display: flex;
    flex-direction: column;
}

/* Author Section */
.author-section {
    grid-column: 1;
}

.author-info {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.author-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: #bee3f8;
    line-height: 1.4;
}

.copyright-info {
    font-size: 0.95rem;
    color: #a0aec0;
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
}

.email-link {
    color: #4299e1;
    text-decoration: none;
    transition: color 0.3s ease;
}

.email-link:hover {
    color: #63b3ed;
    text-decoration: underline;
}

.phone-info {
    font-size: 0.9rem;
    color: #a0aec0;
}

/* Links Section */
.links-section {
    grid-column: 2;
}

.footer-links {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.footer-link {
    color: #cbd5e0;
    text-decoration: none;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    padding: 8px 0;
    border-bottom: 1px solid transparent;
}

.footer-link:hover {
    color: #4299e1;
    border-bottom-color: #4299e1;
    transform: translateX(5px);
}

/* Social Section */
.social-section {
    grid-column: 3;
    align-items: flex-end;
}

.social-links {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    justify-content: flex-end;
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: rgba(66, 153, 225, 0.1);
    border: 2px solid rgba(66, 153, 225, 0.3);
    border-radius: 50%;
    color: #4299e1;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 1.1rem;
}

.social-link:hover {
    background: #4299e1;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(66, 153, 225, 0.4);
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 10px;
    justify-content: flex-end;
    font-size: 1.2rem;
    font-weight: 600;
    color: #bee3f8;
}

.footer-logo i {
    font-size: 1.5rem;
    color: #4299e1;
}

/* Footer Bottom */
.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: 30px;
    padding-top: 20px;
}

.footer-bottom-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.85rem;
    color: #a0aec0;
}

.powered-by {
    font-style: italic;
}

.last-updated {
    font-size: 0.8rem;
}

/* Modal Styles */
.footer-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    padding: 20px;
}

.modal-overlay {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 15px;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    animation: modal-appear 0.3s ease;
}

@keyframes modal-appear {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.modal-header {
    padding: 25px 30px 15px;
    border-bottom: 2px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    color: #2d3748;
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #4a5568;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.modal-close:hover {
    background: #f7fafc;
    color: #2d3748;
}

.modal-body {
    padding: 25px 30px;
    color: #2d3748;
    line-height: 1.6;
}

.modal-body h3 {
    color: #2d3748;
    font-size: 1.3rem;
    margin-bottom: 15px;
    font-weight: 600;
}

.modal-body h4 {
    color: #4a5568;
    font-size: 1.1rem;
    margin: 20px 0 10px;
    font-weight: 600;
}

.modal-body p {
    margin-bottom: 12px;
    color: #4a5568;
}

.modal-body ul {
    margin: 10px 0 15px 20px;
    color: #4a5568;
}

.modal-body li {
    margin-bottom: 5px;
}

.contact-card {
    background: #f7fafc;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 15px;
    border-left: 4px solid #4299e1;
}

.contact-card h4 {
    color: #2d3748;
    margin-top: 0;
    margin-bottom: 10px;
}

.contact-card p {
    margin-bottom: 8px;
}

.contact-card a {
    color: #4299e1;
    text-decoration: none;
}

.contact-card a:hover {
    text-decoration: underline;
}

.modal-footer {
    padding: 15px 30px 25px;
    border-top: 1px solid #e2e8f0;
    display: flex;
    justify-content: flex-end;
}

.footer-btn {
    background: linear-gradient(145deg, #4299e1, #3182ce);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.footer-btn:hover {
    background: linear-gradient(145deg, #3182ce, #2c5282);
    transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .footer-content {
        grid-template-columns: 1fr;
        gap: 30px;
        text-align: center;
    }
    
    .author-section,
    .links-section,
    .social-section {
        grid-column: 1;
    }
    
    .social-links {
        justify-content: center;
    }
    
    .footer-logo {
        justify-content: center;
    }
    
    .footer-bottom-content {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
    
    .modal-content {
        margin: 10px;
        max-width: calc(100vw - 20px);
    }
    
    .modal-header,
    .modal-body,
    .modal-footer {
        padding-left: 20px;
        padding-right: 20px;
    }
}

@media (max-width: 480px) {
    .simlab-footer {
        padding: 30px 0 15px;
    }
    
    .footer-content {
        padding: 0 15px;
        gap: 20px;
    }
    
    .author-name {
        font-size: 1rem;
    }
    
    .copyright-info,
    .phone-info {
        font-size: 0.85rem;
    }
    
    .social-links {
        gap: 10px;
    }
    
    .social-link {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }
}

/* RTL Support */
[dir="rtl"] .footer-content {
    direction: rtl;
}

[dir="rtl"] .footer-link:hover {
    transform: translateX(-5px);
}

[dir="rtl"] .social-links {
    justify-content: flex-start;
}

[dir="rtl"] .footer-logo {
    justify-content: flex-start;
    flex-direction: row-reverse;
}

[dir="rtl"] .footer-bottom-content {
    flex-direction: row-reverse;
}

[dir="rtl"] .modal-header {
    flex-direction: row-reverse;
}

[dir="rtl"] .modal-footer {
    justify-content: flex-start;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .modal-content {
        background: #2d3748;
        color: #e2e8f0;
    }
    
    .modal-header {
        border-bottom-color: #4a5568;
    }
    
    .modal-header h2 {
        color: #e2e8f0;
    }
    
    .modal-close {
        color: #a0aec0;
    }
    
    .modal-close:hover {
        background: #4a5568;
        color: #e2e8f0;
    }
    
    .modal-body {
        color: #e2e8f0;
    }
    
    .modal-body h3,
    .modal-body h4 {
        color: #bee3f8;
    }
    
    .modal-body p,
    .modal-body li {
        color: #cbd5e0;
    }
    
    .contact-card {
        background: #4a5568;
    }
    
    .modal-footer {
        border-top-color: #4a5568;
    }
}

/* Print Styles */
@media print {
    .simlab-footer {
        background: none;
        color: #000;
        border-top: 2px solid #000;
    }
    
    .social-links,
    .footer-logo {
        display: none;
    }
    
    .footer-modal {
        display: none;
    }
}
