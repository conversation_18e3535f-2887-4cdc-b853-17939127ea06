<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الأدوات الافتراضية - Virtual Instruments | SimLab HUB</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Tajawal', Arial, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #e2e8f0;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .instruments-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .header h1 {
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }

        .instruments-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 50px;
        }

        .instrument-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .instrument-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .instrument-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            border-color: rgba(102, 126, 234, 0.3);
        }

        .instrument-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2.5rem;
            margin: 0 auto 20px;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }

        .instrument-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 15px;
            text-align: center;
            color: #90cdf4;
        }

        .instrument-description {
            color: #a0aec0;
            line-height: 1.6;
            margin-bottom: 25px;
            text-align: center;
        }

        .instrument-features {
            background: rgba(45, 55, 72, 0.5);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .features-list {
            list-style: none;
            padding: 0;
        }

        .features-list li {
            padding: 5px 0;
            color: #e2e8f0;
            font-size: 0.9rem;
            position: relative;
            padding-right: 20px;
        }

        .features-list li::before {
            content: '✓';
            position: absolute;
            right: 0;
            top: 5px;
            color: #48bb78;
            font-weight: bold;
        }

        .instrument-button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .instrument-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        /* Instrument Interface Overlay */
        .instrument-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.9);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            backdrop-filter: blur(5px);
        }

        .instrument-window {
            background: linear-gradient(135deg, #1a202c, #2d3748);
            border: 1px solid #4a5568;
            border-radius: 20px;
            padding: 30px;
            max-width: 800px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            position: relative;
        }

        .instrument-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 1px solid #4a5568;
        }

        .instrument-close {
            background: #f56565;
            border: none;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            transition: all 0.3s ease;
        }

        .instrument-close:hover {
            background: #e53e3e;
            transform: scale(1.1);
        }

        /* Oscilloscope Styles */
        .oscilloscope-screen {
            background: #000;
            border: 3px solid #4a5568;
            border-radius: 10px;
            width: 100%;
            height: 300px;
            position: relative;
            margin-bottom: 20px;
            overflow: hidden;
        }

        .oscilloscope-grid {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0.3;
            background-image: 
                linear-gradient(#00ff00 1px, transparent 1px),
                linear-gradient(90deg, #00ff00 1px, transparent 1px);
            background-size: 40px 30px;
        }

        .oscilloscope-trace {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        .oscilloscope-controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .control-panel {
            background: rgba(45, 55, 72, 0.7);
            border: 1px solid #4a5568;
            border-radius: 10px;
            padding: 15px;
        }

        .control-title {
            font-size: 1rem;
            font-weight: 600;
            color: #90cdf4;
            margin-bottom: 10px;
        }

        .control-group {
            margin-bottom: 15px;
        }

        .control-label {
            display: block;
            font-size: 0.9rem;
            color: #a0aec0;
            margin-bottom: 5px;
        }

        .control-slider {
            width: 100%;
            height: 6px;
            border-radius: 3px;
            background: #4a5568;
            outline: none;
            transition: all 0.3s ease;
        }

        .control-slider::-webkit-slider-thumb {
            appearance: none;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: #4299e1;
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(66, 153, 225, 0.3);
        }

        .control-value {
            font-size: 0.8rem;
            color: #e2e8f0;
            margin-top: 5px;
        }

        /* Voltmeter Styles */
        .voltmeter-display {
            background: #000;
            border: 3px solid #4a5568;
            border-radius: 10px;
            width: 100%;
            height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            margin-bottom: 20px;
        }

        .voltage-reading {
            font-size: 3rem;
            font-weight: bold;
            color: #00ff00;
            text-shadow: 0 0 10px #00ff00;
            font-family: 'Courier New', monospace;
        }

        .voltage-unit {
            font-size: 1.5rem;
            color: #00ff00;
            margin-right: 10px;
        }

        .probe-connections {
            display: flex;
            justify-content: space-around;
            margin: 20px 0;
        }

        .probe {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .probe.positive {
            background: linear-gradient(135deg, #f56565, #e53e3e);
            color: white;
        }

        .probe.negative {
            background: linear-gradient(135deg, #2d3748, #1a202c);
            color: white;
            border: 2px solid #4a5568;
        }

        .probe:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }

        /* Spectrophotometer Styles */
        .spectro-sample-holder {
            background: linear-gradient(135deg, #e2e8f0, #cbd5e0);
            border: 2px solid #4a5568;
            border-radius: 10px;
            width: 100%;
            height: 100px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            position: relative;
        }

        .sample-cuvette {
            width: 30px;
            height: 60px;
            background: linear-gradient(180deg, transparent 20%, var(--sample-color, #4299e1) 20%);
            border: 2px solid #2d3748;
            border-radius: 4px;
            position: relative;
        }

        .wavelength-display {
            background: #000;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            font-size: 1.5rem;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
            margin-bottom: 15px;
        }

        .absorbance-display {
            background: #000;
            color: #ff6600;
            font-family: 'Courier New', monospace;
            font-size: 2rem;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
            margin-bottom: 20px;
        }

        /* Balance Styles */
        .balance-platform {
            background: linear-gradient(135deg, #e2e8f0, #cbd5e0);
            border: 2px solid #4a5568;
            border-radius: 10px;
            width: 100%;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            position: relative;
        }

        .balance-display {
            background: #000;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            font-size: 2.5rem;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
            margin-bottom: 20px;
        }

        .balance-controls {
            display: flex;
            justify-content: space-around;
            margin-top: 20px;
        }

        .balance-button {
            background: linear-gradient(135deg, #4299e1, #3182ce);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .balance-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(66, 153, 225, 0.3);
        }

        /* pH Meter Styles */
        .ph-probe {
            width: 20px;
            height: 150px;
            background: linear-gradient(180deg, #4a5568, #2d3748);
            border-radius: 10px;
            margin: 0 auto 20px;
            position: relative;
        }

        .ph-probe::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 6px;
            height: 20px;
            background: #90cdf4;
            border-radius: 3px;
        }

        .ph-display {
            background: #000;
            color: var(--ph-color, #00ff00);
            font-family: 'Courier New', monospace;
            font-size: 3rem;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 20px;
            border: 2px solid #4a5568;
        }

        .ph-scale {
            display: flex;
            height: 30px;
            border-radius: 15px;
            overflow: hidden;
            margin-bottom: 20px;
            position: relative;
        }

        .ph-segment {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            font-weight: bold;
            color: white;
        }

        .ph-indicator {
            position: absolute;
            top: -5px;
            width: 20px;
            height: 40px;
            background: #fff;
            border: 2px solid #2d3748;
            border-radius: 10px;
            transition: left 0.3s ease;
        }

        /* Thermometer Styles */
        .thermometer {
            width: 30px;
            height: 200px;
            background: linear-gradient(180deg, 
                #f56565 0%, 
                #f56565 var(--temp-fill, 25%), 
                #e2e8f0 var(--temp-fill, 25%), 
                #e2e8f0 100%);
            border: 3px solid #4a5568;
            border-radius: 15px;
            margin: 0 auto 20px;
            position: relative;
        }

        .thermometer::before {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 40px;
            height: 40px;
            background: #f56565;
            border-radius: 50%;
            border: 3px solid #4a5568;
        }

        .temperature-display {
            background: #000;
            color: var(--temp-color, #00ff00);
            font-family: 'Courier New', monospace;
            font-size: 2.5rem;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 20px;
        }

        .temperature-scale {
            display: flex;
            justify-content: space-between;
            padding: 0 20px;
            font-size: 0.8rem;
            color: #a0aec0;
        }

        @media (max-width: 768px) {
            .instruments-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .instrument-window {
                width: 95%;
                padding: 20px;
            }
            
            .oscilloscope-controls {
                grid-template-columns: 1fr;
            }
        }

        .data-logger {
            background: rgba(45, 55, 72, 0.7);
            border: 1px solid #4a5568;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }

        .logger-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #90cdf4;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.9rem;
        }

        .data-table th,
        .data-table td {
            padding: 8px;
            border: 1px solid #4a5568;
            text-align: center;
        }

        .data-table th {
            background: rgba(66, 153, 225, 0.2);
            color: #90cdf4;
        }

        .export-button {
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 15px;
            cursor: pointer;
            font-size: 0.9rem;
            margin-top: 10px;
            transition: all 0.3s ease;
        }

        .export-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(72, 187, 120, 0.3);
        }
    </style>
</head>
<body>
    <div class="instruments-container">
        <!-- Header -->
        <div class="header">
            <h1>🛠️ الأدوات الافتراضية</h1>
            <p>مجموعة شاملة من الأدوات العلمية المتقدمة لجميع احتياجاتك التجريبية</p>
        </div>

        <!-- Instruments Grid -->
        <div class="instruments-grid">
            <!-- Oscilloscope -->
            <div class="instrument-card">
                <div class="instrument-icon">
                    <i class="fas fa-wave-square"></i>
                </div>
                <h3 class="instrument-title">راسم الذبذبات</h3>
                <p class="instrument-description">
                    جهاز متقدم لعرض وتحليل الإشارات الكهربائية والموجات بدقة عالية
                </p>
                <div class="instrument-features">
                    <ul class="features-list">
                        <li>نطاق ترددي واسع حتى 100 MHz</li>
                        <li>قنوات متعددة للقياس المتزامن</li>
                        <li>تحليل طيفي للإشارات</li>
                        <li>حفظ وتصدير البيانات</li>
                        <li>مؤشرات رقمية دقيقة</li>
                    </ul>
                </div>
                <button class="instrument-button" onclick="openInstrument('oscilloscope')">
                    <i class="fas fa-play"></i>
                    تشغيل الجهاز
                </button>
            </div>

            <!-- Voltmeter -->
            <div class="instrument-card">
                <div class="instrument-icon">
                    <i class="fas fa-bolt"></i>
                </div>
                <h3 class="instrument-title">الفولتميتر الرقمي</h3>
                <p class="instrument-description">
                    مقياس دقيق للجهد الكهربائي مع إمكانيات قياس متقدمة
                </p>
                <div class="instrument-features">
                    <ul class="features-list">
                        <li>دقة قياس عالية (±0.1%)</li>
                        <li>نطاقات قياس متعددة</li>
                        <li>شاشة رقمية واضحة</li>
                        <li>حماية من التيار الزائد</li>
                        <li>أطراف قياس آمنة</li>
                    </ul>
                </div>
                <button class="instrument-button" onclick="openInstrument('voltmeter')">
                    <i class="fas fa-play"></i>
                    تشغيل الجهاز
                </button>
            </div>

            <!-- Spectrophotometer -->
            <div class="instrument-card">
                <div class="instrument-icon">
                    <i class="fas fa-eye"></i>
                </div>
                <h3 class="instrument-title">مطياف الضوء</h3>
                <p class="instrument-description">
                    جهاز تحليل طيفي متقدم لقياس امتصاص الضوء وتحديد التراكيز
                </p>
                <div class="instrument-features">
                    <ul class="features-list">
                        <li>نطاق طيفي واسع (200-800 nm)</li>
                        <li>تطبيق قانون بير تلقائياً</li>
                        <li>منحنيات المعايرة</li>
                        <li>تحليل العينات المجهولة</li>
                        <li>قاعدة بيانات للطيف</li>
                    </ul>
                </div>
                <button class="instrument-button" onclick="openInstrument('spectrophotometer')">
                    <i class="fas fa-play"></i>
                    تشغيل الجهاز
                </button>
            </div>

            <!-- pH Meter -->
            <div class="instrument-card">
                <div class="instrument-icon">
                    <i class="fas fa-tint"></i>
                </div>
                <h3 class="instrument-title">مقياس الحموضة</h3>
                <p class="instrument-description">
                    مقياس دقيق لقياس درجة الحموضة والقاعدية في المحاليل
                </p>
                <div class="instrument-features">
                    <ul class="features-list">
                        <li>دقة قياس ±0.01 pH</li>
                        <li>تعويض تلقائي لدرجة الحرارة</li>
                        <li>مجس عالي الجودة</li>
                        <li>معايرة تلقائية</li>
                        <li>ذاكرة لحفظ القراءات</li>
                    </ul>
                </div>
                <button class="instrument-button" onclick="openInstrument('phmeter')">
                    <i class="fas fa-play"></i>
                    تشغيل الجهاز
                </button>
            </div>

            <!-- Analytical Balance -->
            <div class="instrument-card">
                <div class="instrument-icon">
                    <i class="fas fa-balance-scale"></i>
                </div>
                <h3 class="instrument-title">الميزان التحليلي</h3>
                <p class="instrument-description">
                    ميزان عالي الدقة لقياس الكتل بدقة تصل إلى أجزاء الملليجرام
                </p>
                <div class="instrument-features">
                    <ul class="features-list">
                        <li>دقة قياس 0.1 ملليجرام</li>
                        <li>حماية من تيارات الهواء</li>
                        <li>معايرة داخلية</li>
                        <li>وزن تفاضلي</li>
                        <li>وضع العد للقطع</li>
                    </ul>
                </div>
                <button class="instrument-button" onclick="openInstrument('balance')">
                    <i class="fas fa-play"></i>
                    تشغيل الجهاز
                </button>
            </div>

            <!-- Thermometer -->
            <div class="instrument-card">
                <div class="instrument-icon">
                    <i class="fas fa-thermometer-half"></i>
                </div>
                <h3 class="instrument-title">مقياس الحرارة الرقمي</h3>
                <p class="instrument-description">
                    مقياس حرارة دقيق مع مجسات متعددة لمختلف التطبيقات
                </p>
                <div class="instrument-features">
                    <ul class="features-list">
                        <li>نطاق واسع (-50 إلى 500°C)</li>
                        <li>مجسات قابلة للتبديل</li>
                        <li>استجابة سريعة</li>
                        <li>تسجيل مستمر للبيانات</li>
                        <li>إنذارات الحد الأقصى/الأدنى</li>
                    </ul>
                </div>
                <button class="instrument-button" onclick="openInstrument('thermometer')">
                    <i class="fas fa-play"></i>
                    تشغيل الجهاز
                </button>
            </div>

            <!-- Calipers -->
            <div class="instrument-card">
                <div class="instrument-icon">
                    <i class="fas fa-ruler"></i>
                </div>
                <h3 class="instrument-title">الكاليبر الرقمي</h3>
                <p class="instrument-description">
                    أداة قياس دقيقة للأطوال والأقطار الداخلية والخارجية
                </p>
                <div class="instrument-features">
                    <ul class="features-list">
                        <li>دقة قياس 0.01 مم</li>
                        <li>قياس داخلي وخارجي</li>
                        <li>قياس العمق</li>
                        <li>شاشة رقمية كبيرة</li>
                        <li>تحويل بين الوحدات</li>
                    </ul>
                </div>
                <button class="instrument-button" onclick="openInstrument('calipers')">
                    <i class="fas fa-play"></i>
                    تشغيل الجهاز
                </button>
            </div>

            <!-- Multimeter -->
            <div class="instrument-card">
                <div class="instrument-icon">
                    <i class="fas fa-gauge-high"></i>
                </div>
                <h3 class="instrument-title">المالتيميتر</h3>
                <p class="instrument-description">
                    جهاز قياس متعدد الوظائف للتيار والجهد والمقاومة
                </p>
                <div class="instrument-features">
                    <ul class="features-list">
                        <li>قياس جهد AC/DC</li>
                        <li>قياس التيار والمقاومة</li>
                        <li>اختبار الصمامات</li>
                        <li>قياس التردد والدورة</li>
                        <li>اختبار الاستمرارية</li>
                    </ul>
                </div>
                <button class="instrument-button" onclick="openInstrument('multimeter')">
                    <i class="fas fa-play"></i>
                    تشغيل الجهاز
                </button>
            </div>
        </div>
    </div>

    <!-- Instrument Overlays -->
    <!-- Oscilloscope -->
    <div class="instrument-overlay" id="oscilloscopeOverlay">
        <div class="instrument-window">
            <div class="instrument-header">
                <h3 style="color: #90cdf4;">راسم الذبذبات - Oscilloscope</h3>
                <button class="instrument-close" onclick="closeInstrument('oscilloscope')">✕</button>
            </div>
            
            <div class="oscilloscope-screen">
                <div class="oscilloscope-grid"></div>
                <canvas class="oscilloscope-trace" id="oscilloscopeCanvas" width="700" height="300"></canvas>
            </div>
            
            <div class="oscilloscope-controls">
                <div class="control-panel">
                    <div class="control-title">التحكم في المحور الزمني</div>
                    <div class="control-group">
                        <label class="control-label">قاعدة الزمن (ms/div)</label>
                        <input type="range" class="control-slider" id="timebaseSlider" 
                               min="0.1" max="10" value="1" step="0.1" oninput="updateTimebase()">
                        <div class="control-value" id="timebaseValue">1.0 ms/div</div>
                    </div>
                </div>
                
                <div class="control-panel">
                    <div class="control-title">التحكم في الجهد</div>
                    <div class="control-group">
                        <label class="control-label">حساسية الجهد (V/div)</label>
                        <input type="range" class="control-slider" id="voltageSlider" 
                               min="0.1" max="5" value="1" step="0.1" oninput="updateVoltage()">
                        <div class="control-value" id="voltageValue">1.0 V/div</div>
                    </div>
                </div>
                
                <div class="control-panel">
                    <div class="control-title">إعدادات الإشارة</div>
                    <div class="control-group">
                        <label class="control-label">التردد (Hz)</label>
                        <input type="range" class="control-slider" id="frequencySlider" 
                               min="1" max="1000" value="60" oninput="updateFrequency()">
                        <div class="control-value" id="frequencyValue">60 Hz</div>
                    </div>
                    
                    <div class="control-group">
                        <label class="control-label">السعة (V)</label>
                        <input type="range" class="control-slider" id="amplitudeSlider" 
                               min="0.1" max="10" value="2" step="0.1" oninput="updateAmplitude()">
                        <div class="control-value" id="amplitudeValue">2.0 V</div>
                    </div>
                </div>
            </div>
            
            <div class="data-logger">
                <div class="logger-title">
                    <i class="fas fa-chart-line"></i>
                    سجل البيانات
                </div>
                <table class="data-table" id="oscilloscopeData">
                    <thead>
                        <tr>
                            <th>الوقت (ms)</th>
                            <th>السعة (V)</th>
                            <th>التردد (Hz)</th>
                            <th>الطور</th>
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
                <button class="export-button" onclick="exportOscilloscopeData()">
                    <i class="fas fa-download"></i> تصدير البيانات
                </button>
            </div>
        </div>
    </div>

    <!-- pH Meter -->
    <div class="instrument-overlay" id="phmeterOverlay">
        <div class="instrument-window">
            <div class="instrument-header">
                <h3 style="color: #90cdf4;">مقياس الحموضة - pH Meter</h3>
                <button class="instrument-close" onclick="closeInstrument('phmeter')">✕</button>
            </div>
            
            <div class="ph-probe"></div>
            
            <div class="ph-display" id="phDisplay">7.00</div>
            
            <div class="ph-scale">
                <div class="ph-indicator" id="phIndicator"></div>
                <div class="ph-segment" style="background: #e53e3e;">0</div>
                <div class="ph-segment" style="background: #f56565;">2</div>
                <div class="ph-segment" style="background: #ed8936;">4</div>
                <div class="ph-segment" style="background: #ecc94b;">6</div>
                <div class="ph-segment" style="background: #48bb78;">7</div>
                <div class="ph-segment" style="background: #4299e1;">8</div>
                <div class="ph-segment" style="background: #667eea;">10</div>
                <div class="ph-segment" style="background: #9f7aea;">12</div>
                <div class="ph-segment" style="background: #805ad5;">14</div>
            </div>
            
            <div class="control-panel">
                <div class="control-title">محاكاة القياس</div>
                <div class="control-group">
                    <label class="control-label">نوع المحلول</label>
                    <select style="width: 100%; padding: 8px; border-radius: 5px; background: #2d3748; color: #e2e8f0; border: 1px solid #4a5568;" onchange="changeSolution(this.value)">
                        <option value="7.0">الماء المقطر (pH 7.0)</option>
                        <option value="1.0">حمض الهيدروكلوريك (pH 1.0)</option>
                        <option value="3.5">القهوة (pH 3.5)</option>
                        <option value="8.5">بيكربونات الصوديوم (pH 8.5)</option>
                        <option value="13.0">هيدروكسيد الصوديوم (pH 13.0)</option>
                    </select>
                </div>
                
                <button class="instrument-button" onclick="calibratePH()">
                    <i class="fas fa-cog"></i> معايرة الجهاز
                </button>
            </div>
            
            <div class="data-logger">
                <div class="logger-title">
                    <i class="fas fa-table"></i>
                    سجل القياسات
                </div>
                <table class="data-table" id="phData">
                    <thead>
                        <tr>
                            <th>الوقت</th>
                            <th>pH</th>
                            <th>نوع المحلول</th>
                            <th>درجة الحرارة</th>
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
                <button class="export-button" onclick="exportPHData()">
                    <i class="fas fa-download"></i> تصدير القياسات
                </button>
            </div>
        </div>
    </div>

    <!-- Add more instrument overlays as needed -->

    <script>
        let oscilloscopeAnimation = null;
        let currentPhValue = 7.0;

        function openInstrument(instrumentType) {
            const overlay = document.getElementById(instrumentType + 'Overlay');
            if (overlay) {
                overlay.style.display = 'flex';
                
                if (instrumentType === 'oscilloscope') {
                    startOscilloscope();
                } else if (instrumentType === 'phmeter') {
                    updatePhDisplay();
                }
            }
        }

        function closeInstrument(instrumentType) {
            const overlay = document.getElementById(instrumentType + 'Overlay');
            if (overlay) {
                overlay.style.display = 'none';
                
                if (instrumentType === 'oscilloscope' && oscilloscopeAnimation) {
                    cancelAnimationFrame(oscilloscopeAnimation);
                }
            }
        }

        // Oscilloscope Functions
        function startOscilloscope() {
            const canvas = document.getElementById('oscilloscopeCanvas');
            const ctx = canvas.getContext('2d');
            
            let time = 0;
            
            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                const frequency = parseFloat(document.getElementById('frequencySlider').value);
                const amplitude = parseFloat(document.getElementById('amplitudeSlider').value);
                const timebase = parseFloat(document.getElementById('timebaseSlider').value);
                
                // Draw sine wave
                ctx.strokeStyle = '#00ff00';
                ctx.lineWidth = 2;
                ctx.beginPath();
                
                for (let x = 0; x < canvas.width; x++) {
                    const t = (x / canvas.width) * 10 * timebase + time;
                    const y = canvas.height / 2 + (amplitude * 30) * Math.sin(2 * Math.PI * frequency * t / 1000);
                    
                    if (x === 0) {
                        ctx.moveTo(x, y);
                    } else {
                        ctx.lineTo(x, y);
                    }
                }
                
                ctx.stroke();
                
                time += 16; // ~60fps
                oscilloscopeAnimation = requestAnimationFrame(animate);
            }
            
            animate();
        }

        function updateTimebase() {
            const value = document.getElementById('timebaseSlider').value;
            document.getElementById('timebaseValue').textContent = parseFloat(value).toFixed(1) + ' ms/div';
        }

        function updateVoltage() {
            const value = document.getElementById('voltageSlider').value;
            document.getElementById('voltageValue').textContent = parseFloat(value).toFixed(1) + ' V/div';
        }

        function updateFrequency() {
            const value = document.getElementById('frequencySlider').value;
            document.getElementById('frequencyValue').textContent = value + ' Hz';
        }

        function updateAmplitude() {
            const value = document.getElementById('amplitudeSlider').value;
            document.getElementById('amplitudeValue').textContent = parseFloat(value).toFixed(1) + ' V';
        }

        function exportOscilloscopeData() {
            // Generate sample data
            const frequency = parseFloat(document.getElementById('frequencySlider').value);
            const amplitude = parseFloat(document.getElementById('amplitudeSlider').value);
            
            let csv = 'الوقت (ms),السعة (V),التردد (Hz),الطور\n';
            
            for (let i = 0; i < 100; i++) {
                const time = i * 10; // 10ms intervals
                const voltage = amplitude * Math.sin(2 * Math.PI * frequency * time / 1000);
                const phase = (2 * Math.PI * frequency * time / 1000) % (2 * Math.PI);
                
                csv += `${time},${voltage.toFixed(3)},${frequency},${phase.toFixed(3)}\n`;
            }
            
            downloadCSV(csv, 'oscilloscope_data.csv');
        }

        // pH Meter Functions
        function updatePhDisplay() {
            const phDisplay = document.getElementById('phDisplay');
            const phIndicator = document.getElementById('phIndicator');
            
            phDisplay.textContent = currentPhValue.toFixed(2);
            
            // Update color based on pH
            let color = '#00ff00';
            if (currentPhValue < 6) {
                color = '#ff0000'; // Red for acidic
            } else if (currentPhValue > 8) {
                color = '#0000ff'; // Blue for basic
            }
            
            phDisplay.style.setProperty('--ph-color', color);
            
            // Update indicator position
            const position = (currentPhValue / 14) * 100;
            phIndicator.style.left = `calc(${position}% - 10px)`;
        }

        function changeSolution(phValue) {
            currentPhValue = parseFloat(phValue);
            updatePhDisplay();
            
            // Add to data log
            addPhMeasurement(currentPhValue);
        }

        function calibratePH() {
            alert('تمت معايرة مقياس الحموضة بنجاح!');
        }

        function addPhMeasurement(ph) {
            const tbody = document.querySelector('#phData tbody');
            const row = document.createElement('tr');
            
            const now = new Date();
            const solutionTypes = {
                '7.0': 'الماء المقطر',
                '1.0': 'حمض الهيدروكلوريك',
                '3.5': 'القهوة',
                '8.5': 'بيكربونات الصوديوم',
                '13.0': 'هيدروكسيد الصوديوم'
            };
            
            row.innerHTML = `
                <td>${now.toLocaleTimeString()}</td>
                <td>${ph.toFixed(2)}</td>
                <td>${solutionTypes[ph.toString()] || 'محلول مجهول'}</td>
                <td>25°C</td>
            `;
            
            tbody.appendChild(row);
        }

        function exportPHData() {
            const table = document.getElementById('phData');
            let csv = 'الوقت,pH,نوع المحلول,درجة الحرارة\n';
            
            for (let i = 1; i < table.rows.length; i++) {
                const row = table.rows[i];
                const cells = Array.from(row.cells).map(cell => cell.textContent);
                csv += cells.join(',') + '\n';
            }
            
            downloadCSV(csv, 'ph_measurements.csv');
        }

        function downloadCSV(content, filename) {
            const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            
            if (link.download !== undefined) {
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', filename);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Add initial pH measurement
            addPhMeasurement(currentPhValue);
        });

        // Close overlays when clicking outside
        document.addEventListener('click', function(event) {
            if (event.target.classList.contains('instrument-overlay')) {
                const instrumentType = event.target.id.replace('Overlay', '');
                closeInstrument(instrumentType);
            }
        });
    </script>
</body>
</html>