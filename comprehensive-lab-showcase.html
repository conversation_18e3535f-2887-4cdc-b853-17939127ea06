<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comprehensive Laboratory Showcase - SimLab HUB</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="css/virtual-instruments.css" rel="stylesheet">
    <link href="css/virtual-lab-bench.css" rel="stylesheet">
    <link href="css/electronics-lab.css" rel="stylesheet">
    <link href="css/language-switcher.css" rel="stylesheet">
    <link href="css/footer-styles.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #2d3748;
            line-height: 1.6;
        }

        .showcase-container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 20px;
        }

        .showcase-header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .showcase-header h1 {
            font-size: 3.5rem;
            font-weight: 800;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            background: linear-gradient(45deg, #fff, #e2e8f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .showcase-header p {
            font-size: 1.3rem;
            opacity: 0.9;
            max-width: 900px;
            margin: 0 auto;
        }

        .lab-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
            gap: 30px;
            margin-bottom: 50px;
        }

        .lab-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .lab-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .lab-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15);
        }

        .lab-icon {
            width: 100px;
            height: 100px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 3rem;
            margin: 0 auto 25px;
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        .lab-title {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 15px;
            text-align: center;
            color: #2d3748;
        }

        .lab-description {
            color: #4a5568;
            margin-bottom: 25px;
            text-align: center;
            line-height: 1.7;
        }

        .feature-list {
            list-style: none;
            margin-bottom: 25px;
        }

        .feature-list li {
            padding: 8px 0;
            display: flex;
            align-items: center;
            gap: 10px;
            color: #2d3748;
        }

        .feature-list li::before {
            content: '✓';
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .lab-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
        }

        .lab-btn {
            background: linear-gradient(145deg, #4299e1, #3182ce);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(66, 153, 225, 0.3);
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .lab-btn:hover {
            background: linear-gradient(145deg, #3182ce, #2c5282);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(66, 153, 225, 0.4);
            text-decoration: none;
            color: white;
        }

        .lab-btn.secondary {
            background: linear-gradient(145deg, #48bb78, #38a169);
        }

        .lab-btn.secondary:hover {
            background: linear-gradient(145deg, #38a169, #2f855a);
        }

        .stats-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 50px;
        }

        .stats-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 30px;
            color: #2d3748;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 30px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 800;
            color: #4299e1;
            margin-bottom: 10px;
            display: block;
        }

        .stat-label {
            font-size: 1.1rem;
            color: #4a5568;
            font-weight: 600;
        }

        .navigation-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .navigation-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            color: #2d3748;
        }

        .navigation-subtitle {
            font-size: 1.2rem;
            color: #4a5568;
            margin-bottom: 40px;
        }

        .nav-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
        }

        .nav-card {
            background: linear-gradient(145deg, #f7fafc, #edf2f7);
            border: 2px solid #e2e8f0;
            border-radius: 15px;
            padding: 25px;
            text-decoration: none;
            color: #2d3748;
            transition: all 0.3s ease;
            display: block;
            position: relative;
            overflow: hidden;
        }

        .nav-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(66, 153, 225, 0.1), transparent);
            transition: left 0.5s ease;
        }

        .nav-card:hover::before {
            left: 100%;
        }

        .nav-card:hover {
            border-color: #4299e1;
            background: linear-gradient(145deg, #ebf8ff, #bee3f8);
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(66, 153, 225, 0.2);
            text-decoration: none;
            color: #2d3748;
        }

        .nav-icon {
            font-size: 3rem;
            color: #4299e1;
            margin-bottom: 15px;
        }

        .nav-title {
            font-size: 1.4rem;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .nav-description {
            font-size: 0.95rem;
            color: #4a5568;
            line-height: 1.5;
        }

        @media (max-width: 768px) {
            .showcase-header h1 {
                font-size: 2.5rem;
            }
            
            .lab-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .lab-card {
                padding: 20px;
            }
            
            .lab-actions {
                flex-direction: column;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 20px;
            }
            
            .nav-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="showcase-container">
        <!-- Header -->
        <div class="showcase-header">
            <h1 data-translate="nav.showcase">🔬 Complete Laboratory Suite</h1>
            <p data-translate="showcase.description">Experience the most comprehensive virtual laboratory environment with advanced tools, realistic simulations, and interactive learning experiences across multiple scientific disciplines.</p>
        </div>

        <!-- Statistics Section -->
        <div class="stats-section">
            <h2 class="stats-title">🎯 What You Get</h2>
            <div class="stats-grid">
                <div class="stat-item">
                    <span class="stat-number">50+</span>
                    <div class="stat-label">Interactive Experiments</div>
                </div>
                <div class="stat-item">
                    <span class="stat-number">15+</span>
                    <div class="stat-label">Virtual Instruments</div>
                </div>
                <div class="stat-item">
                    <span class="stat-number">3</span>
                    <div class="stat-label">Complete Lab Environments</div>
                </div>
                <div class="stat-item">
                    <span class="stat-number">100%</span>
                    <div class="stat-label">Safe & Accessible</div>
                </div>
            </div>
        </div>

        <!-- Laboratory Cards -->
        <div class="lab-grid">
            <!-- Enhanced Physics Lab -->
            <div class="lab-card">
                <div class="lab-icon">
                    <i class="fas fa-atom"></i>
                </div>
                <h2 class="lab-title">Enhanced Physics Laboratory</h2>
                <p class="lab-description">
                    Complete physics laboratory with integrated virtual instruments, real-time data logging, 
                    animated visualizations, and comprehensive experiment suite.
                </p>
                <ul class="feature-list">
                    <li>Virtual Instruments (Caliper, Thermometer, Voltmeter, Oscilloscope)</li>
                    <li>Real-time Data Logging & Graphing</li>
                    <li>Variable Controls with Presets</li>
                    <li>Animated Force Fields & Energy Visualizations</li>
                    <li>Particle Motion & Wave Simulations</li>
                    <li>Export Data in Multiple Formats</li>
                </ul>
                <div class="lab-actions">
                    <a href="enhanced-virtual-lab.html" class="lab-btn">
                        <i class="fas fa-play"></i>
                        Launch Lab
                    </a>
                    <a href="interactive-tools-showcase.html" class="lab-btn secondary">
                        <i class="fas fa-eye"></i>
                        View Tools
                    </a>
                </div>
            </div>

            <!-- Chemistry Laboratory -->
            <div class="lab-card">
                <div class="lab-icon">
                    <i class="fas fa-flask"></i>
                </div>
                <h2 class="lab-title">Virtual Chemistry Laboratory</h2>
                <p class="lab-description">
                    Interactive chemistry lab with virtual lab bench, chemical inventory, molecular-level 
                    reaction visualization, and comprehensive safety monitoring.
                </p>
                <ul class="feature-list">
                    <li>Virtual Lab Bench with Drag-and-Drop Equipment</li>
                    <li>Chemical Inventory with Safety Information</li>
                    <li>Real-time Molecular Reaction Visualization</li>
                    <li>15 Complete Chemistry Experiments</li>
                    <li>Safety Warnings & Emergency Protocols</li>
                    <li>Titration, Calorimetry, Synthesis & More</li>
                </ul>
                <div class="lab-actions">
                    <a href="chemistry-lab.html" class="lab-btn">
                        <i class="fas fa-play"></i>
                        Launch Lab
                    </a>
                    <a href="#chemistry-features" class="lab-btn secondary">
                        <i class="fas fa-info-circle"></i>
                        Learn More
                    </a>
                </div>
            </div>

            <!-- Electronics Laboratory -->
            <div class="lab-card">
                <div class="lab-icon">
                    <i class="fas fa-microchip"></i>
                </div>
                <h2 class="lab-title">Virtual Electronics Laboratory</h2>
                <p class="lab-description">
                    Comprehensive electronics lab for circuit analysis, component testing, and electronics 
                    design with 18 specialized experiments and advanced simulation tools.
                </p>
                <ul class="feature-list">
                    <li>Interactive Circuit Breadboard</li>
                    <li>Component Library (Resistors, Capacitors, ICs)</li>
                    <li>Circuit Simulation & Analysis Tools</li>
                    <li>18 Electronics Experiments</li>
                    <li>Ohm's Law, Kirchhoff's Laws, Logic Gates</li>
                    <li>Oscilloscope & Multimeter Integration</li>
                </ul>
                <div class="lab-actions">
                    <a href="electronics-lab.html" class="lab-btn">
                        <i class="fas fa-play"></i>
                        Launch Lab
                    </a>
                    <a href="#electronics-features" class="lab-btn secondary">
                        <i class="fas fa-cogs"></i>
                        View Circuits
                    </a>
                </div>
            </div>

            <!-- Integrated Tools -->
            <div class="lab-card">
                <div class="lab-icon">
                    <i class="fas fa-puzzle-piece"></i>
                </div>
                <h2 class="lab-title">Integrated Tool Suite</h2>
                <p class="lab-description">
                    All laboratory tools work together seamlessly, providing a unified experience where 
                    instruments, data loggers, controls, and visualizations are fully integrated.
                </p>
                <ul class="feature-list">
                    <li>Cross-Platform Tool Integration</li>
                    <li>Unified Data Management</li>
                    <li>Real-time Synchronization</li>
                    <li>Comprehensive Export Options</li>
                    <li>Educational Analytics</li>
                    <li>Collaborative Features</li>
                </ul>
                <div class="lab-actions">
                    <a href="interactive-tools-showcase.html" class="lab-btn">
                        <i class="fas fa-tools"></i>
                        Explore Tools
                    </a>
                    <a href="experiment-showcase.html" class="lab-btn secondary">
                        <i class="fas fa-graduation-cap"></i>
                        View Experiments
                    </a>
                </div>
            </div>
        </div>

        <!-- Navigation Section -->
        <div class="navigation-section">
            <h2 class="navigation-title">🚀 Start Your Virtual Laboratory Journey</h2>
            <p class="navigation-subtitle">Choose your path and begin exploring the future of scientific education</p>
            
            <div class="nav-grid">
                <a href="enhanced-virtual-lab.html" class="nav-card">
                    <div class="nav-icon">
                        <i class="fas fa-microscope"></i>
                    </div>
                    <div class="nav-title">Physics Laboratory</div>
                    <div class="nav-description">
                        Start with our most advanced physics lab featuring all integrated tools and comprehensive experiments.
                    </div>
                </a>
                
                <a href="chemistry-lab.html" class="nav-card">
                    <div class="nav-icon">
                        <i class="fas fa-vial"></i>
                    </div>
                    <div class="nav-title">Chemistry Laboratory</div>
                    <div class="nav-description">
                        Explore chemical reactions with our virtual lab bench and molecular visualization system.
                    </div>
                </a>
                
                <a href="electronics-lab.html" class="nav-card">
                    <div class="nav-icon">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <div class="nav-title">Electronics Laboratory</div>
                    <div class="nav-description">
                        Build and analyze circuits with our comprehensive electronics simulation environment.
                    </div>
                </a>
                
                <a href="interactive-tools-showcase.html" class="nav-card">
                    <div class="nav-icon">
                        <i class="fas fa-wrench"></i>
                    </div>
                    <div class="nav-title">Tools Showcase</div>
                    <div class="nav-description">
                        Discover all available tools and see how they work together in our interactive demonstration.
                    </div>
                </a>
                
                <a href="virtual-labs.html" class="nav-card">
                    <div class="nav-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <div class="nav-title">Labs Hub</div>
                    <div class="nav-description">
                        Access the main laboratory hub with all available experiments and learning resources.
                    </div>
                </a>
                
                <a href="index.html" class="nav-card">
                    <div class="nav-icon">
                        <i class="fas fa-rocket"></i>
                    </div>
                    <div class="nav-title">SimLab HUB</div>
                    <div class="nav-description">
                        Return to the main SimLab HUB for access to all educational content and features.
                    </div>
                </a>
            </div>
        </div>
    </div>

    <script>
        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            // Animate stat numbers
            const statNumbers = document.querySelectorAll('.stat-number');
            
            const animateNumber = (element, target) => {
                const duration = 2000;
                const start = 0;
                const increment = target / (duration / 16);
                let current = start;
                
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }
                    
                    if (target.toString().includes('+')) {
                        element.textContent = Math.floor(current) + '+';
                    } else if (target.toString().includes('%')) {
                        element.textContent = Math.floor(current) + '%';
                    } else {
                        element.textContent = Math.floor(current);
                    }
                }, 16);
            };
            
            // Intersection Observer for animations
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const target = entry.target.textContent;
                        const numericValue = parseInt(target.replace(/[^0-9]/g, ''));
                        animateNumber(entry.target, numericValue);
                        observer.unobserve(entry.target);
                    }
                });
            });
            
            statNumbers.forEach(number => {
                observer.observe(number);
            });
            
            // Add hover effects to cards
            const cards = document.querySelectorAll('.lab-card, .nav-card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-10px) scale(1.02)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        });
    </script>

    <!-- Language System and Footer -->
    <script src="js/language-system.js"></script>
    <script src="js/footer-component.js"></script>
</body>
</html>
