/* RTL (Right-to-Left) Styles for Arabic Interface */

/* Global RTL Adjustments */
[dir="rtl"] {
    text-align: right;
}

[dir="rtl"] body {
    font-family: 'Segoe UI', '<PERSON>hom<PERSON>', 'Arial', 'Helvetica', sans-serif;
    direction: rtl;
}

/* Language Switcher RTL */
[dir="rtl"] .language-switcher {
    left: 20px;
    right: auto;
}

[dir="rtl"] .lang-btn {
    flex-direction: row-reverse;
}

/* Navigation RTL */
[dir="rtl"] .nav-overlay {
    right: auto;
    left: 0;
    text-align: right;
}

[dir="rtl"] .nav-overlay a {
    text-align: right;
    padding-right: 20px;
    padding-left: 10px;
}

/* Header RTL */
[dir="rtl"] .lab-header {
    flex-direction: row-reverse;
}

[dir="rtl"] .lab-title {
    flex-direction: row-reverse;
}

[dir="rtl"] .lab-controls {
    flex-direction: row-reverse;
}

/* Grid Layouts RTL */
[dir="rtl"] .electronics-lab {
    grid-template-areas: 
        "header header header header"
        "measurements instruments breadboard components"
        "tools results circuit-analysis experiments";
}

[dir="rtl"] .chemistry-lab {
    grid-template-areas: 
        "header header header"
        "workspace equipment experiments"
        "controls safety safety";
}

[dir="rtl"] .virtual-lab-bench {
    grid-template-areas: 
        "controls workspace equipment"
        "safety safety safety";
    grid-template-columns: 200px 1fr 300px;
}

/* Panel Titles RTL */
[dir="rtl"] .panel-title {
    flex-direction: row-reverse;
    text-align: right;
}

[dir="rtl"] .panel-title i {
    margin-left: 8px;
    margin-right: 0;
}

/* Component Items RTL */
[dir="rtl"] .component-item,
[dir="rtl"] .equipment-item,
[dir="rtl"] .tool-item {
    text-align: center; /* Keep centered for icons */
}

/* Experiment Items RTL */
[dir="rtl"] .experiment-item {
    text-align: right;
}

[dir="rtl"] .experiment-title {
    text-align: right;
}

[dir="rtl"] .experiment-description {
    text-align: right;
}

/* Controls RTL */
[dir="rtl"] .control-wrapper {
    text-align: right;
}

[dir="rtl"] .control-label {
    text-align: right;
}

[dir="rtl"] .slider-container {
    flex-direction: row-reverse;
}

[dir="rtl"] .slider-range {
    flex-direction: row-reverse;
}

[dir="rtl"] .input-container {
    flex-direction: row-reverse;
}

[dir="rtl"] .button-container {
    flex-direction: row-reverse;
}

[dir="rtl"] .toggle-label {
    flex-direction: row-reverse;
}

/* Safety Panel RTL */
[dir="rtl"] .safety-status {
    margin-right: auto;
    margin-left: 0;
}

[dir="rtl"] .alert-content {
    flex-direction: row-reverse;
}

[dir="rtl"] .alert-close {
    margin-right: auto;
    margin-left: 0;
}

[dir="rtl"] .emergency-controls {
    flex-direction: row-reverse;
}

/* Measurement Cards RTL */
[dir="rtl"] .measurement-grid {
    direction: rtl;
}

[dir="rtl"] .measurement-card {
    text-align: center; /* Keep measurements centered */
}

[dir="rtl"] .data-row {
    flex-direction: row-reverse;
}

/* Analysis Controls RTL */
[dir="rtl"] .analysis-controls {
    flex-direction: row-reverse;
}

[dir="rtl"] .demo-controls {
    flex-direction: row-reverse;
}

[dir="rtl"] .visualization-controls {
    flex-direction: row-reverse;
}

/* Lab Actions RTL */
[dir="rtl"] .lab-actions {
    flex-direction: row-reverse;
}

[dir="rtl"] .lab-btn {
    flex-direction: row-reverse;
}

[dir="rtl"] .lab-btn i {
    margin-left: 8px;
    margin-right: 0;
}

/* Equipment Visual RTL */
[dir="rtl"] .equipment-visual {
    text-align: center; /* Keep equipment visuals centered */
}

[dir="rtl"] .equipment-controls {
    left: -10px;
    right: auto;
}

/* Breadboard RTL */
[dir="rtl"] .power-rails::before {
    right: 10px;
    left: auto;
}

[dir="rtl"] .power-rails::after {
    left: 10px;
    right: auto;
}

/* Feature Lists RTL */
[dir="rtl"] .feature-list li {
    flex-direction: row-reverse;
    text-align: right;
}

[dir="rtl"] .feature-list li::before {
    margin-left: 10px;
    margin-right: 0;
}

/* Stats Grid RTL */
[dir="rtl"] .stats-grid {
    direction: rtl;
}

[dir="rtl"] .stat-item {
    text-align: center; /* Keep stats centered */
}

/* Navigation Cards RTL */
[dir="rtl"] .nav-grid {
    direction: rtl;
}

[dir="rtl"] .nav-card {
    text-align: center; /* Keep nav cards centered for better appearance */
}

/* Footer RTL */
[dir="rtl"] .footer {
    text-align: right;
}

[dir="rtl"] .footer-content {
    flex-direction: row-reverse;
}

[dir="rtl"] .footer-section {
    text-align: right;
}

[dir="rtl"] .footer-links {
    flex-direction: row-reverse;
}

[dir="rtl"] .footer-links a {
    margin-left: 20px;
    margin-right: 0;
}

/* Instrument Displays RTL */
[dir="rtl"] .instrument-display {
    direction: ltr; /* Keep instrument displays LTR for readability */
}

[dir="rtl"] .measurement-display {
    direction: ltr; /* Keep measurements LTR */
}

[dir="rtl"] .digital-display {
    direction: ltr; /* Keep digital displays LTR */
}

/* Canvas and SVG Elements */
[dir="rtl"] canvas,
[dir="rtl"] svg {
    direction: ltr; /* Keep graphics LTR */
}

/* Form Elements RTL */
[dir="rtl"] .form-group {
    text-align: right;
}

[dir="rtl"] .form-label {
    text-align: right;
}

[dir="rtl"] .form-control {
    text-align: right;
}

[dir="rtl"] select {
    text-align: right;
    padding-right: 12px;
    padding-left: 30px;
}

/* Modal RTL */
[dir="rtl"] .modal-content {
    text-align: right;
}

[dir="rtl"] .modal-header {
    flex-direction: row-reverse;
}

[dir="rtl"] .modal-close {
    left: 15px;
    right: auto;
}

/* Tooltip RTL */
[dir="rtl"] .tooltip {
    text-align: right;
}

/* Responsive RTL Adjustments */
@media (max-width: 1200px) {
    [dir="rtl"] .electronics-lab {
        grid-template-areas: 
            "header header header"
            "measurements breadboard components"
            "instruments circuit-analysis experiments"
            "tools results results";
    }
    
    [dir="rtl"] .chemistry-lab {
        grid-template-areas: 
            "header header"
            "workspace equipment"
            "experiments experiments"
            "controls safety"
            "results results";
    }
}

@media (max-width: 768px) {
    [dir="rtl"] .electronics-lab,
    [dir="rtl"] .chemistry-lab {
        grid-template-areas: 
            "header"
            "breadboard"
            "components"
            "experiments"
            "instruments"
            "measurements"
            "circuit-analysis"
            "results"
            "tools";
        grid-template-columns: 1fr;
    }
    
    [dir="rtl"] .virtual-lab-bench {
        grid-template-areas: 
            "workspace"
            "equipment"
            "controls"
            "safety";
        grid-template-columns: 1fr;
    }
    
    [dir="rtl"] .lab-controls {
        flex-direction: column;
    }
    
    [dir="rtl"] .lab-actions {
        flex-direction: column;
    }
}

/* Language-specific Font Adjustments */
.lang-ar {
    font-family: 'Segoe UI', 'Tahoma', 'Arial', 'Helvetica', sans-serif;
}

.lang-ar .panel-title,
.lang-ar .lab-title,
.lang-ar .nav-title {
    font-weight: 600; /* Slightly lighter for Arabic */
}

.lang-ar .measurement-value,
.lang-ar .stat-number {
    direction: ltr; /* Keep numbers LTR */
    display: inline-block;
}

/* Arabic Text Improvements */
.lang-ar p,
.lang-ar .description {
    line-height: 1.8; /* Better line height for Arabic */
}

.lang-ar .experiment-description,
.lang-ar .nav-description {
    line-height: 1.7;
}

/* Preserve LTR for Technical Content */
[dir="rtl"] .code,
[dir="rtl"] .formula,
[dir="rtl"] .equation,
[dir="rtl"] .chemical-formula {
    direction: ltr;
    display: inline-block;
    text-align: left;
}

/* Animation Adjustments for RTL */
[dir="rtl"] .loading::after {
    animation: loading-sweep-rtl 1.5s ease-in-out infinite;
}

@keyframes loading-sweep-rtl {
    0% { right: -100%; }
    100% { right: 100%; }
}

[dir="rtl"] .nav-card::before {
    background: linear-gradient(-90deg, transparent, rgba(66, 153, 225, 0.1), transparent);
}

/* Specific Component Adjustments */
[dir="rtl"] .status-indicator {
    flex-direction: row-reverse;
}

[dir="rtl"] .status-led {
    margin-left: 8px;
    margin-right: 0;
}

[dir="rtl"] .difficulty-badge {
    left: 10px;
    right: auto;
}

[dir="rtl"] .equipment-info {
    text-align: center;
}

[dir="rtl"] .component-value {
    text-align: center;
}
