// Advanced Physics Engine for Virtual Labs
class PhysicsEngine {
    constructor() {
        this.constants = {
            G: 9.8,           // Gravity (m/s²)
            c: 299792458,     // Speed of light (m/s)
            h: 6.626e-34,     // Planck constant (J⋅s)
            k: 1.381e-23,     // <PERSON><PERSON>mann constant (J/K)
            e: 1.602e-19,     // Elementary charge (C)
            me: 9.109e-31,    // Electron mass (kg)
            mp: 1.673e-27,    // Proton mass (kg)
            R: 8.314,         // Gas constant (J/mol⋅K)
            sigma: 5.67e-8    // <PERSON><PERSON> constant (W/m²⋅K⁴)
        };
        
        this.experiments = {
            projectile: new ProjectileMotion(),
            pendulum: new PendulumMotion(),
            collision: new CollisionSimulation(),
            spring: new SpringMassSystem(),
            newton: new NewtonLaws(),
            gasLaws: new GasLaws(),
            heatTransfer: new HeatTransfer(),
            phaseChange: new PhaseChange(),
            optics: new OpticsSimulation(),
            photoelectric: new PhotoelectricEffect(),
            radioactive: new RadioactiveDecay()
        };
    }

    getExperiment(type) {
        return this.experiments[type];
    }
}

// Base Experiment Class
class BaseExperiment {
    constructor() {
        this.data = [];
        this.isRunning = false;
        this.startTime = 0;
        this.parameters = {};
    }

    start(parameters) {
        this.parameters = { ...parameters };
        this.data = [];
        this.isRunning = true;
        this.startTime = Date.now();
    }

    stop() {
        this.isRunning = false;
    }

    reset() {
        this.stop();
        this.data = [];
        this.startTime = 0;
    }

    addDataPoint(dataPoint) {
        this.data.push({
            ...dataPoint,
            timestamp: Date.now() - this.startTime
        });
    }

    getData() {
        return this.data;
    }
}

// Mechanics Experiments
class ProjectileMotion extends BaseExperiment {
    constructor() {
        super();
        this.position = { x: 0, y: 0 };
        this.velocity = { x: 0, y: 0 };
        this.acceleration = { x: 0, y: -9.8 };
    }

    calculateTrajectory(v0, angle, mass = 1, gravity = 9.8, airResistance = 0) {
        const angleRad = angle * Math.PI / 180;
        const v0x = v0 * Math.cos(angleRad);
        const v0y = v0 * Math.sin(angleRad);
        
        // Without air resistance
        if (airResistance === 0) {
            const flightTime = (2 * v0y) / gravity;
            const maxRange = (v0 * v0 * Math.sin(2 * angleRad)) / gravity;
            const maxHeight = (v0y * v0y) / (2 * gravity);
            
            return {
                flightTime,
                maxRange,
                maxHeight,
                trajectory: this.generateTrajectoryPoints(v0x, v0y, gravity, flightTime)
            };
        }
        
        // With air resistance - numerical integration
        return this.calculateWithAirResistance(v0x, v0y, mass, gravity, airResistance);
    }

    generateTrajectoryPoints(v0x, v0y, gravity, flightTime, steps = 100) {
        const points = [];
        const dt = flightTime / steps;
        
        for (let i = 0; i <= steps; i++) {
            const t = i * dt;
            const x = v0x * t;
            const y = v0y * t - 0.5 * gravity * t * t;
            
            if (y >= 0) {
                points.push({ x, y, t });
            }
        }
        
        return points;
    }

    calculateWithAirResistance(v0x, v0y, mass, gravity, dragCoeff) {
        const points = [];
        let x = 0, y = 0;
        let vx = v0x, vy = v0y;
        let t = 0;
        const dt = 0.01; // Time step
        
        while (y >= 0 || t === 0) {
            // Calculate drag force
            const speed = Math.sqrt(vx * vx + vy * vy);
            const dragX = -dragCoeff * speed * vx / mass;
            const dragY = -dragCoeff * speed * vy / mass;
            
            // Update acceleration
            const ax = dragX;
            const ay = -gravity + dragY;
            
            // Update velocity and position
            vx += ax * dt;
            vy += ay * dt;
            x += vx * dt;
            y += vy * dt;
            t += dt;
            
            points.push({ x, y, t, vx, vy, speed });
        }
        
        return {
            trajectory: points,
            maxRange: Math.max(...points.map(p => p.x)),
            maxHeight: Math.max(...points.map(p => p.y)),
            flightTime: points[points.length - 1].t
        };
    }

    updatePosition(t) {
        const { v0, angle, gravity } = this.parameters;
        const angleRad = angle * Math.PI / 180;
        const v0x = v0 * Math.cos(angleRad);
        const v0y = v0 * Math.sin(angleRad);
        
        this.position.x = v0x * t;
        this.position.y = v0y * t - 0.5 * gravity * t * t;
        
        this.velocity.x = v0x;
        this.velocity.y = v0y - gravity * t;
        
        // Calculate energies
        const speed = Math.sqrt(this.velocity.x ** 2 + this.velocity.y ** 2);
        const kineticEnergy = 0.5 * this.parameters.mass * speed ** 2;
        const potentialEnergy = this.parameters.mass * gravity * this.position.y;
        
        this.addDataPoint({
            t,
            position: { ...this.position },
            velocity: { ...this.velocity },
            speed,
            kineticEnergy,
            potentialEnergy,
            totalEnergy: kineticEnergy + potentialEnergy
        });
        
        return this.position.y >= 0; // Continue simulation if above ground
    }
}

class PendulumMotion extends BaseExperiment {
    constructor() {
        super();
        this.angle = 0;
        this.angularVelocity = 0;
        this.length = 1;
    }

    calculatePeriod(length, gravity = 9.8, angle = 0) {
        // Small angle approximation
        if (Math.abs(angle) < 0.2) {
            return 2 * Math.PI * Math.sqrt(length / gravity);
        }
        
        // Large angle correction using elliptic integral approximation
        const k = Math.sin(angle / 2);
        const correction = 1 + (1/4) * k*k + (9/64) * k*k*k*k;
        return 2 * Math.PI * Math.sqrt(length / gravity) * correction;
    }

    updatePosition(t) {
        const { length, initialAngle, gravity, damping = 0 } = this.parameters;
        
        if (damping === 0) {
            // Simple harmonic motion
            const omega = Math.sqrt(gravity / length);
            this.angle = initialAngle * Math.cos(omega * t);
            this.angularVelocity = -initialAngle * omega * Math.sin(omega * t);
        } else {
            // Damped harmonic motion
            const omega0 = Math.sqrt(gravity / length);
            const omega = omega0 * Math.sqrt(1 - damping * damping);
            const A = initialAngle;
            
            this.angle = A * Math.exp(-damping * omega0 * t) * Math.cos(omega * t);
            this.angularVelocity = -A * Math.exp(-damping * omega0 * t) * 
                (damping * omega0 * Math.cos(omega * t) + omega * Math.sin(omega * t));
        }
        
        // Calculate position
        const x = length * Math.sin(this.angle);
        const y = -length * Math.cos(this.angle);
        
        // Calculate energies
        const speed = length * Math.abs(this.angularVelocity);
        const kineticEnergy = 0.5 * this.parameters.mass * speed * speed;
        const potentialEnergy = this.parameters.mass * gravity * (length - length * Math.cos(this.angle));
        
        this.addDataPoint({
            t,
            angle: this.angle,
            angularVelocity: this.angularVelocity,
            position: { x, y },
            kineticEnergy,
            potentialEnergy,
            totalEnergy: kineticEnergy + potentialEnergy
        });
        
        return Math.abs(this.angle) > 0.001 || Math.abs(this.angularVelocity) > 0.001;
    }
}

class CollisionSimulation extends BaseExperiment {
    constructor() {
        super();
        this.objects = [];
    }

    elasticCollision1D(m1, v1, m2, v2) {
        const totalMass = m1 + m2;
        const v1f = ((m1 - m2) * v1 + 2 * m2 * v2) / totalMass;
        const v2f = ((m2 - m1) * v2 + 2 * m1 * v1) / totalMass;
        
        return { v1f, v2f };
    }

    inelasticCollision1D(m1, v1, m2, v2, e = 0) {
        // e = coefficient of restitution (0 = perfectly inelastic, 1 = perfectly elastic)
        const totalMass = m1 + m2;
        const vcm = (m1 * v1 + m2 * v2) / totalMass; // Center of mass velocity
        
        const v1f = vcm + e * m2 * (v2 - v1) / totalMass;
        const v2f = vcm + e * m1 * (v1 - v2) / totalMass;
        
        return { v1f, v2f };
    }

    elasticCollision2D(obj1, obj2) {
        const dx = obj2.x - obj1.x;
        const dy = obj2.y - obj1.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        
        if (distance < obj1.radius + obj2.radius) {
            // Collision detected
            const nx = dx / distance; // Normal vector
            const ny = dy / distance;
            
            // Relative velocity
            const dvx = obj2.vx - obj1.vx;
            const dvy = obj2.vy - obj1.vy;
            
            // Relative velocity along normal
            const dvn = dvx * nx + dvy * ny;
            
            if (dvn > 0) return; // Objects separating
            
            // Collision impulse
            const impulse = 2 * dvn / (obj1.mass + obj2.mass);
            
            // Update velocities
            obj1.vx += impulse * obj2.mass * nx;
            obj1.vy += impulse * obj2.mass * ny;
            obj2.vx -= impulse * obj1.mass * nx;
            obj2.vy -= impulse * obj1.mass * ny;
            
            // Separate objects
            const overlap = obj1.radius + obj2.radius - distance;
            const separationX = nx * overlap * 0.5;
            const separationY = ny * overlap * 0.5;
            
            obj1.x -= separationX;
            obj1.y -= separationY;
            obj2.x += separationX;
            obj2.y += separationY;
            
            return true; // Collision occurred
        }
        
        return false; // No collision
    }

    calculateMomentum(objects) {
        let totalMomentumX = 0;
        let totalMomentumY = 0;
        
        objects.forEach(obj => {
            totalMomentumX += obj.mass * obj.vx;
            totalMomentumY += obj.mass * obj.vy;
        });
        
        return {
            x: totalMomentumX,
            y: totalMomentumY,
            magnitude: Math.sqrt(totalMomentumX ** 2 + totalMomentumY ** 2)
        };
    }

    calculateKineticEnergy(objects) {
        return objects.reduce((total, obj) => {
            const speed = Math.sqrt(obj.vx ** 2 + obj.vy ** 2);
            return total + 0.5 * obj.mass * speed ** 2;
        }, 0);
    }
}

class SpringMassSystem extends BaseExperiment {
    constructor() {
        super();
        this.position = 0;
        this.velocity = 0;
    }

    calculatePeriod(mass, springConstant) {
        return 2 * Math.PI * Math.sqrt(mass / springConstant);
    }

    updatePosition(t) {
        const { mass, springConstant, initialPosition, damping = 0 } = this.parameters;
        const omega0 = Math.sqrt(springConstant / mass);
        
        if (damping === 0) {
            // Simple harmonic motion
            this.position = initialPosition * Math.cos(omega0 * t);
            this.velocity = -initialPosition * omega0 * Math.sin(omega0 * t);
        } else {
            // Damped harmonic motion
            const gamma = damping / (2 * mass);
            
            if (gamma < omega0) {
                // Underdamped
                const omega = Math.sqrt(omega0 * omega0 - gamma * gamma);
                this.position = initialPosition * Math.exp(-gamma * t) * Math.cos(omega * t);
                this.velocity = initialPosition * Math.exp(-gamma * t) * 
                    (-gamma * Math.cos(omega * t) - omega * Math.sin(omega * t));
            } else if (gamma === omega0) {
                // Critically damped
                this.position = initialPosition * (1 + gamma * t) * Math.exp(-gamma * t);
                this.velocity = -initialPosition * gamma * gamma * t * Math.exp(-gamma * t);
            } else {
                // Overdamped
                const alpha = Math.sqrt(gamma * gamma - omega0 * omega0);
                const A = initialPosition * 0.5;
                this.position = A * Math.exp(-gamma * t) * 
                    (Math.exp(alpha * t) + Math.exp(-alpha * t));
                this.velocity = A * Math.exp(-gamma * t) * 
                    (-gamma * (Math.exp(alpha * t) + Math.exp(-alpha * t)) + 
                     alpha * (Math.exp(alpha * t) - Math.exp(-alpha * t)));
            }
        }
        
        // Calculate forces and energies
        const springForce = -springConstant * this.position;
        const dampingForce = -damping * this.velocity;
        const kineticEnergy = 0.5 * mass * this.velocity * this.velocity;
        const potentialEnergy = 0.5 * springConstant * this.position * this.position;
        
        this.addDataPoint({
            t,
            position: this.position,
            velocity: this.velocity,
            springForce,
            dampingForce,
            kineticEnergy,
            potentialEnergy,
            totalEnergy: kineticEnergy + potentialEnergy
        });
        
        return Math.abs(this.position) > 0.001 || Math.abs(this.velocity) > 0.001;
    }
}

// Thermodynamics Experiments
class GasLaws extends BaseExperiment {
    constructor() {
        super();
        this.state = { P: 101325, V: 0.001, T: 298, n: 1 }; // Standard conditions
    }

    idealGasLaw(P, V, n, T, R = 8.314) {
        // PV = nRT
        if (P && V && n && T) return P * V === n * R * T;
        if (!P) return (n * R * T) / V;
        if (!V) return (n * R * T) / P;
        if (!n) return (P * V) / (R * T);
        if (!T) return (P * V) / (n * R);
    }

    boyleLaw(P1, V1, P2, V2) {
        // P1V1 = P2V2 (constant T, n)
        if (!P2) return (P1 * V1) / V2;
        if (!V2) return (P1 * V1) / P2;
        return P1 * V1 === P2 * V2;
    }

    charlesLaw(V1, T1, V2, T2) {
        // V1/T1 = V2/T2 (constant P, n)
        if (!V2) return (V1 * T2) / T1;
        if (!T2) return (V2 * T1) / V1;
        return V1 / T1 === V2 / T2;
    }

    gayLussacLaw(P1, T1, P2, T2) {
        // P1/T1 = P2/T2 (constant V, n)
        if (!P2) return (P1 * T2) / T1;
        if (!T2) return (P2 * T1) / P1;
        return P1 / T1 === P2 / T2;
    }

    combinedGasLaw(P1, V1, T1, P2, V2, T2) {
        // (P1V1)/T1 = (P2V2)/T2
        const constant = (P1 * V1) / T1;
        if (!P2) return (constant * T2) / V2;
        if (!V2) return (constant * T2) / P2;
        if (!T2) return (P2 * V2) / constant;
        return Math.abs((P1 * V1) / T1 - (P2 * V2) / T2) < 0.001;
    }

    vanDerWaalsEquation(P, V, n, T, a, b, R = 8.314) {
        // (P + a*n²/V²)(V - nb) = nRT
        const pressureCorrection = a * n * n / (V * V);
        const volumeCorrection = n * b;
        return (P + pressureCorrection) * (V - volumeCorrection) === n * R * T;
    }
}

class HeatTransfer extends BaseExperiment {
    constructor() {
        super();
        this.temperatures = [];
    }

    conduction(k, A, deltaT, deltaX) {
        // Fourier's law: q = -kA(dT/dx)
        return -k * A * (deltaT / deltaX);
    }

    convection(h, A, deltaT) {
        // Newton's law of cooling: q = hA(T_surface - T_fluid)
        return h * A * deltaT;
    }

    radiation(sigma, epsilon, A, T1, T2) {
        // Stefan-Boltzmann law: q = σεA(T₁⁴ - T₂⁴)
        return sigma * epsilon * A * (Math.pow(T1, 4) - Math.pow(T2, 4));
    }

    thermalDiffusion(alpha, initialTemp, x, t) {
        // Solution for infinite rod with initial temperature step
        return initialTemp * (1 - erf(x / (2 * Math.sqrt(alpha * t))));
    }

    // Error function approximation
    erf(x) {
        const a1 =  0.*********;
        const a2 = -0.*********;
        const a3 =  1.*********;
        const a4 = -1.*********;
        const a5 =  1.*********;
        const p  =  0.3275911;

        const sign = x >= 0 ? 1 : -1;
        x = Math.abs(x);

        const t = 1.0 / (1.0 + p * x);
        const y = 1.0 - (((((a5 * t + a4) * t) + a3) * t + a2) * t + a1) * t * Math.exp(-x * x);

        return sign * y;
    }
}

// Optics Experiments
class OpticsSimulation extends BaseExperiment {
    constructor() {
        super();
        this.rays = [];
    }

    snellsLaw(n1, theta1, n2) {
        // n1*sin(θ1) = n2*sin(θ2)
        const sinTheta2 = (n1 * Math.sin(theta1)) / n2;
        
        if (Math.abs(sinTheta2) > 1) {
            // Total internal reflection
            return { theta2: null, totalInternalReflection: true };
        }
        
        return { theta2: Math.asin(sinTheta2), totalInternalReflection: false };
    }

    criticalAngle(n1, n2) {
        if (n1 <= n2) return null; // No critical angle for n1 <= n2
        return Math.asin(n2 / n1);
    }

    thinLensEquation(f, do, di) {
        // 1/f = 1/do + 1/di
        if (f && do && !di) return 1 / (1/f - 1/do);
        if (f && di && !do) return 1 / (1/f - 1/di);
        if (do && di && !f) return 1 / (1/do + 1/di);
        return null;
    }

    magnification(di, do) {
        return -di / do;
    }

    diffraction(wavelength, slitWidth, distance, angle) {
        // Single-slit diffraction minima: a*sin(θ) = m*λ
        const m = (slitWidth * Math.sin(angle)) / wavelength;
        return Math.round(m); // Order of minimum
    }

    interference(wavelength, slitSeparation, distance, position) {
        // Double-slit interference
        const pathDifference = (slitSeparation * position) / distance;
        const phase = (2 * Math.PI * pathDifference) / wavelength;
        const intensity = 4 * Math.cos(phase / 2) ** 2;
        return { pathDifference, phase, intensity };
    }
}

// Modern Physics Experiments
class PhotoelectricEffect extends BaseExperiment {
    constructor() {
        super();
        this.workFunction = 4.5; // eV for typical metal
    }

    photoelectricEquation(photonEnergy, workFunction) {
        // E_kinetic = hf - φ
        const kineticEnergy = photonEnergy - workFunction;
        return kineticEnergy > 0 ? kineticEnergy : 0;
    }

    photonEnergy(frequency, h = 6.626e-34) {
        return h * frequency / 1.602e-19; // Convert to eV
    }

    stoppingPotential(maxKineticEnergy, e = 1.602e-19) {
        return maxKineticEnergy / e; // in volts
    }

    thresholdFrequency(workFunction, h = 6.626e-34) {
        return (workFunction * 1.602e-19) / h; // Convert eV to J
    }
}

class RadioactiveDecay extends BaseExperiment {
    constructor() {
        super();
        this.isotopes = {
            'C-14': { halfLife: 5730 * 365.25 * 24 * 3600 }, // seconds
            'U-238': { halfLife: 4.468e9 * 365.25 * 24 * 3600 },
            'Ra-226': { halfLife: 1600 * 365.25 * 24 * 3600 },
            'I-131': { halfLife: 8.02 * 24 * 3600 }
        };
    }

    decayConstant(halfLife) {
        return Math.log(2) / halfLife;
    }

    exponentialDecay(N0, lambda, t) {
        return N0 * Math.exp(-lambda * t);
    }

    activity(N, lambda) {
        return N * lambda; // Disintegrations per second
    }

    halfLife(lambda) {
        return Math.log(2) / lambda;
    }

    meanLifetime(lambda) {
        return 1 / lambda;
    }

    dateFromDecay(N, N0, lambda) {
        if (N <= 0 || N > N0) return null;
        return Math.log(N0 / N) / lambda;
    }
}

// Utility Functions
class PhysicsUtils {
    static vectorAdd(v1, v2) {
        return {
            x: v1.x + v2.x,
            y: v1.y + v2.y,
            z: (v1.z || 0) + (v2.z || 0)
        };
    }

    static vectorMagnitude(v) {
        return Math.sqrt(v.x ** 2 + v.y ** 2 + (v.z || 0) ** 2);
    }

    static vectorNormalize(v) {
        const mag = this.vectorMagnitude(v);
        if (mag === 0) return { x: 0, y: 0, z: 0 };
        return {
            x: v.x / mag,
            y: v.y / mag,
            z: (v.z || 0) / mag
        };
    }

    static vectorDotProduct(v1, v2) {
        return v1.x * v2.x + v1.y * v2.y + (v1.z || 0) * (v2.z || 0);
    }

    static vectorCrossProduct(v1, v2) {
        return {
            x: v1.y * (v2.z || 0) - (v1.z || 0) * v2.y,
            y: (v1.z || 0) * v2.x - v1.x * (v2.z || 0),
            z: v1.x * v2.y - v1.y * v2.x
        };
    }

    static degreesToRadians(degrees) {
        return degrees * Math.PI / 180;
    }

    static radiansToDegrees(radians) {
        return radians * 180 / Math.PI;
    }

    static roundToSignificantFigures(num, sig) {
        if (num === 0) return 0;
        const digits = sig - Math.floor(Math.log10(Math.abs(num))) - 1;
        return parseFloat(num.toFixed(digits));
    }

    static formatScientificNotation(num, precision = 2) {
        return num.toExponential(precision);
    }

    static kelvinToCelsius(K) {
        return K - 273.15;
    }

    static celsiusToKelvin(C) {
        return C + 273.15;
    }

    static fahrenheitToCelsius(F) {
        return (F - 32) * 5 / 9;
    }

    static celsiusToFahrenheit(C) {
        return C * 9 / 5 + 32;
    }

    static eVToJoules(eV) {
        return eV * 1.602e-19;
    }

    static joulesToEV(J) {
        return J / 1.602e-19;
    }
}

// Error Analysis
class ErrorAnalysis {
    static propagateUncertainty(values, uncertainties, operation) {
        switch (operation) {
            case 'add':
            case 'subtract':
                return Math.sqrt(uncertainties.reduce((sum, u) => sum + u*u, 0));
            
            case 'multiply':
            case 'divide':
                const relativeUncertainties = uncertainties.map((u, i) => u / Math.abs(values[i]));
                return Math.sqrt(relativeUncertainties.reduce((sum, ru) => sum + ru*ru, 0));
            
            case 'power':
                const [value, power] = values;
                const [valueUncertainty] = uncertainties;
                return Math.abs(power) * (valueUncertainty / Math.abs(value));
            
            default:
                return 0;
        }
    }

    static calculateStandardDeviation(data) {
        const mean = data.reduce((sum, val) => sum + val, 0) / data.length;
        const variance = data.reduce((sum, val) => sum + (val - mean)**2, 0) / (data.length - 1);
        return Math.sqrt(variance);
    }

    static calculateStandardError(data) {
        return this.calculateStandardDeviation(data) / Math.sqrt(data.length);
    }

    static linearRegression(xData, yData) {
        const n = xData.length;
        const sumX = xData.reduce((sum, x) => sum + x, 0);
        const sumY = yData.reduce((sum, y) => sum + y, 0);
        const sumXY = xData.reduce((sum, x, i) => sum + x * yData[i], 0);
        const sumXX = xData.reduce((sum, x) => sum + x * x, 0);
        
        const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
        const intercept = (sumY - slope * sumX) / n;
        
        // Calculate R²
        const yMean = sumY / n;
        const ssTotal = yData.reduce((sum, y) => sum + (y - yMean)**2, 0);
        const ssResidual = yData.reduce((sum, y, i) => {
            const predicted = slope * xData[i] + intercept;
            return sum + (y - predicted)**2;
        }, 0);
        const rSquared = 1 - (ssResidual / ssTotal);
        
        return { slope, intercept, rSquared };
    }
}

// Export the physics engine
if (typeof window !== 'undefined') {
    window.PhysicsEngine = PhysicsEngine;
    window.PhysicsUtils = PhysicsUtils;
    window.ErrorAnalysis = ErrorAnalysis;
} else if (typeof module !== 'undefined' && module.exports) {
    module.exports = { PhysicsEngine, PhysicsUtils, ErrorAnalysis };
}