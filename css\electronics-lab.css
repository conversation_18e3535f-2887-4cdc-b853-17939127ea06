/* Electronics Lab Specific Styling */

.electronics-lab {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Component Library Styling */
.component-item {
    position: relative;
    overflow: hidden;
}

.component-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    transform: translateX(-100%);
    transition: transform 0.3s ease;
}

.component-item:hover::before {
    transform: translateX(0);
}

.component-icon {
    transition: all 0.3s ease;
}

.component-item:hover .component-icon {
    transform: scale(1.2);
    color: #3182ce;
}

/* Breadboard Styling */
.breadboard-workspace {
    position: relative;
    background: linear-gradient(145deg, #2d5016, #1a365d);
    box-shadow: 
        inset 0 0 20px rgba(0, 0, 0, 0.3),
        0 4px 20px rgba(0, 0, 0, 0.2);
}

.breadboard-grid {
    background-image: 
        repeating-linear-gradient(
            0deg,
            rgba(255, 255, 255, 0.1) 0px,
            rgba(255, 255, 255, 0.1) 1px,
            transparent 1px,
            transparent 10px
        ),
        repeating-linear-gradient(
            90deg,
            rgba(255, 255, 255, 0.1) 0px,
            rgba(255, 255, 255, 0.1) 1px,
            transparent 1px,
            transparent 10px
        );
}

.power-rails {
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
}

.power-rails::before {
    content: '+';
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: white;
    font-weight: bold;
    font-size: 12px;
}

.power-rails::after {
    content: '-';
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: white;
    font-weight: bold;
    font-size: 12px;
}

/* Circuit Canvas */
#circuit-canvas {
    cursor: crosshair;
    border-radius: 8px;
    background: linear-gradient(145deg, #f8f9fa, #e9ecef);
}

#circuit-canvas:hover {
    box-shadow: 0 0 15px rgba(66, 153, 225, 0.3);
}

/* Experiment List Styling */
.experiment-item {
    position: relative;
    overflow: hidden;
}

.experiment-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(180deg, #667eea, #764ba2);
    transform: scaleY(0);
    transition: transform 0.3s ease;
}

.experiment-item:hover::before {
    transform: scaleY(1);
}

.experiment-item.active::before {
    transform: scaleY(1);
    background: linear-gradient(180deg, #48bb78, #38a169);
}

/* Measurement Display Styling */
.measurement-display {
    background: linear-gradient(145deg, #1a202c, #2d3748);
    box-shadow: 
        inset 0 2px 8px rgba(0, 0, 0, 0.3),
        0 1px 0 rgba(255, 255, 255, 0.1);
    text-shadow: 0 0 10px rgba(0, 255, 65, 0.5);
    position: relative;
    overflow: hidden;
}

.measurement-display::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 65, 0.1), transparent);
    animation: scan 3s ease-in-out infinite;
}

@keyframes scan {
    0% { left: -100%; }
    100% { left: 100%; }
}

.measurement-card {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.measurement-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(66, 153, 225, 0.2);
    border-color: #4299e1;
}

.measurement-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #4299e1, #3182ce);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.measurement-card:hover::before {
    transform: scaleX(1);
}

.measurement-value {
    font-family: 'Courier New', monospace;
    transition: all 0.3s ease;
}

.measurement-card:hover .measurement-value {
    color: #4299e1;
    transform: scale(1.1);
}

/* Analysis Controls */
.analysis-btn {
    position: relative;
    overflow: hidden;
}

.analysis-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.3s ease;
}

.analysis-btn:hover::before {
    width: 100px;
    height: 100px;
}

.analysis-btn:active {
    transform: translateY(1px);
}

/* Tool Grid Styling */
.tool-item {
    position: relative;
    overflow: hidden;
}

.tool-item::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.tool-item:hover::after {
    transform: translateX(100%);
}

.tool-icon {
    transition: all 0.3s ease;
}

.tool-item:hover .tool-icon {
    transform: rotateY(360deg);
}

/* Circuit Analysis Canvas */
.circuit-canvas {
    transition: all 0.3s ease;
    cursor: pointer;
}

.circuit-canvas:hover {
    transform: scale(1.02);
    box-shadow: 0 4px 15px rgba(66, 153, 225, 0.2);
}

/* Lab Controls Styling */
.lab-btn {
    position: relative;
    overflow: hidden;
}

.lab-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.lab-btn:hover::before {
    left: 100%;
}

/* Power Button Special Styling */
#power-btn {
    transition: all 0.3s ease;
}

#power-btn.powered {
    background: linear-gradient(145deg, #e53e3e, #c53030);
    box-shadow: 0 0 20px rgba(229, 62, 62, 0.4);
    animation: power-pulse 2s ease-in-out infinite;
}

@keyframes power-pulse {
    0%, 100% { box-shadow: 0 0 20px rgba(229, 62, 62, 0.4); }
    50% { box-shadow: 0 0 30px rgba(229, 62, 62, 0.6); }
}

/* Simulate Button Animation */
#simulate-btn.running {
    animation: simulate-spin 1s linear infinite;
}

@keyframes simulate-spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Results Panel Styling */
#results-display {
    background: linear-gradient(145deg, #f7fafc, #edf2f7);
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e2e8f0;
    min-height: 100px;
}

#analysis-results {
    background: linear-gradient(145deg, #f7fafc, #edf2f7);
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e2e8f0;
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    line-height: 1.4;
    max-height: 200px;
    overflow-y: auto;
}

#analysis-results::-webkit-scrollbar {
    width: 6px;
}

#analysis-results::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

#analysis-results::-webkit-scrollbar-thumb {
    background: #4299e1;
    border-radius: 3px;
}

/* Component Drag and Drop Effects */
.component-item.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
}

.breadboard-workspace.drag-over {
    background: linear-gradient(145deg, #2d5016, #1a365d);
    box-shadow: 
        inset 0 0 20px rgba(66, 153, 225, 0.3),
        0 4px 20px rgba(66, 153, 225, 0.2);
}

/* Instrument Integration */
#active-instrument {
    background: linear-gradient(145deg, #f8f9fa, #e9ecef);
    border-radius: 10px;
    padding: 15px;
    border: 2px solid #e2e8f0;
    min-height: 200px;
}

#active-instrument .virtual-instrument {
    margin: 0;
    background: transparent;
    box-shadow: none;
    border: none;
}

/* Responsive Adjustments */
@media (max-width: 1200px) {
    .component-grid {
        grid-template-columns: 1fr;
    }
    
    .measurement-grid {
        grid-template-columns: 1fr;
    }
    
    .tool-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .lab-controls {
        flex-direction: column;
        gap: 8px;
    }
    
    .lab-btn {
        width: 100%;
        justify-content: center;
    }
    
    .analysis-controls {
        flex-direction: column;
    }
    
    .analysis-btn {
        width: 100%;
    }
    
    .breadboard-workspace {
        height: 200px;
    }
    
    #circuit-canvas {
        height: 180px;
    }
}

/* Loading States */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(66, 153, 225, 0.2), transparent);
    animation: loading-sweep 1.5s ease-in-out infinite;
}

@keyframes loading-sweep {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Success/Error States */
.success {
    border-color: #48bb78 !important;
    background: linear-gradient(145deg, #f0fff4, #c6f6d5) !important;
}

.error {
    border-color: #e53e3e !important;
    background: linear-gradient(145deg, #fed7d7, #feb2b2) !important;
}

/* Tooltip Styling */
.tooltip {
    position: absolute;
    background: rgba(45, 55, 72, 0.9);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 0.8rem;
    z-index: 1000;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.tooltip.show {
    opacity: 1;
}

/* Circuit Component Highlighting */
.component-highlight {
    box-shadow: 0 0 15px rgba(66, 153, 225, 0.6);
    border: 2px solid #4299e1;
}

.wire-highlight {
    stroke: #e53e3e;
    stroke-width: 4;
    filter: drop-shadow(0 0 5px rgba(229, 62, 62, 0.6));
}

/* Measurement Animation */
.measuring {
    animation: measure-pulse 1s ease-in-out infinite;
}

@keyframes measure-pulse {
    0%, 100% { 
        background: linear-gradient(145deg, #1a202c, #2d3748);
    }
    50% { 
        background: linear-gradient(145deg, #2d3748, #4a5568);
    }
}
