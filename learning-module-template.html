<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قالب وحدة التعلم التفاعلية - SimLab HUB</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: '<PERSON><PERSON><PERSON>', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            overflow: hidden;
            height: 100vh;
        }

        .slideshow-container {
            position: relative;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .slide {
            display: none;
            width: 95%;
            max-width: 1200px;
            height: 90vh;
            background: rgba(255, 255, 255, 0.98);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 40px;
            overflow-y: auto;
            animation: slideIn 0.5s ease-in-out;
        }

        .slide.active {
            display: block;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .slide-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #667eea;
        }

        .slide-number {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
            display: inline-block;
            margin-bottom: 10px;
        }

        .slide-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }

        .slide-subtitle {
            font-size: 1.2rem;
            color: #4a5568;
            font-weight: 400;
        }

        .slide-content {
            line-height: 1.8;
            color: #2d3748;
        }

        .concept-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }

        .concept-card {
            background: linear-gradient(135deg, #f7fafc, #edf2f7);
            border-radius: 15px;
            padding: 25px;
            border: 2px solid #e2e8f0;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .concept-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .concept-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .concept-card:hover::before {
            transform: scaleX(1);
        }

        .concept-icon {
            font-size: 2.5rem;
            color: #667eea;
            margin-bottom: 15px;
        }

        .concept-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 10px;
        }

        .concept-description {
            color: #4a5568;
            line-height: 1.6;
        }

        .interactive-diagram {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            border: 2px solid #e2e8f0;
            position: relative;
            overflow: hidden;
        }

        .diagram-container {
            position: relative;
            width: 100%;
            height: 300px;
            background: linear-gradient(135deg, #f7fafc, #edf2f7);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .circuit-element {
            position: absolute;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .circuit-element:hover {
            transform: scale(1.2);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.5);
        }

        .circuit-element.active {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .connection-line {
            position: absolute;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
            animation: flow 2s infinite;
        }

        @keyframes flow {
            0% { box-shadow: 0 0 0 rgba(102, 126, 234, 0.8); }
            50% { box-shadow: 0 0 20px rgba(102, 126, 234, 0.8); }
            100% { box-shadow: 0 0 0 rgba(102, 126, 234, 0.8); }
        }

        .info-tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 10px 15px;
            border-radius: 8px;
            font-size: 0.9rem;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s ease;
            z-index: 1000;
            max-width: 200px;
            text-align: center;
        }

        .info-tooltip.show {
            opacity: 1;
        }

        .simulation-embed {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            border: 2px solid #e2e8f0;
            text-align: center;
        }

        .simulation-frame {
            width: 100%;
            height: 400px;
            border: none;
            border-radius: 10px;
            background: linear-gradient(135deg, #f7fafc, #edf2f7);
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            color: #4a5568;
            font-size: 1.1rem;
        }

        .simulation-controls {
            margin-top: 20px;
            display: flex;
            justify-content: center;
            gap: 15px;
        }

        .control-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .real-world-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 30px 0;
        }

        .real-world-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            border: 2px solid #e2e8f0;
            transition: all 0.3s ease;
        }

        .real-world-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        .card-image {
            width: 100%;
            height: 200px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 3rem;
        }

        .card-content {
            padding: 20px;
        }

        .card-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 10px;
        }

        .card-description {
            color: #4a5568;
            line-height: 1.6;
        }

        .quiz-section {
            background: linear-gradient(135deg, #f7fafc, #edf2f7);
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            border: 2px solid #e2e8f0;
        }

        .quiz-question {
            font-size: 1.2rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 20px;
        }

        .quiz-options {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .quiz-option {
            background: white;
            padding: 15px 20px;
            border-radius: 10px;
            border: 2px solid #e2e8f0;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .quiz-option:hover {
            border-color: #667eea;
            background: #f0f4ff;
        }

        .quiz-option.selected {
            border-color: #667eea;
            background: #667eea;
            color: white;
        }

        .quiz-option.correct {
            border-color: #48bb78;
            background: #48bb78;
            color: white;
        }

        .quiz-option.incorrect {
            border-color: #f56565;
            background: #f56565;
            color: white;
        }

        .navigation {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 15px;
            background: rgba(255, 255, 255, 0.95);
            padding: 15px 25px;
            border-radius: 25px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            backdrop-filter: blur(10px);
        }

        .nav-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .nav-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .progress-bar {
            position: fixed;
            top: 0;
            left: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transition: width 0.3s ease;
            z-index: 1000;
        }

        .slide-counter {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.9);
            padding: 10px 20px;
            border-radius: 20px;
            font-weight: 500;
            color: #2d3748;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .learning-objectives {
            background: linear-gradient(135deg, #e6fffa, #b2f5ea);
            border-radius: 15px;
            padding: 25px;
            margin: 25px 0;
            border-right: 4px solid #38b2ac;
        }

        .objectives-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .objectives-list {
            list-style: none;
            padding: 0;
        }

        .objectives-list li {
            padding: 8px 0;
            position: relative;
            padding-right: 25px;
        }

        .objectives-list li::before {
            content: '✓';
            position: absolute;
            right: 0;
            color: #38b2ac;
            font-weight: bold;
            font-size: 1.2rem;
        }

        .animated-process {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            background: white;
            border-radius: 15px;
            margin: 20px 0;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .process-step {
            flex: 1;
            text-align: center;
            padding: 20px;
            position: relative;
        }

        .process-step::after {
            content: '';
            position: absolute;
            left: 100%;
            top: 50%;
            transform: translateY(-50%);
            width: 30px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            animation: flowRight 3s infinite;
        }

        .process-step:last-child::after {
            display: none;
        }

        @keyframes flowRight {
            0% { opacity: 0; }
            50% { opacity: 1; }
            100% { opacity: 0; }
        }

        .process-icon {
            font-size: 2rem;
            color: #667eea;
            margin-bottom: 10px;
        }

        .process-title {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 5px;
        }

        .process-description {
            font-size: 0.9rem;
            color: #4a5568;
        }

        @media (max-width: 768px) {
            .slide {
                width: 98%;
                height: 95vh;
                padding: 20px;
            }
            
            .slide-title {
                font-size: 2rem;
            }
            
            .concept-grid {
                grid-template-columns: 1fr;
            }
            
            .real-world-section {
                grid-template-columns: 1fr;
            }
            
            .navigation {
                bottom: 15px;
                padding: 10px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="progress-bar" id="progressBar"></div>
    <div class="slide-counter" id="slideCounter">1 / 9</div>

    <div class="slideshow-container">
        <!-- Slide 1: Title Slide -->
        <div class="slide active">
            <div class="slide-header">
                <div class="slide-number">الشريحة 1</div>
                <h1 class="slide-title">الدوائر الكهربائية الأساسية</h1>
                <p class="slide-subtitle">مقدمة في عالم الكهرباء والإلكترونيات</p>
            </div>
            <div class="slide-content">
                <div class="card-image" style="height: 300px; margin: 30px 0; border-radius: 15px;">
                    <i class="fas fa-bolt"></i>
                </div>
                <div style="text-align: center; padding: 20px;">
                    <h2 style="color: #667eea; margin-bottom: 15px;">أهلاً بك في رحلة استكشاف الكهرباء</h2>
                    <p style="font-size: 1.1rem; color: #4a5568; max-width: 600px; margin: 0 auto;">
                        سنتعلم معاً كيفية عمل الدوائر الكهربائية، من المفاهيم الأساسية إلى التطبيقات العملية في حياتنا اليومية
                    </p>
                </div>
            </div>
        </div>

        <!-- Slide 2: Introduction & Learning Objectives -->
        <div class="slide">
            <div class="slide-header">
                <div class="slide-number">الشريحة 2</div>
                <h1 class="slide-title">مقدمة وأهداف التعلم</h1>
                <p class="slide-subtitle">ما سنتعلمه في هذه الوحدة</p>
            </div>
            <div class="slide-content">
                <div class="learning-objectives">
                    <h3 class="objectives-title">
                        <i class="fas fa-bullseye"></i>
                        أهداف التعلم
                    </h3>
                    <ul class="objectives-list">
                        <li>فهم المفاهيم الأساسية للكهرباء والتيار الكهربائي</li>
                        <li>تعلم قانون أوم وتطبيقاته العملية</li>
                        <li>التعرف على أنواع الدوائر الكهربائية المختلفة</li>
                        <li>فهم دور المقاومة والمكثف في الدوائر</li>
                        <li>تطبيق المعرفة في حل مسائل عملية</li>
                        <li>ربط المفاهيم بالتطبيقات اليومية</li>
                    </ul>
                </div>
                <div style="background: white; padding: 30px; border-radius: 15px; margin-top: 30px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                    <h3 style="color: #667eea; margin-bottom: 20px; text-align: center;">
                        <i class="fas fa-lightbulb"></i>
                        لماذا ندرس الدوائر الكهربائية؟
                    </h3>
                    <p style="font-size: 1.1rem; line-height: 1.8; color: #4a5568; text-align: center;">
                        الكهرباء هي أساس الحياة المعاصرة، من الهواتف الذكية إلى السيارات الكهربائية، 
                        فهم الدوائر الكهربائية يفتح لنا آفاقاً واسعة في عالم التكنولوجيا والابتكار
                    </p>
                </div>
            </div>
        </div>

        <!-- Slide 3: Basic Concepts -->
        <div class="slide">
            <div class="slide-header">
                <div class="slide-number">الشريحة 3</div>
                <h1 class="slide-title">المفاهيم الأساسية</h1>
                <p class="slide-subtitle">العناصر الأساسية للدوائر الكهربائية</p>
            </div>
            <div class="slide-content">
                <div class="concept-grid">
                    <div class="concept-card" onclick="showConceptDetails('voltage')">
                        <div class="concept-icon">
                            <i class="fas fa-battery-three-quarters"></i>
                        </div>
                        <h3 class="concept-title">الجهد الكهربائي (V)</h3>
                        <p class="concept-description">
                            القوة الدافعة التي تحرك الإلكترونات في الدائرة، يُقاس بالفولت
                        </p>
                    </div>
                    <div class="concept-card" onclick="showConceptDetails('current')">
                        <div class="concept-icon">
                            <i class="fas fa-long-arrow-alt-right"></i>
                        </div>
                        <h3 class="concept-title">التيار الكهربائي (I)</h3>
                        <p class="concept-description">
                            معدل تدفق الشحنات الكهربائية عبر موصل، يُقاس بالأمبير
                        </p>
                    </div>
                    <div class="concept-card" onclick="showConceptDetails('resistance')">
                        <div class="concept-icon">
                            <i class="fas fa-minus"></i>
                        </div>
                        <h3 class="concept-title">المقاومة (R)</h3>
                        <p class="concept-description">
                            مقدار مقاومة المادة لتدفق التيار الكهربائي، تُقاس بالأوم
                        </p>
                    </div>
                    <div class="concept-card" onclick="showConceptDetails('power')">
                        <div class="concept-icon">
                            <i class="fas fa-bolt"></i>
                        </div>
                        <h3 class="concept-title">القدرة (P)</h3>
                        <p class="concept-description">
                            معدل استهلاك أو إنتاج الطاقة الكهربائية، تُقاس بالواط
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 4: Interactive Circuit Diagram -->
        <div class="slide">
            <div class="slide-header">
                <div class="slide-number">الشريحة 4</div>
                <h1 class="slide-title">دائرة كهربائية تفاعلية</h1>
                <p class="slide-subtitle">اضغط على العناصر لاستكشاف وظائفها</p>
            </div>
            <div class="slide-content">
                <div class="interactive-diagram">
                    <h3 style="text-align: center; margin-bottom: 20px; color: #667eea;">
                        <i class="fas fa-mouse-pointer"></i>
                        اضغط على العناصر لمعرفة المزيد
                    </h3>
                    <div class="diagram-container">
                        <!-- Circuit Elements -->
                        <div class="circuit-element" style="top: 50px; left: 100px;" onclick="showElementInfo('بطارية', 'مصدر الطاقة الكهربائية - توفر الجهد اللازم لتشغيل الدائرة')">
                            <i class="fas fa-battery-three-quarters"></i>
                        </div>
                        <div class="circuit-element" style="top: 50px; right: 100px;" onclick="showElementInfo('مصباح', 'حمل كهربائي - يحول الطاقة الكهربائية إلى ضوء وحرارة')">
                            <i class="fas fa-lightbulb"></i>
                        </div>
                        <div class="circuit-element" style="bottom: 50px; left: 100px;" onclick="showElementInfo('مقاومة', 'عنصر يحد من تدفق التيار - يحمي الدائرة من التلف')">
                            <i class="fas fa-minus"></i>
                        </div>
                        <div class="circuit-element" style="bottom: 50px; right: 100px;" onclick="showElementInfo('مفتاح', 'يتحكم في تدفق التيار - يفتح أو يغلق الدائرة')">
                            <i class="fas fa-toggle-on"></i>
                        </div>
                        
                        <!-- Connection Lines -->
                        <div class="connection-line" style="top: 75px; left: 160px; width: 480px;"></div>
                        <div class="connection-line" style="bottom: 75px; left: 160px; width: 480px;"></div>
                        <div class="connection-line" style="top: 110px; left: 125px; width: 3px; height: 150px;"></div>
                        <div class="connection-line" style="top: 110px; right: 125px; width: 3px; height: 150px;"></div>
                    </div>
                    <div class="info-tooltip" id="elementTooltip"></div>
                </div>
            </div>
        </div>

        <!-- Slide 5: Ohm's Law Animation -->
        <div class="slide">
            <div class="slide-header">
                <div class="slide-number">الشريحة 5</div>
                <h1 class="slide-title">قانون أوم</h1>
                <p class="slide-subtitle">العلاقة بين الجهد والتيار والمقاومة</p>
            </div>
            <div class="slide-content">
                <div class="animated-process">
                    <div class="process-step">
                        <div class="process-icon">
                            <i class="fas fa-battery-three-quarters"></i>
                        </div>
                        <div class="process-title">الجهد (V)</div>
                        <div class="process-description">القوة الدافعة</div>
                    </div>
                    <div class="process-step">
                        <div class="process-icon">
                            <i class="fas fa-equals"></i>
                        </div>
                        <div class="process-title">يساوي</div>
                        <div class="process-description">V = I × R</div>
                    </div>
                    <div class="process-step">
                        <div class="process-icon">
                            <i class="fas fa-long-arrow-alt-right"></i>
                        </div>
                        <div class="process-title">التيار (I)</div>
                        <div class="process-description">معدل التدفق</div>
                    </div>
                    <div class="process-step">
                        <div class="process-icon">
                            <i class="fas fa-times"></i>
                        </div>
                        <div class="process-title">مضروب في</div>
                        <div class="process-description">×</div>
                    </div>
                    <div class="process-step">
                        <div class="process-icon">
                            <i class="fas fa-minus"></i>
                        </div>
                        <div class="process-title">المقاومة (R)</div>
                        <div class="process-description">مقاومة التدفق</div>
                    </div>
                </div>
                
                <div style="background: linear-gradient(135deg, #f0f4ff, #e6f3ff); padding: 30px; border-radius: 15px; margin: 30px 0; text-align: center;">
                    <h3 style="color: #667eea; margin-bottom: 20px; font-size: 1.5rem;">
                        <i class="fas fa-calculator"></i>
                        مثال تطبيقي
                    </h3>
                    <div style="background: white; padding: 20px; border-radius: 10px; margin: 20px 0;">
                        <p style="font-size: 1.1rem; color: #2d3748; margin-bottom: 15px;">
                            إذا كان الجهد = 12 فولت، والمقاومة = 4 أوم
                        </p>
                        <p style="font-size: 1.2rem; color: #667eea; font-weight: 600;">
                            التيار = 12 ÷ 4 = 3 أمبير
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 6: Interactive Simulation -->
        <div class="slide">
            <div class="slide-header">
                <div class="slide-number">الشريحة 6</div>
                <h1 class="slide-title">محاكاة تفاعلية</h1>
                <p class="slide-subtitle">جرب بناء دائرة كهربائية بنفسك</p>
            </div>
            <div class="slide-content">
                <div class="simulation-embed">
                    <h3 style="color: #667eea; margin-bottom: 20px;">
                        <i class="fas fa-play-circle"></i>
                        محاكي الدوائر الكهربائية
                    </h3>
                    <div class="simulation-frame">
                        <i class="fas fa-cogs" style="font-size: 4rem; margin-bottom: 20px; color: #667eea;"></i>
                        <p style="font-size: 1.2rem; margin-bottom: 20px;">محاكي الدوائر الكهربائية التفاعلي</p>
                        <p style="font-size: 1rem; color: #4a5568;">
                            اسحب العناصر وأسقطها لبناء دائرتك الكهربائية
                        </p>
                    </div>
                    <div class="simulation-controls">
                        <button class="control-btn" onclick="startSimulation()">
                            <i class="fas fa-play"></i>
                            تشغيل المحاكاة
                        </button>
                        <button class="control-btn" onclick="resetSimulation()">
                            <i class="fas fa-redo"></i>
                            إعادة تعيين
                        </button>
                        <button class="control-btn" onclick="saveCircuit()">
                            <i class="fas fa-save"></i>
                            حفظ الدائرة
                        </button>
                    </div>
                </div>
                
                <div style="background: linear-gradient(135deg, #fff5f5, #fed7d7); padding: 20px; border-radius: 15px; margin-top: 20px; border-right: 4px solid #f56565;">
                    <h4 style="color: #c53030; margin-bottom: 10px;">
                        <i class="fas fa-exclamation-triangle"></i>
                        تعليمات الأمان
                    </h4>
                    <p style="color: #742a2a; font-size: 0.9rem;">
                        تذكر دائماً: لا تتعامل مع الكهرباء الحقيقية بدون إشراف خبير، واستخدم دائماً معدات الحماية المناسبة
                    </p>
                </div>
            </div>
        </div>

        <!-- Slide 7: Real-World Applications -->
        <div class="slide">
            <div class="slide-header">
                <div class="slide-number">الشريحة 7</div>
                <h1 class="slide-title">التطبيقات العملية</h1>
                <p class="slide-subtitle">الدوائر الكهربائية في حياتنا اليومية</p>
            </div>
            <div class="slide-content">
                <div class="real-world-section">
                    <div class="real-world-card">
                        <div class="card-image">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <div class="card-content">
                            <h3 class="card-title">الهواتف الذكية</h3>
                            <p class="card-description">
                                تحتوي على دوائر معقدة تتحكم في المعالج، الشاشة، والبطارية
                            </p>
                        </div>
                    </div>
                    <div class="real-world-card">
                        <div class="card-image">
                            <i class="fas fa-car"></i>
                        </div>
                        <div class="card-content">
                            <h3 class="card-title">السيارات الحديثة</h3>
                            <p class="card-description">
                                تعتمد على شبكة معقدة من الدوائر للتحكم في المحرك والأمان
                            </p>
                        </div>
                    </div>
                    <div class="real-world-card">
                        <div class="card-image">
                            <i class="fas fa-home"></i>
                        </div>
                        <div class="card-content">
                            <h3 class="card-title">المنازل الذكية</h3>
                            <p class="card-description">
                                دوائر الإضاءة والتحكم الآلي تجعل المنزل أكثر راحة وأماناً
                            </p>
                        </div>
                    </div>
                    <div class="real-world-card">
                        <div class="card-image">
                            <i class="fas fa-hospital"></i>
                        </div>
                        <div class="card-content">
                            <h3 class="card-title">الأجهزة الطبية</h3>
                            <p class="card-description">
                                أجهزة القلب والمراقبة تعتمد على دوائر دقيقة لإنقاذ الأرواح
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 8: Summary -->
        <div class="slide">
            <div class="slide-header">
                <div class="slide-number">الشريحة 8</div>
                <h1 class="slide-title">ملخص الدرس</h1>
                <p class="slide-subtitle">النقاط الرئيسية التي تعلمناها</p>
            </div>
            <div class="slide-content">
                <div style="background: linear-gradient(135deg, #f0fff4, #c6f6d5); padding: 30px; border-radius: 15px; margin: 30px 0; border-right: 4px solid #48bb78;">
                    <h3 style="color: #2f855a; margin-bottom: 20px; text-align: center;">
                        <i class="fas fa-check-circle"></i>
                        ما تعلمناه اليوم
                    </h3>
                    <div class="concept-grid">
                        <div style="background: white; padding: 20px; border-radius: 10px; text-align: center;">
                            <i class="fas fa-battery-three-quarters" style="font-size: 2rem; color: #667eea; margin-bottom: 10px;"></i>
                            <h4 style="color: #2d3748; margin-bottom: 10px;">المفاهيم الأساسية</h4>
                            <p style="color: #4a5568; font-size: 0.9rem;">الجهد، التيار، المقاومة، والقدرة</p>
                        </div>
                        <div style="background: white; padding: 20px; border-radius: 10px; text-align: center;">
                            <i class="fas fa-calculator" style="font-size: 2rem; color: #667eea; margin-bottom: 10px;"></i>
                            <h4 style="color: #2d3748; margin-bottom: 10px;">قانون أوم</h4>
                            <p style="color: #4a5568; font-size: 0.9rem;">V = I × R والتطبيقات العملية</p>
                        </div>
                        <div style="background: white; padding: 20px; border-radius: 10px; text-align: center;">
                            <i class="fas fa-cogs" style="font-size: 2rem; color: #667eea; margin-bottom: 10px;"></i>
                            <h4 style="color: #2d3748; margin-bottom: 10px;">التجريب العملي</h4>
                            <p style="color: #4a5568; font-size: 0.9rem;">بناء ومحاكاة الدوائر الكهربائية</p>
                        </div>
                        <div style="background: white; padding: 20px; border-radius: 10px; text-align: center;">
                            <i class="fas fa-globe" style="font-size: 2rem; color: #667eea; margin-bottom: 10px;"></i>
                            <h4 style="color: #2d3748; margin-bottom: 10px;">التطبيقات الحقيقية</h4>
                            <p style="color: #4a5568; font-size: 0.9rem;">الدوائر في الحياة اليومية والتكنولوجيا</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 9: Assessment -->
        <div class="slide">
            <div class="slide-header">
                <div class="slide-number">الشريحة 9</div>
                <h1 class="slide-title">اختبار الفهم</h1>
                <p class="slide-subtitle">اختبر معرفتك بما تعلمته</p>
            </div>
            <div class="slide-content">
                <div class="quiz-section">
                    <div class="quiz-question">
                        <i class="fas fa-question-circle"></i>
                        ما هو قانون أوم؟
                    </div>
                    <div class="quiz-options">
                        <div class="quiz-option" onclick="selectAnswer(this, false)">
                            <span>أ)</span>
                            <span>V = I + R</span>
                        </div>
                        <div class="quiz-option" onclick="selectAnswer(this, true)">
                            <span>ب)</span>
                            <span>V = I × R</span>
                        </div>
                        <div class="quiz-option" onclick="selectAnswer(this, false)">
                            <span>ج)</span>
                            <span>V = I - R</span>
                        </div>
                        <div class="quiz-option" onclick="selectAnswer(this, false)">
                            <span>د)</span>
                            <span>V = I ÷ R</span>
                        </div>
                    </div>
                </div>
                
                <div style="background: linear-gradient(135deg, #fffaf0, #feebc8); padding: 25px; border-radius: 15px; margin-top: 30px; border-right: 4px solid #ed8936;">
                    <h3 style="color: #c05621; margin-bottom: 15px;">
                        <i class="fas fa-lightbulb"></i>
                        المزيد من التعلم
                    </h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
                        <div>
                            <h4 style="color: #2d3748; margin-bottom: 8px;">محاكاة متقدمة</h4>
                            <p style="color: #4a5568; font-size: 0.9rem;">جرب المحاكاة المتقدمة للدوائر المعقدة</p>
                        </div>
                        <div>
                            <h4 style="color: #2d3748; margin-bottom: 8px;">مشاريع عملية</h4>
                            <p style="color: #4a5568; font-size: 0.9rem;">ابن دوائر حقيقية مع مرشد متخصص</p>
                        </div>
                        <div>
                            <h4 style="color: #2d3748; margin-bottom: 8px;">تحديات إضافية</h4>
                            <p style="color: #4a5568; font-size: 0.9rem;">حل مسائل متقدمة في الدوائر الكهربائية</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="navigation">
        <button class="nav-btn" id="prevBtn" onclick="changeSlide(-1)">
            <i class="fas fa-chevron-right"></i>
            السابق
        </button>
        <button class="nav-btn" id="nextBtn" onclick="changeSlide(1)">
            التالي
            <i class="fas fa-chevron-left"></i>
        </button>
    </div>

    <script>
        let currentSlide = 0;
        const slides = document.querySelectorAll('.slide');
        const totalSlides = slides.length;

        function showSlide(n) {
            slides[currentSlide].classList.remove('active');
            currentSlide = (n + totalSlides) % totalSlides;
            slides[currentSlide].classList.add('active');
            
            // Update progress bar
            const progress = ((currentSlide + 1) / totalSlides) * 100;
            document.getElementById('progressBar').style.width = progress + '%';
            
            // Update slide counter
            document.getElementById('slideCounter').textContent = `${currentSlide + 1} / ${totalSlides}`;
            
            // Update navigation buttons
            document.getElementById('prevBtn').disabled = currentSlide === 0;
            document.getElementById('nextBtn').disabled = currentSlide === totalSlides - 1;
        }

        function changeSlide(n) {
            showSlide(currentSlide + n);
        }

        // Initialize
        showSlide(0);

        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') changeSlide(-1);
            if (e.key === 'ArrowRight') changeSlide(1);
        });

        // Interactive functions
        function showConceptDetails(concept) {
            const details = {
                'voltage': 'الجهد الكهربائي هو الطاقة الكامنة للشحنة الكهربائية. يُقاس بالفولت ويمثل القوة المحركة للتيار.',
                'current': 'التيار الكهربائي هو تدفق الشحنات الكهربائية. يُقاس بالأمبير ويمثل كمية الشحنة التي تمر في الثانية.',
                'resistance': 'المقاومة هي خاصية المواد لمقاومة تدفق التيار. تُقاس بالأوم وتختلف حسب المادة.',
                'power': 'القدرة الكهربائية هي معدل استهلاك الطاقة. تُقاس بالواط وتحسب بضرب الجهد في التيار.'
            };
            
            alert(details[concept]);
        }

        function showElementInfo(element, description) {
            const tooltip = document.getElementById('elementTooltip');
            tooltip.textContent = element + ': ' + description;
            tooltip.classList.add('show');
            
            setTimeout(() => {
                tooltip.classList.remove('show');
            }, 3000);
        }

        function selectAnswer(option, isCorrect) {
            // Remove previous selections
            document.querySelectorAll('.quiz-option').forEach(opt => {
                opt.classList.remove('selected', 'correct', 'incorrect');
            });
            
            // Mark selected option
            option.classList.add('selected');
            
            // Show correct/incorrect after delay
            setTimeout(() => {
                if (isCorrect) {
                    option.classList.add('correct');
                    alert('إجابة صحيحة! أحسنت 🎉');
                } else {
                    option.classList.add('incorrect');
                    // Also show correct answer
                    document.querySelectorAll('.quiz-option')[1].classList.add('correct');
                    alert('إجابة خاطئة. الإجابة الصحيحة هي: V = I × R');
                }
            }, 500);
        }

        function startSimulation() {
            alert('تم تشغيل المحاكاة! في النسخة الكاملة، ستفتح نافذة تفاعلية لبناء الدوائر.');
        }

        function resetSimulation() {
            alert('تم إعادة تعيين المحاكاة إلى الحالة الأولى.');
        }

        function saveCircuit() {
            alert('تم حفظ تصميم الدائرة في ملفك الشخصي.');
        }

        // Auto-advance slides (optional)
        function autoAdvance() {
            setInterval(() => {
                if (currentSlide < totalSlides - 1) {
                    changeSlide(1);
                }
            }, 30000); // 30 seconds per slide
        }

        // Uncomment to enable auto-advance
        // autoAdvance();
    </script>
</body>
</html>