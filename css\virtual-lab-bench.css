/* Virtual Lab Bench Styling */

.virtual-lab-bench {
    display: grid;
    grid-template-areas: 
        "equipment workspace controls"
        "safety safety safety";
    grid-template-columns: 300px 1fr 200px;
    grid-template-rows: 600px 200px;
    gap: 15px;
    height: 800px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Equipment Panel */
.equipment-panel {
    grid-area: equipment;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    overflow-y: auto;
}

.equipment-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    margin-top: 15px;
}

.equipment-item {
    background: linear-gradient(145deg, #f7fafc, #edf2f7);
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    padding: 12px;
    text-align: center;
    cursor: grab;
    transition: all 0.3s ease;
    user-select: none;
}

.equipment-item:hover {
    border-color: #4299e1;
    background: linear-gradient(145deg, #ebf8ff, #bee3f8);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(66, 153, 225, 0.2);
}

.equipment-item:active {
    cursor: grabbing;
    transform: scale(0.95);
}

.equipment-icon {
    font-size: 2rem;
    color: #4299e1;
    margin-bottom: 8px;
}

.equipment-name {
    font-size: 0.85rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 4px;
}

.equipment-info {
    font-size: 0.75rem;
    color: #4a5568;
}

/* Lab Workspace */
.lab-workspace {
    grid-area: workspace;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.workspace-surface {
    position: relative;
    width: 100%;
    height: 70%;
    background: radial-gradient(circle at center, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 10px;
    border: 2px solid #dee2e6;
    overflow: hidden;
}

.bench-grid {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        linear-gradient(rgba(0,0,0,0.05) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0,0,0,0.05) 1px, transparent 1px);
    background-size: 20px 20px;
}

.active-equipment {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.placed-equipment {
    position: absolute;
    cursor: move;
    z-index: 10;
}

.equipment-visual {
    background: rgba(255, 255, 255, 0.9);
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    padding: 10px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    min-width: 60px;
    min-height: 60px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.equipment-visual.container {
    min-height: 80px;
    position: relative;
}

.equipment-visual i {
    font-size: 1.5rem;
    color: #4299e1;
    margin-bottom: 5px;
}

.equipment-label {
    font-size: 0.7rem;
    font-weight: 600;
    color: #2d3748;
}

.container-visual {
    position: absolute;
    bottom: 5px;
    left: 5px;
    right: 5px;
    height: 40px;
    background: rgba(226, 232, 240, 0.3);
    border-radius: 4px;
    overflow: hidden;
}

.liquid-level {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: #e3f2fd;
    transition: height 0.5s ease;
    border-radius: 0 0 4px 4px;
}

.volume-indicator {
    position: absolute;
    top: -15px;
    left: 0;
    right: 0;
    font-size: 0.6rem;
    color: #4a5568;
    text-align: center;
}

.equipment-controls {
    position: absolute;
    top: -10px;
    right: -10px;
}

.mini-btn {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: none;
    background: #e53e3e;
    color: white;
    font-size: 0.7rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Reaction Viewer */
.reaction-viewer {
    position: absolute;
    bottom: 20px;
    left: 20px;
    right: 20px;
    height: 25%;
    background: rgba(248, 249, 250, 0.95);
    border-radius: 8px;
    border: 2px solid #e2e8f0;
    padding: 10px;
}

#reaction-canvas {
    width: 100%;
    height: calc(100% - 40px);
    border-radius: 5px;
    background: white;
    border: 1px solid #dee2e6;
}

.reaction-controls {
    display: flex;
    gap: 10px;
    margin-top: 10px;
    justify-content: center;
}

.reaction-btn {
    background: linear-gradient(145deg, #48bb78, #38a169);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.reaction-btn:hover {
    background: linear-gradient(145deg, #38a169, #2f855a);
    transform: translateY(-1px);
}

/* Controls Panel */
.controls-panel {
    grid-area: controls;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    overflow-y: auto;
}

.control-section {
    margin-bottom: 20px;
    padding: 15px;
    background: #f7fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.control-section h4 {
    color: #2d3748;
    font-size: 1rem;
    margin-bottom: 10px;
    font-weight: 600;
}

.temperature-control {
    margin-bottom: 10px;
}

.temperature-control input[type="range"] {
    width: 100%;
    margin: 8px 0;
}

.temp-display {
    text-align: center;
    font-size: 1.2rem;
    font-weight: 600;
    color: #2d3748;
    background: #edf2f7;
    padding: 8px;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
}

.heating-controls {
    display: flex;
    gap: 8px;
}

.control-btn {
    background: linear-gradient(145deg, #4299e1, #3182ce);
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 0.85rem;
    cursor: pointer;
    transition: all 0.3s ease;
    flex: 1;
}

.control-btn:hover {
    background: linear-gradient(145deg, #3182ce, #2c5282);
    transform: translateY(-1px);
}

.addition-controls {
    display: flex;
    gap: 8px;
    align-items: center;
    margin-top: 8px;
}

.addition-controls input {
    flex: 1;
    padding: 6px;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
}

.addition-controls span {
    font-size: 0.85rem;
    color: #4a5568;
}

#chemical-selector {
    width: 100%;
    padding: 8px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    background: white;
    margin-bottom: 8px;
}

/* Safety Panel */
.safety-panel {
    grid-area: safety;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
}

.panel-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e2e8f0;
}

.safety-status {
    margin-left: auto;
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-led {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #e53e3e;
    box-shadow: 0 0 10px rgba(229, 62, 62, 0.6);
    animation: pulse 2s ease-in-out infinite;
}

.status-led.safe {
    background: #48bb78;
    box-shadow: 0 0 10px rgba(72, 187, 120, 0.6);
    animation: none;
}

.status-led.warning {
    background: #ed8936;
    box-shadow: 0 0 10px rgba(237, 137, 54, 0.6);
}

.safety-alerts {
    flex: 1;
    overflow-y: auto;
    margin-bottom: 15px;
}

.safety-alert {
    background: #fed7d7;
    border: 1px solid #fc8181;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 10px;
    display: flex;
    align-items: flex-start;
    gap: 10px;
}

.safety-alert.warning {
    background: #fefcbf;
    border-color: #f6e05e;
}

.safety-alert.info {
    background: #bee3f8;
    border-color: #63b3ed;
}

.alert-content {
    display: flex;
    align-items: center;
    gap: 10px;
    width: 100%;
}

.alert-message {
    flex: 1;
    font-size: 0.9rem;
    line-height: 1.4;
}

.alert-close {
    background: none;
    border: none;
    color: #4a5568;
    cursor: pointer;
    padding: 2px;
}

.emergency-controls {
    display: flex;
    gap: 10px;
}

.emergency-btn {
    background: linear-gradient(145deg, #e53e3e, #c53030);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    flex: 1;
    text-transform: uppercase;
}

.emergency-btn:hover {
    background: linear-gradient(145deg, #c53030, #9c2626);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(197, 48, 48, 0.4);
}

.safety-btn {
    background: linear-gradient(145deg, #4299e1, #3182ce);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    flex: 1;
}

.safety-btn:hover {
    background: linear-gradient(145deg, #3182ce, #2c5282);
    transform: translateY(-1px);
}

/* Equipment Animations */
.heating {
    animation: heat-glow 1s ease-in-out infinite alternate;
}

@keyframes heat-glow {
    from { box-shadow: 0 0 5px rgba(255, 69, 0, 0.5); }
    to { box-shadow: 0 0 20px rgba(255, 69, 0, 0.8); }
}

.stirring {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Safety Modal */
.safety-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 12px;
    padding: 30px;
    max-width: 500px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.modal-content h3 {
    color: #2d3748;
    margin-bottom: 20px;
    font-size: 1.5rem;
}

.safety-rules ul {
    list-style-type: none;
    padding: 0;
}

.safety-rules li {
    padding: 8px 0;
    border-bottom: 1px solid #e2e8f0;
    position: relative;
    padding-left: 20px;
}

.safety-rules li::before {
    content: '⚠️';
    position: absolute;
    left: 0;
}

.modal-content button {
    background: linear-gradient(145deg, #4299e1, #3182ce);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    margin-top: 20px;
    width: 100%;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .virtual-lab-bench {
        grid-template-areas: 
            "equipment workspace"
            "controls workspace"
            "safety safety";
        grid-template-columns: 250px 1fr;
        grid-template-rows: auto auto 150px;
    }
}

@media (max-width: 768px) {
    .virtual-lab-bench {
        grid-template-areas: 
            "workspace"
            "equipment"
            "controls"
            "safety";
        grid-template-columns: 1fr;
        grid-template-rows: 400px auto auto auto;
        height: auto;
    }
    
    .equipment-grid {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .emergency-controls {
        flex-direction: column;
    }
}
