<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مختبر الفيزياء التفاعلي - Interactive Physics Lab | SimLab HUB</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800&display=swap" rel="stylesheet">
    <link href="css/virtual-instruments.css" rel="stylesheet">
    <link href="css/language-switcher.css" rel="stylesheet">
    <link href="css/footer-styles.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: '<PERSON><PERSON><PERSON>', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #2d3748;
        }

        .lab-container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 20px;
        }

        .lab-header {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .lab-title {
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 15px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .lab-description {
            font-size: 1.2rem;
            color: #4a5568;
            max-width: 800px;
            margin: 0 auto;
            line-height: 1.6;
        }

        .experiments-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .experiment-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .experiment-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .experiment-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .experiment-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2.5rem;
            margin: 0 auto 20px;
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }

        .experiment-title {
            font-size: 1.4rem;
            font-weight: 700;
            margin-bottom: 10px;
            text-align: center;
            color: #2d3748;
        }

        .experiment-subtitle {
            font-size: 1rem;
            color: #4a5568;
            text-align: center;
            margin-bottom: 15px;
        }

        .experiment-description {
            color: #4a5568;
            margin-bottom: 20px;
            text-align: center;
            line-height: 1.6;
            font-size: 0.95rem;
        }

        .experiment-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            font-size: 0.85rem;
            color: #718096;
        }

        .difficulty {
            padding: 4px 12px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.8rem;
        }

        .difficulty.easy {
            background: #c6f6d5;
            color: #22543d;
        }

        .difficulty.medium {
            background: #fed7aa;
            color: #9c4221;
        }

        .difficulty.hard {
            background: #fed7d7;
            color: #742a2a;
        }

        .experiment-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
        }

        .btn {
            background: linear-gradient(145deg, #4299e1, #3182ce);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 10px;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(66, 153, 225, 0.3);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn:hover {
            background: linear-gradient(145deg, #3182ce, #2c5282);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(66, 153, 225, 0.4);
        }

        .btn.secondary {
            background: linear-gradient(145deg, #48bb78, #38a169);
        }

        .btn.secondary:hover {
            background: linear-gradient(145deg, #38a169, #2f855a);
        }

        .experiment-interface {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            display: none;
        }

        .experiment-interface.active {
            display: block;
        }

        .experiment-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e2e8f0;
        }

        .experiment-header h2 {
            font-size: 2rem;
            font-weight: 700;
            color: #2d3748;
        }

        .experiment-controls {
            display: flex;
            gap: 15px;
        }

        .experiment-content {
            display: grid;
            grid-template-columns: 1fr 350px;
            gap: 30px;
        }

        .experiment-canvas {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            min-height: 600px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .experiment-sidebar {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .control-panel {
            background: #f7fafc;
            border-radius: 15px;
            padding: 20px;
            border: 1px solid #e2e8f0;
        }

        .control-panel h3 {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 20px;
            color: #2d3748;
            text-align: center;
        }

        .control-group {
            margin-bottom: 20px;
        }

        .control-group label {
            display: block;
            font-weight: 600;
            margin-bottom: 8px;
            color: #4a5568;
        }

        .control-group input[type="range"] {
            width: 100%;
            margin-bottom: 5px;
        }

        .control-group span {
            font-weight: 700;
            color: #2d3748;
        }

        .control-buttons {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-top: 20px;
        }

        .btn-reset {
            background: linear-gradient(145deg, #f56565, #e53e3e);
        }

        .btn-reset:hover {
            background: linear-gradient(145deg, #e53e3e, #c53030);
        }

        .btn-export {
            background: linear-gradient(145deg, #48bb78, #38a169);
        }

        .btn-export:hover {
            background: linear-gradient(145deg, #38a169, #2f855a);
        }

        .btn-stop {
            background: linear-gradient(145deg, #ed8936, #dd6b20);
        }

        .btn-stop:hover {
            background: linear-gradient(145deg, #dd6b20, #c05621);
        }

        .energy-display {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin-top: 15px;
            border: 1px solid #e2e8f0;
        }

        .energy-display h4 {
            margin-bottom: 10px;
            color: #2d3748;
            text-align: center;
        }

        #energy-values div {
            margin-bottom: 8px;
            font-size: 0.9rem;
            color: #4a5568;
        }

        @media (max-width: 1200px) {
            .experiment-content {
                grid-template-columns: 1fr;
            }
            
            .experiment-sidebar {
                order: -1;
            }
        }

        @media (max-width: 768px) {
            .lab-container {
                padding: 15px;
            }
            
            .lab-title {
                font-size: 2.5rem;
            }
            
            .experiments-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .experiment-header {
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }
            
            .experiment-controls {
                flex-direction: column;
                width: 100%;
            }
        }

        /* RTL Adjustments */
        [dir="rtl"] .experiment-content {
            grid-template-columns: 350px 1fr;
        }

        [dir="rtl"] .experiment-header {
            flex-direction: row-reverse;
        }

        [dir="rtl"] .experiment-controls {
            flex-direction: row-reverse;
        }

        @media (max-width: 1200px) {
            [dir="rtl"] .experiment-content {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="lab-container">
        <!-- Header -->
        <div class="lab-header">
            <h1 class="lab-title" data-translate="physics.title">
                <i class="fas fa-atom"></i>
                مختبر الفيزياء التفاعلي
            </h1>
            <p class="lab-description" data-translate="physics.description">
                استكشف قوانين الفيزياء من خلال تجارب تفاعلية متقدمة مع محاكاة واقعية وأدوات قياس دقيقة
            </p>
        </div>

        <!-- Experiments Grid -->
        <div class="experiments-grid" id="experiments-grid">
            <!-- سيتم ملء التجارب هنا بواسطة JavaScript -->
        </div>

        <!-- Experiment Interface -->
        <div class="experiment-interface" id="experiment-interface">
            <div class="experiment-header">
                <h2 id="current-experiment-title">تجربة الحركة</h2>
                <div class="experiment-controls">
                    <button class="btn btn-stop" onclick="stopCurrentExperiment()">
                        <i class="fas fa-stop"></i>
                        <span data-translate="ui.stop">إيقاف</span>
                    </button>
                </div>
            </div>
            
            <div class="experiment-content">
                <div class="experiment-canvas" id="experiment-canvas">
                    <!-- سيتم إدراج canvas التجربة هنا -->
                </div>
                
                <div class="experiment-sidebar">
                    <div id="experiment-controls">
                        <!-- سيتم إدراج متحكمات التجربة هنا -->
                    </div>
                    
                    <div id="experiment-data">
                        <!-- سيتم إدراج بيانات التجربة هنا -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/virtual-instruments.js"></script>
    <script src="js/interactive-physics-experiments.js"></script>
    <script src="js/language-system.js"></script>
    <script src="js/footer-component.js"></script>
    
    <script>
        // متغيرات عامة
        let physicsExperiments;
        let currentExperiment = null;

        // تهيئة المختبر
        document.addEventListener('DOMContentLoaded', function() {
            physicsExperiments = new InteractivePhysicsExperiments();
            loadExperimentsList();
        });

        // تحميل قائمة التجارب
        function loadExperimentsList() {
            const experimentsGrid = document.getElementById('experiments-grid');
            const experiments = [
                {
                    id: 'motion',
                    name: 'تحليل الحركة',
                    nameEn: 'Motion Analysis',
                    description: 'دراسة الحركة في بعد واحد وبعدين مع تأثير الجاذبية ومقاومة الهواء',
                    icon: 'fas fa-running',
                    difficulty: 'easy',
                    duration: '15-20 دقيقة'
                },
                {
                    id: 'forces',
                    name: 'القوى وقوانين نيوتن',
                    nameEn: 'Forces and Newton\'s Laws',
                    description: 'تطبيق القوى على الأجسام ودراسة تأثيرها على الحركة',
                    icon: 'fas fa-arrows-alt',
                    difficulty: 'medium',
                    duration: '20-25 دقيقة'
                },
                {
                    id: 'energy',
                    name: 'حفظ الطاقة',
                    nameEn: 'Energy Conservation',
                    description: 'دراسة تحولات الطاقة في البندول البسيط',
                    icon: 'fas fa-bolt',
                    difficulty: 'medium',
                    duration: '25-30 دقيقة'
                },
                {
                    id: 'momentum',
                    name: 'حفظ الزخم',
                    nameEn: 'Momentum Conservation',
                    description: 'دراسة التصادمات المرنة وغير المرنة وحفظ الزخم',
                    icon: 'fas fa-exchange-alt',
                    difficulty: 'medium',
                    duration: '20-25 دقيقة'
                },
                {
                    id: 'oscillation',
                    name: 'الحركة التوافقية البسيطة',
                    nameEn: 'Simple Harmonic Motion',
                    description: 'دراسة اهتزازات النابض والحركة التوافقية',
                    icon: 'fas fa-wave-square',
                    difficulty: 'medium',
                    duration: '25-30 دقيقة'
                },
                {
                    id: 'heat-transfer',
                    name: 'انتقال الحرارة',
                    nameEn: 'Heat Transfer',
                    description: 'دراسة انتقال الحرارة بين المواد المختلفة والتوصيل الحراري',
                    icon: 'fas fa-thermometer-half',
                    difficulty: 'medium',
                    duration: '20-25 دقيقة'
                },
                {
                    id: 'gas-laws',
                    name: 'قوانين الغازات',
                    nameEn: 'Gas Laws',
                    description: 'استكشاف قوانين بويل وشارل وجاي لوساك والغاز المثالي',
                    icon: 'fas fa-cloud',
                    difficulty: 'hard',
                    duration: '30-35 دقيقة'
                }
            ];

            experimentsGrid.innerHTML = experiments.map(exp => `
                <div class="experiment-card" onclick="startExperiment('${exp.id}')">
                    <div class="experiment-icon">
                        <i class="${exp.icon}"></i>
                    </div>
                    <h3 class="experiment-title">${exp.name}</h3>
                    <p class="experiment-subtitle">${exp.nameEn}</p>
                    <p class="experiment-description">${exp.description}</p>
                    <div class="experiment-meta">
                        <span class="difficulty ${exp.difficulty}">
                            ${exp.difficulty === 'easy' ? 'سهل' : exp.difficulty === 'medium' ? 'متوسط' : 'صعب'}
                        </span>
                        <span class="duration">
                            <i class="fas fa-clock"></i> ${exp.duration}
                        </span>
                    </div>
                    <div class="experiment-actions">
                        <button class="btn" onclick="event.stopPropagation(); startExperiment('${exp.id}')">
                            <i class="fas fa-play"></i>
                            <span data-translate="ui.start">بدء التجربة</span>
                        </button>
                        <button class="btn secondary" onclick="event.stopPropagation(); showExperimentInfo('${exp.id}')">
                            <i class="fas fa-info-circle"></i>
                            <span data-translate="ui.help">معلومات</span>
                        </button>
                    </div>
                </div>
            `).join('');
        }

        // بدء تجربة
        function startExperiment(experimentId) {
            const experimentsGrid = document.getElementById('experiments-grid');
            const experimentInterface = document.getElementById('experiment-interface');
            const titleElement = document.getElementById('current-experiment-title');

            // إخفاء شبكة التجارب وإظهار واجهة التجربة
            experimentsGrid.style.display = 'none';
            experimentInterface.classList.add('active');

            // تحديث العنوان
            const experimentNames = {
                'motion': 'تجربة تحليل الحركة - Motion Analysis',
                'forces': 'تجربة القوى وقوانين نيوتن - Forces and Newton\'s Laws',
                'energy': 'تجربة حفظ الطاقة - Energy Conservation',
                'momentum': 'تجربة حفظ الزخم - Momentum Conservation',
                'oscillation': 'تجربة الحركة التوافقية البسيطة - Simple Harmonic Motion',
                'heat-transfer': 'تجربة انتقال الحرارة - Heat Transfer',
                'gas-laws': 'تجربة قوانين الغازات - Gas Laws'
            };
            titleElement.textContent = experimentNames[experimentId] || 'تجربة فيزيائية';

            // بدء التجربة
            physicsExperiments.startExperiment(experimentId);
            currentExperiment = experimentId;
        }

        // إيقاف التجربة الحالية
        function stopCurrentExperiment() {
            const experimentsGrid = document.getElementById('experiments-grid');
            const experimentInterface = document.getElementById('experiment-interface');

            // إيقاف التجربة
            physicsExperiments.stopExperiment();

            // إظهار شبكة التجارب وإخفاء واجهة التجربة
            experimentsGrid.style.display = 'grid';
            experimentInterface.classList.remove('active');

            // مسح محتوى التجربة
            document.getElementById('experiment-canvas').innerHTML = '';
            document.getElementById('experiment-controls').innerHTML = '';
            document.getElementById('experiment-data').innerHTML = '';

            currentExperiment = null;
        }

        // عرض معلومات التجربة
        function showExperimentInfo(experimentId) {
            const info = {
                'motion': {
                    title: 'تجربة تحليل الحركة',
                    objectives: [
                        'فهم مفاهيم الموقع والسرعة والتسارع',
                        'دراسة تأثير الجاذبية على الحركة',
                        'تحليل الحركة المقذوفة',
                        'فهم تأثير مقاومة الهواء'
                    ],
                    theory: 'تعتمد هذه التجربة على قوانين الحركة الأساسية حيث يتم دراسة حركة جسم تحت تأثير الجاذبية ومقاومة الهواء.',
                    procedure: [
                        'اضبط السرعة الأولية الأفقية والرأسية',
                        'اختر قيمة الجاذبية المناسبة',
                        'ابدأ التجربة وراقب مسار الحركة',
                        'سجل البيانات وحلل النتائج'
                    ]
                }
            };

            const experimentInfo = info[experimentId];
            if (experimentInfo) {
                alert(`${experimentInfo.title}\n\nالأهداف:\n${experimentInfo.objectives.join('\n')}\n\nالنظرية:\n${experimentInfo.theory}`);
            }
        }
    </script>
</body>
</html>
