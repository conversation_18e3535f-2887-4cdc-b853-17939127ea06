// Advanced Learning Module JavaScript
class LearningModule {
    constructor() {
        this.currentSlide = 0;
        this.totalSlides = 0;
        this.slides = [];
        this.isAutoPlay = false;
        this.autoPlayInterval = null;
        this.progressData = {};
        this.userInteractions = [];
        this.startTime = Date.now();
        
        this.init();
    }

    init() {
        this.slides = document.querySelectorAll('.slide');
        this.totalSlides = this.slides.length;
        this.setupEventListeners();
        this.initializeSlideshow();
        this.trackProgress();
        this.enableAccessibility();
    }

    setupEventListeners() {
        // Keyboard navigation
        document.addEventListener('keydown', (e) => this.handleKeyNavigation(e));
        
        // Touch/swipe navigation for mobile
        this.setupTouchNavigation();
        
        // Window resize handling
        window.addEventListener('resize', () => this.handleResize());
        
        // Visibility change (tab switching)
        document.addEventListener('visibilitychange', () => this.handleVisibilityChange());
        
        // Before unload (save progress)
        window.addEventListener('beforeunload', () => this.saveProgress());
    }

    handleKeyNavigation(e) {
        switch(e.key) {
            case 'ArrowLeft':
            case 'ArrowUp':
                e.preventDefault();
                this.previousSlide();
                break;
            case 'ArrowRight':
            case 'ArrowDown':
            case ' ':
                e.preventDefault();
                this.nextSlide();
                break;
            case 'Home':
                e.preventDefault();
                this.goToSlide(0);
                break;
            case 'End':
                e.preventDefault();
                this.goToSlide(this.totalSlides - 1);
                break;
            case 'f':
            case 'F':
                if (e.ctrlKey || e.metaKey) {
                    e.preventDefault();
                    this.toggleFullscreen();
                }
                break;
            case 'p':
            case 'P':
                if (e.ctrlKey || e.metaKey) {
                    e.preventDefault();
                    this.toggleAutoPlay();
                }
                break;
        }
    }

    setupTouchNavigation() {
        let startX = 0;
        let startY = 0;
        let endX = 0;
        let endY = 0;

        document.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
        });

        document.addEventListener('touchend', (e) => {
            endX = e.changedTouches[0].clientX;
            endY = e.changedTouches[0].clientY;
            this.handleSwipe(startX, startY, endX, endY);
        });
    }

    handleSwipe(startX, startY, endX, endY) {
        const deltaX = endX - startX;
        const deltaY = endY - startY;
        const minSwipeDistance = 50;

        if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > minSwipeDistance) {
            if (deltaX > 0) {
                this.previousSlide(); // Swipe right (previous in RTL)
            } else {
                this.nextSlide(); // Swipe left (next in RTL)
            }
        }
    }

    initializeSlideshow() {
        this.showSlide(0);
        this.createSlideIndicators();
        this.loadProgress();
    }

    showSlide(n, direction = 'next') {
        if (n < 0 || n >= this.totalSlides) return;

        // Add exit animation to current slide
        if (this.currentSlide !== n) {
            this.slides[this.currentSlide].classList.add('slide-exit');
            setTimeout(() => {
                this.slides[this.currentSlide].classList.remove('active', 'slide-exit');
            }, 400);
        }

        // Update current slide
        this.currentSlide = n;

        // Add enter animation to new slide
        setTimeout(() => {
            this.slides[this.currentSlide].classList.add('active', 'slide-enter');
            setTimeout(() => {
                this.slides[this.currentSlide].classList.remove('slide-enter');
            }, 600);
        }, direction === 'prev' ? 0 : 200);

        this.updateUI();
        this.trackSlideView(n);
        this.announceSlideChange();
    }

    nextSlide() {
        if (this.currentSlide < this.totalSlides - 1) {
            this.showSlide(this.currentSlide + 1, 'next');
        } else {
            this.onModuleComplete();
        }
    }

    previousSlide() {
        if (this.currentSlide > 0) {
            this.showSlide(this.currentSlide - 1, 'prev');
        }
    }

    goToSlide(n) {
        if (n >= 0 && n < this.totalSlides) {
            const direction = n > this.currentSlide ? 'next' : 'prev';
            this.showSlide(n, direction);
        }
    }

    updateUI() {
        // Update progress bar
        const progress = ((this.currentSlide + 1) / this.totalSlides) * 100;
        const progressBar = document.getElementById('progressBar');
        if (progressBar) {
            progressBar.style.width = progress + '%';
        }

        // Update slide counter
        const slideCounter = document.getElementById('slideCounter');
        if (slideCounter) {
            slideCounter.textContent = `${this.currentSlide + 1} / ${this.totalSlides}`;
        }

        // Update navigation buttons
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');
        
        if (prevBtn) prevBtn.disabled = this.currentSlide === 0;
        if (nextBtn) {
            nextBtn.disabled = this.currentSlide === this.totalSlides - 1;
            nextBtn.textContent = this.currentSlide === this.totalSlides - 1 ? 'إنهاء' : 'التالي';
        }

        // Update slide indicators
        this.updateSlideIndicators();
    }

    createSlideIndicators() {
        const indicatorsContainer = document.createElement('div');
        indicatorsContainer.className = 'slide-indicators';
        indicatorsContainer.style.cssText = `
            position: fixed;
            top: 50%;
            left: 20px;
            transform: translateY(-50%);
            display: flex;
            flex-direction: column;
            gap: 10px;
            z-index: 999;
        `;

        for (let i = 0; i < this.totalSlides; i++) {
            const indicator = document.createElement('div');
            indicator.className = 'slide-indicator';
            indicator.style.cssText = `
                width: 12px;
                height: 12px;
                border-radius: 50%;
                background: rgba(255, 255, 255, 0.3);
                cursor: pointer;
                transition: all 0.3s ease;
                border: 2px solid transparent;
            `;
            
            indicator.addEventListener('click', () => this.goToSlide(i));
            indicator.title = `الشريحة ${i + 1}`;
            indicatorsContainer.appendChild(indicator);
        }

        document.body.appendChild(indicatorsContainer);
        this.indicatorsContainer = indicatorsContainer;
    }

    updateSlideIndicators() {
        if (!this.indicatorsContainer) return;

        const indicators = this.indicatorsContainer.querySelectorAll('.slide-indicator');
        indicators.forEach((indicator, index) => {
            if (index === this.currentSlide) {
                indicator.style.background = '#667eea';
                indicator.style.borderColor = 'white';
                indicator.style.transform = 'scale(1.2)';
            } else {
                indicator.style.background = 'rgba(255, 255, 255, 0.3)';
                indicator.style.borderColor = 'transparent';
                indicator.style.transform = 'scale(1)';
            }
        });
    }

    // Auto-play functionality
    toggleAutoPlay() {
        this.isAutoPlay = !this.isAutoPlay;
        
        if (this.isAutoPlay) {
            this.startAutoPlay();
            this.showNotification('تم تشغيل العرض التلقائي', 'info');
        } else {
            this.stopAutoPlay();
            this.showNotification('تم إيقاف العرض التلقائي', 'info');
        }
    }

    startAutoPlay(interval = 10000) {
        this.stopAutoPlay(); // Clear any existing interval
        this.autoPlayInterval = setInterval(() => {
            if (this.currentSlide < this.totalSlides - 1) {
                this.nextSlide();
            } else {
                this.stopAutoPlay();
            }
        }, interval);
    }

    stopAutoPlay() {
        if (this.autoPlayInterval) {
            clearInterval(this.autoPlayInterval);
            this.autoPlayInterval = null;
            this.isAutoPlay = false;
        }
    }

    // Progress tracking
    trackProgress() {
        this.progressData = {
            moduleId: 'basic-circuits',
            userId: this.getUserId(),
            startTime: this.startTime,
            lastAccessed: Date.now(),
            slidesViewed: [],
            timeSpentPerSlide: {},
            interactions: [],
            quizResponses: {},
            completed: false
        };
    }

    trackSlideView(slideIndex) {
        const now = Date.now();
        
        // Track time spent on previous slide
        if (this.lastSlideTime && this.lastSlideIndex !== undefined) {
            const timeSpent = now - this.lastSlideTime;
            this.progressData.timeSpentPerSlide[this.lastSlideIndex] = 
                (this.progressData.timeSpentPerSlide[this.lastSlideIndex] || 0) + timeSpent;
        }

        // Track current slide view
        if (!this.progressData.slidesViewed.includes(slideIndex)) {
            this.progressData.slidesViewed.push(slideIndex);
        }

        this.lastSlideTime = now;
        this.lastSlideIndex = slideIndex;
        this.progressData.lastAccessed = now;
    }

    trackInteraction(type, data) {
        this.progressData.interactions.push({
            type,
            data,
            timestamp: Date.now(),
            slideIndex: this.currentSlide
        });
    }

    saveProgress() {
        try {
            localStorage.setItem('learningModuleProgress', JSON.stringify(this.progressData));
        } catch (error) {
            console.warn('Could not save progress to localStorage:', error);
        }
    }

    loadProgress() {
        try {
            const saved = localStorage.getItem('learningModuleProgress');
            if (saved) {
                const data = JSON.parse(saved);
                if (data.moduleId === this.progressData.moduleId) {
                    this.progressData = { ...this.progressData, ...data };
                    
                    // Offer to resume from last position
                    if (data.slidesViewed.length > 1) {
                        const lastSlide = Math.max(...data.slidesViewed);
                        if (confirm(`هل تريد المتابعة من الشريحة ${lastSlide + 1}؟`)) {
                            this.goToSlide(lastSlide);
                        }
                    }
                }
            }
        } catch (error) {
            console.warn('Could not load progress from localStorage:', error);
        }
    }

    getUserId() {
        // In a real implementation, this would come from authentication
        let userId = localStorage.getItem('userId');
        if (!userId) {
            userId = 'user_' + Math.random().toString(36).substr(2, 9);
            localStorage.setItem('userId', userId);
        }
        return userId;
    }

    // Accessibility features
    enableAccessibility() {
        // Add ARIA attributes
        this.slides.forEach((slide, index) => {
            slide.setAttribute('role', 'region');
            slide.setAttribute('aria-label', `شريحة ${index + 1} من ${this.totalSlides}`);
            slide.setAttribute('tabindex', index === 0 ? '0' : '-1');
        });

        // Add screen reader announcements
        const announcer = document.createElement('div');
        announcer.setAttribute('aria-live', 'polite');
        announcer.setAttribute('aria-atomic', 'true');
        announcer.className = 'sr-only';
        announcer.id = 'slide-announcer';
        document.body.appendChild(announcer);
    }

    announceSlideChange() {
        const announcer = document.getElementById('slide-announcer');
        if (announcer) {
            const slideTitle = this.slides[this.currentSlide].querySelector('.slide-title')?.textContent || `الشريحة ${this.currentSlide + 1}`;
            announcer.textContent = `${slideTitle}. الشريحة ${this.currentSlide + 1} من ${this.totalSlides}`;
        }
    }

    // Interactive elements
    createInteractiveCircuit() {
        const container = document.querySelector('.diagram-container');
        if (!container) return;

        // Clear existing content
        container.innerHTML = '';

        // Create interactive circuit elements
        const circuit = this.createCircuitSVG();
        container.appendChild(circuit);

        // Add interaction handlers
        this.setupCircuitInteractions(circuit);
    }

    createCircuitSVG() {
        const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
        svg.setAttribute('width', '100%');
        svg.setAttribute('height', '100%');
        svg.setAttribute('viewBox', '0 0 400 200');
        svg.style.background = '#f8fafc';

        // Define circuit elements
        const elements = [
            { type: 'battery', x: 50, y: 100, label: 'بطارية' },
            { type: 'resistor', x: 200, y: 50, label: 'مقاومة' },
            { type: 'bulb', x: 350, y: 100, label: 'مصباح' },
            { type: 'switch', x: 200, y: 150, label: 'مفتاح' }
        ];

        // Create circuit paths
        const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
        path.setAttribute('d', 'M 70 100 L 180 100 L 180 50 L 220 50 L 220 100 L 330 100 L 330 150 L 220 150 L 220 100 L 180 100 L 180 150 L 70 150 Z');
        path.setAttribute('stroke', '#667eea');
        path.setAttribute('stroke-width', '3');
        path.setAttribute('fill', 'none');
        path.classList.add('circuit-path');
        svg.appendChild(path);

        // Create interactive elements
        elements.forEach(element => {
            const group = this.createCircuitElement(element);
            svg.appendChild(group);
        });

        return svg;
    }

    createCircuitElement(element) {
        const group = document.createElementNS('http://www.w3.org/2000/svg', 'g');
        group.setAttribute('transform', `translate(${element.x}, ${element.y})`);
        group.setAttribute('data-type', element.type);
        group.setAttribute('data-label', element.label);
        group.style.cursor = 'pointer';

        // Create element shape based on type
        let shape;
        switch (element.type) {
            case 'battery':
                shape = this.createBatteryShape();
                break;
            case 'resistor':
                shape = this.createResistorShape();
                break;
            case 'bulb':
                shape = this.createBulbShape();
                break;
            case 'switch':
                shape = this.createSwitchShape();
                break;
        }

        if (shape) {
            group.appendChild(shape);
        }

        // Add interaction handlers
        group.addEventListener('click', () => this.handleElementClick(element));
        group.addEventListener('mouseenter', () => this.showElementTooltip(element, group));
        group.addEventListener('mouseleave', () => this.hideElementTooltip());

        return group;
    }

    createBatteryShape() {
        const g = document.createElementNS('http://www.w3.org/2000/svg', 'g');
        
        // Positive terminal
        const posLine = document.createElementNS('http://www.w3.org/2000/svg', 'line');
        posLine.setAttribute('x1', '0');
        posLine.setAttribute('y1', '-15');
        posLine.setAttribute('x2', '0');
        posLine.setAttribute('y2', '15');
        posLine.setAttribute('stroke', '#667eea');
        posLine.setAttribute('stroke-width', '4');
        g.appendChild(posLine);

        // Negative terminal
        const negLine = document.createElementNS('http://www.w3.org/2000/svg', 'line');
        negLine.setAttribute('x1', '-10');
        negLine.setAttribute('y1', '-10');
        negLine.setAttribute('x2', '-10');
        negLine.setAttribute('y2', '10');
        negLine.setAttribute('stroke', '#667eea');
        negLine.setAttribute('stroke-width', '2');
        g.appendChild(negLine);

        return g;
    }

    createResistorShape() {
        const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
        path.setAttribute('d', 'M -20 0 L -15 -8 L -5 8 L 5 -8 L 15 8 L 20 0');
        path.setAttribute('stroke', '#667eea');
        path.setAttribute('stroke-width', '3');
        path.setAttribute('fill', 'none');
        return path;
    }

    createBulbShape() {
        const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        circle.setAttribute('r', '15');
        circle.setAttribute('stroke', '#667eea');
        circle.setAttribute('stroke-width', '3');
        circle.setAttribute('fill', 'rgba(102, 126, 234, 0.1)');
        
        // Add filament
        const line1 = document.createElementNS('http://www.w3.org/2000/svg', 'line');
        line1.setAttribute('x1', '-8');
        line1.setAttribute('y1', '-8');
        line1.setAttribute('x2', '8');
        line1.setAttribute('y2', '8');
        line1.setAttribute('stroke', '#667eea');
        line1.setAttribute('stroke-width', '1');
        
        const line2 = document.createElementNS('http://www.w3.org/2000/svg', 'line');
        line2.setAttribute('x1', '8');
        line2.setAttribute('y1', '-8');
        line2.setAttribute('x2', '-8');
        line2.setAttribute('y2', '8');
        line2.setAttribute('stroke', '#667eea');
        line2.setAttribute('stroke-width', '1');

        const g = document.createElementNS('http://www.w3.org/2000/svg', 'g');
        g.appendChild(circle);
        g.appendChild(line1);
        g.appendChild(line2);
        return g;
    }

    createSwitchShape() {
        const g = document.createElementNS('http://www.w3.org/2000/svg', 'g');
        
        // Switch line
        const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
        line.setAttribute('x1', '-15');
        line.setAttribute('y1', '0');
        line.setAttribute('x2', '10');
        line.setAttribute('y2', '-8');
        line.setAttribute('stroke', '#667eea');
        line.setAttribute('stroke-width', '3');
        g.appendChild(line);

        // Contact points
        const circle1 = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        circle1.setAttribute('cx', '-15');
        circle1.setAttribute('cy', '0');
        circle1.setAttribute('r', '2');
        circle1.setAttribute('fill', '#667eea');
        g.appendChild(circle1);

        const circle2 = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        circle2.setAttribute('cx', '15');
        circle2.setAttribute('cy', '0');
        circle2.setAttribute('r', '2');
        circle2.setAttribute('fill', '#667eea');
        g.appendChild(circle2);

        return g;
    }

    handleElementClick(element) {
        this.trackInteraction('element_click', element);
        this.showElementInfo(element.label, this.getElementDescription(element.type));
        
        // Add visual feedback
        const elementGroup = document.querySelector(`[data-type="${element.type}"]`);
        if (elementGroup) {
            elementGroup.style.filter = 'drop-shadow(0 0 10px #667eea)';
            setTimeout(() => {
                elementGroup.style.filter = '';
            }, 1000);
        }
    }

    getElementDescription(type) {
        const descriptions = {
            'battery': 'مصدر الطاقة الكهربائية - توفر الجهد اللازم لتشغيل الدائرة بقوة دفع ثابتة',
            'resistor': 'عنصر يحد من تدفق التيار - يحمي المكونات الأخرى ويتحكم في قيمة التيار',
            'bulb': 'حمل كهربائي - يحول الطاقة الكهربائية إلى ضوء وحرارة مفيدة',
            'switch': 'جهاز تحكم - يفتح أو يغلق الدائرة للتحكم في تدفق التيار'
        };
        return descriptions[type] || 'عنصر كهربائي';
    }

    // Quiz functionality
    initializeQuiz() {
        const quizSections = document.querySelectorAll('.quiz-section');
        quizSections.forEach(section => this.setupQuizSection(section));
    }

    setupQuizSection(section) {
        const options = section.querySelectorAll('.quiz-option');
        options.forEach(option => {
            option.addEventListener('click', (e) => this.handleQuizAnswer(e, section));
        });
    }

    handleQuizAnswer(event, section) {
        const selectedOption = event.currentTarget;
        const isCorrect = selectedOption.dataset.correct === 'true';
        const questionId = section.dataset.questionId || 'q1';

        // Track quiz response
        this.progressData.quizResponses[questionId] = {
            selected: selectedOption.textContent,
            correct: isCorrect,
            timestamp: Date.now()
        };

        this.trackInteraction('quiz_answer', {
            questionId,
            selected: selectedOption.textContent,
            correct: isCorrect
        });

        // Visual feedback
        this.showQuizFeedback(selectedOption, isCorrect, section);
    }

    showQuizFeedback(selectedOption, isCorrect, section) {
        const options = section.querySelectorAll('.quiz-option');
        
        // Disable all options
        options.forEach(option => {
            option.style.pointerEvents = 'none';
            option.classList.remove('selected', 'correct', 'incorrect');
        });

        // Mark selected option
        selectedOption.classList.add('selected');

        setTimeout(() => {
            if (isCorrect) {
                selectedOption.classList.add('correct');
                this.showNotification('إجابة صحيحة! أحسنت 🎉', 'success');
            } else {
                selectedOption.classList.add('incorrect');
                // Show correct answer
                const correctOption = Array.from(options).find(opt => opt.dataset.correct === 'true');
                if (correctOption) {
                    correctOption.classList.add('correct');
                }
                this.showNotification('إجابة خاطئة. راجع الإجابة الصحيحة المميزة', 'warning');
            }
        }, 300);
    }

    // Utility functions
    toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen().catch(err => {
                console.warn('Could not enter fullscreen:', err);
            });
        } else {
            document.exitFullscreen();
        }
    }

    handleResize() {
        // Adjust layout for different screen sizes
        const isSmallScreen = window.innerWidth < 768;
        if (isSmallScreen) {
            this.enableMobileOptimizations();
        } else {
            this.disableMobileOptimizations();
        }
    }

    enableMobileOptimizations() {
        // Hide slide indicators on mobile
        if (this.indicatorsContainer) {
            this.indicatorsContainer.style.display = 'none';
        }
        
        // Adjust slide content for mobile
        this.slides.forEach(slide => {
            slide.style.padding = '15px';
        });
    }

    disableMobileOptimizations() {
        // Show slide indicators on desktop
        if (this.indicatorsContainer) {
            this.indicatorsContainer.style.display = 'flex';
        }
        
        // Reset slide content padding
        this.slides.forEach(slide => {
            slide.style.padding = '';
        });
    }

    handleVisibilityChange() {
        if (document.hidden) {
            // Pause auto-play when tab is not visible
            if (this.isAutoPlay) {
                this.wasAutoPlaying = true;
                this.stopAutoPlay();
            }
        } else {
            // Resume auto-play when tab becomes visible again
            if (this.wasAutoPlaying) {
                this.startAutoPlay();
                this.wasAutoPlaying = false;
            }
        }
    }

    showNotification(message, type = 'info') {
        // Create notification element if it doesn't exist
        let notification = document.getElementById('notification');
        if (!notification) {
            notification = document.createElement('div');
            notification.id = 'notification';
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: white;
                color: #2d3748;
                padding: 15px 20px;
                border-radius: 10px;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
                z-index: 1000;
                transform: translateX(100%);
                transition: transform 0.3s ease;
                max-width: 300px;
                border-left: 4px solid #667eea;
            `;
            document.body.appendChild(notification);
        }

        // Set message and style based on type
        notification.textContent = message;
        const colors = {
            success: '#48bb78',
            warning: '#ed8936',
            error: '#f56565',
            info: '#667eea'
        };
        notification.style.borderLeftColor = colors[type] || colors.info;

        // Show notification
        notification.style.transform = 'translateX(0)';

        // Hide after 3 seconds
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
        }, 3000);
    }

    onModuleComplete() {
        this.progressData.completed = true;
        this.progressData.completionTime = Date.now();
        this.saveProgress();

        // Show completion modal
        this.showCompletionModal();
    }

    showCompletionModal() {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay active';
        modal.innerHTML = `
            <div class="modal-content" style="text-align: center;">
                <h2 style="color: #667eea; margin-bottom: 20px;">🎉 تهانينا!</h2>
                <p style="font-size: 1.1rem; margin-bottom: 20px;">لقد أكملت وحدة الدوائر الكهربائية الأساسية بنجاح</p>
                <div style="background: #f0fff4; padding: 20px; border-radius: 10px; margin: 20px 0;">
                    <h3 style="color: #2f855a; margin-bottom: 15px;">إحصائيات التعلم</h3>
                    <p>الوقت المستغرق: ${this.getFormattedDuration()}</p>
                    <p>الشرائح المشاهدة: ${this.progressData.slidesViewed.length}/${this.totalSlides}</p>
                    <p>التفاعلات: ${this.progressData.interactions.length}</p>
                </div>
                <div style="display: flex; gap: 15px; justify-content: center; margin-top: 20px;">
                    <button onclick="location.reload()" class="btn btn-enhanced">
                        إعادة المراجعة
                    </button>
                    <button onclick="this.parentElement.parentElement.remove()" class="btn btn-enhanced">
                        إنهاء
                    </button>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
    }

    getFormattedDuration() {
        const duration = this.progressData.completionTime - this.progressData.startTime;
        const minutes = Math.floor(duration / 60000);
        const seconds = Math.floor((duration % 60000) / 1000);
        return `${minutes} دقيقة و ${seconds} ثانية`;
    }

    // Animation helpers
    animateElement(element, animation, duration = 1000) {
        element.style.animation = `${animation} ${duration}ms ease-in-out`;
        setTimeout(() => {
            element.style.animation = '';
        }, duration);
    }

    // Export functionality
    generateProgressReport() {
        const report = {
            moduleTitle: 'الدوائر الكهربائية الأساسية',
            studentId: this.progressData.userId,
            completionStatus: this.progressData.completed ? 'مكتمل' : 'غير مكتمل',
            progressPercentage: Math.round((this.progressData.slidesViewed.length / this.totalSlides) * 100),
            timeSpent: this.getFormattedDuration(),
            quizScore: this.calculateQuizScore(),
            interactions: this.progressData.interactions.length,
            timestamp: new Date().toLocaleDateString('ar-SA')
        };

        return report;
    }

    calculateQuizScore() {
        const responses = Object.values(this.progressData.quizResponses);
        if (responses.length === 0) return 'لم يتم الإجابة';
        
        const correct = responses.filter(r => r.correct).length;
        return `${correct}/${responses.length} (${Math.round((correct / responses.length) * 100)}%)`;
    }

    exportProgress() {
        const report = this.generateProgressReport();
        const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `learning-progress-${Date.now()}.json`;
        a.click();
        
        URL.revokeObjectURL(url);
    }
}

// Initialize the learning module when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.learningModule = new LearningModule();
    
    // Global functions for HTML onclick handlers
    window.changeSlide = (direction) => window.learningModule.changeSlide(direction);
    window.showConceptDetails = (concept) => window.learningModule.showConceptDetails(concept);
    window.showElementInfo = (element, description) => window.learningModule.showElementInfo(element, description);
    window.selectAnswer = (option, isCorrect) => window.learningModule.selectAnswer(option, isCorrect);
    window.startSimulation = () => window.learningModule.startSimulation();
    window.resetSimulation = () => window.learningModule.resetSimulation();
    window.saveCircuit = () => window.learningModule.saveCircuit();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LearningModule;
}