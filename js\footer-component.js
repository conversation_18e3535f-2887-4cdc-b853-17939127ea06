// Footer Component for SimLab HUB
// Consistent footer across all pages with author information and contact details

class FooterComponent {
    constructor() {
        this.footerData = {
            author: {
                name: 'Dr<PERSON> <PERSON>smail',
                institution: 'SUST - BME',
                year: '2025'
            },
            contact: {
                email: '<EMAIL>',
                phones: ['+249912867327', '+966538076790']
            },
            links: [
                { key: 'privacy', href: '#privacy', text: 'Privacy Policy' },
                { key: 'terms', href: '#terms', text: 'Terms of Use' },
                { key: 'contact', href: '#contact', text: 'Contact Information' }
            ]
        };
        
        this.init();
    }

    init() {
        this.createFooter();
        this.addEventListeners();
    }

    createFooter() {
        // Remove existing footer if present
        const existingFooter = document.getElementById('simlab-footer');
        if (existingFooter) {
            existingFooter.remove();
        }

        const footer = document.createElement('footer');
        footer.id = 'simlab-footer';
        footer.className = 'simlab-footer';
        
        footer.innerHTML = this.getFooterHTML();
        
        // Add to body
        document.body.appendChild(footer);
    }

    getFooterHTML() {
        return `
            <div class="footer-content">
                <div class="footer-section author-section">
                    <div class="author-info">
                        <div class="author-name" data-translate="footer.author">
                            Author: ${this.footerData.author.name}, ${this.footerData.author.institution}, © ${this.footerData.author.year}
                        </div>
                        <div class="copyright-info">
                            <span data-translate="footer.copyright">Copyright</span> 
                            <a href="mailto:${this.footerData.contact.email}" class="email-link">
                                ${this.footerData.contact.email}
                            </a>
                        </div>
                        <div class="phone-info">
                            <span data-translate="footer.phone">Phone: ${this.footerData.contact.phones.join(', ')}</span>
                        </div>
                    </div>
                </div>
                
                <div class="footer-section links-section">
                    <div class="footer-links">
                        ${this.footerData.links.map(link => `
                            <a href="${link.href}" class="footer-link" data-translate="footer.${link.key}">
                                ${link.text}
                            </a>
                        `).join('')}
                    </div>
                </div>
                
                <div class="footer-section social-section">
                    <div class="social-links">
                        <a href="mailto:${this.footerData.contact.email}" class="social-link" title="Email">
                            <i class="fas fa-envelope"></i>
                        </a>
                        <a href="tel:${this.footerData.contact.phones[0]}" class="social-link" title="Phone (Sudan)">
                            <i class="fas fa-phone"></i>
                        </a>
                        <a href="tel:${this.footerData.contact.phones[1]}" class="social-link" title="Phone (Saudi Arabia)">
                            <i class="fas fa-mobile-alt"></i>
                        </a>
                    </div>
                    
                    <div class="footer-logo">
                        <i class="fas fa-flask"></i>
                        <span>SimLab HUB</span>
                    </div>
                </div>
            </div>
            
            <div class="footer-bottom">
                <div class="footer-bottom-content">
                    <div class="powered-by">
                        Powered by Advanced Virtual Laboratory Technology
                    </div>
                    <div class="last-updated">
                        Last Updated: ${new Date().toLocaleDateString()}
                    </div>
                </div>
            </div>
        `;
    }

    addEventListeners() {
        // Handle footer link clicks
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('footer-link')) {
                this.handleFooterLinkClick(e);
            }
        });

        // Handle social link clicks
        document.addEventListener('click', (e) => {
            if (e.target.closest('.social-link')) {
                this.handleSocialLinkClick(e);
            }
        });
    }

    handleFooterLinkClick(e) {
        const href = e.target.getAttribute('href');
        
        if (href.startsWith('#')) {
            e.preventDefault();
            this.showModal(href.substring(1));
        }
    }

    handleSocialLinkClick(e) {
        const link = e.target.closest('.social-link');
        const href = link.getAttribute('href');
        
        if (href.startsWith('mailto:') || href.startsWith('tel:')) {
            // Let default behavior handle email and phone links
            return;
        }
    }

    showModal(type) {
        const modal = document.createElement('div');
        modal.className = 'footer-modal';
        modal.innerHTML = this.getModalContent(type);
        
        document.body.appendChild(modal);
        
        // Add close event listener
        modal.addEventListener('click', (e) => {
            if (e.target === modal || e.target.classList.contains('modal-close')) {
                modal.remove();
            }
        });
    }

    getModalContent(type) {
        const content = {
            privacy: {
                title: 'Privacy Policy',
                content: `
                    <h3>Privacy Policy - SimLab HUB</h3>
                    <p><strong>Effective Date:</strong> January 1, 2025</p>
                    
                    <h4>Information We Collect</h4>
                    <p>SimLab HUB is designed to protect your privacy. We collect minimal information necessary for the educational experience:</p>
                    <ul>
                        <li>Usage data for improving the learning experience</li>
                        <li>Experiment results and progress (stored locally)</li>
                        <li>Language preferences</li>
                    </ul>
                    
                    <h4>How We Use Information</h4>
                    <p>Information is used solely for:</p>
                    <ul>
                        <li>Providing educational content and laboratory simulations</li>
                        <li>Improving user experience and interface</li>
                        <li>Technical support and troubleshooting</li>
                    </ul>
                    
                    <h4>Data Storage</h4>
                    <p>Most data is stored locally on your device. No personal information is transmitted without your explicit consent.</p>
                    
                    <h4>Contact</h4>
                    <p>For privacy concerns, contact: ${this.footerData.contact.email}</p>
                `
            },
            terms: {
                title: 'Terms of Use',
                content: `
                    <h3>Terms of Use - SimLab HUB</h3>
                    <p><strong>Effective Date:</strong> January 1, 2025</p>
                    
                    <h4>Acceptance of Terms</h4>
                    <p>By using SimLab HUB, you agree to these terms and conditions.</p>
                    
                    <h4>Educational Use</h4>
                    <p>SimLab HUB is designed for educational purposes. Users are expected to:</p>
                    <ul>
                        <li>Use the platform responsibly and ethically</li>
                        <li>Respect intellectual property rights</li>
                        <li>Not attempt to reverse engineer or modify the software</li>
                    </ul>
                    
                    <h4>Limitation of Liability</h4>
                    <p>SimLab HUB is provided "as is" for educational purposes. While we strive for accuracy, virtual experiments should not replace proper laboratory safety training.</p>
                    
                    <h4>Intellectual Property</h4>
                    <p>All content, including experiments, simulations, and educational materials, is the intellectual property of Dr. Mohammed Yagoub Esmail and SUST.</p>
                    
                    <h4>Contact</h4>
                    <p>For questions about these terms, contact: ${this.footerData.contact.email}</p>
                `
            },
            contact: {
                title: 'Contact Information',
                content: `
                    <h3>Contact Information</h3>
                    
                    <div class="contact-card">
                        <h4>Author & Developer</h4>
                        <p><strong>${this.footerData.author.name}</strong></p>
                        <p>${this.footerData.author.institution}</p>
                        <p>Biomedical Engineering Department</p>
                        <p>Sudan University of Science and Technology</p>
                    </div>
                    
                    <div class="contact-card">
                        <h4>Email</h4>
                        <p><a href="mailto:${this.footerData.contact.email}">${this.footerData.contact.email}</a></p>
                    </div>
                    
                    <div class="contact-card">
                        <h4>Phone Numbers</h4>
                        <p><strong>Sudan:</strong> <a href="tel:${this.footerData.contact.phones[0]}">${this.footerData.contact.phones[0]}</a></p>
                        <p><strong>Saudi Arabia:</strong> <a href="tel:${this.footerData.contact.phones[1]}">${this.footerData.contact.phones[1]}</a></p>
                    </div>
                    
                    <div class="contact-card">
                        <h4>Office Hours</h4>
                        <p>Sunday - Thursday: 8:00 AM - 4:00 PM (GMT+3)</p>
                        <p>Response time: Within 24-48 hours</p>
                    </div>
                    
                    <div class="contact-card">
                        <h4>Technical Support</h4>
                        <p>For technical issues or questions about SimLab HUB, please include:</p>
                        <ul>
                            <li>Browser type and version</li>
                            <li>Operating system</li>
                            <li>Description of the issue</li>
                            <li>Steps to reproduce the problem</li>
                        </ul>
                    </div>
                `
            }
        };

        return `
            <div class="modal-overlay">
                <div class="modal-content">
                    <div class="modal-header">
                        <h2>${content[type].title}</h2>
                        <button class="modal-close">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        ${content[type].content}
                    </div>
                    <div class="modal-footer">
                        <button class="modal-close footer-btn">Close</button>
                    </div>
                </div>
            </div>
        `;
    }

    updateFooter() {
        // Update footer content if needed
        const footer = document.getElementById('simlab-footer');
        if (footer) {
            footer.innerHTML = this.getFooterHTML();
        }
    }

    // Method to update contact information
    updateContactInfo(newContact) {
        Object.assign(this.footerData.contact, newContact);
        this.updateFooter();
    }

    // Method to update author information
    updateAuthorInfo(newAuthor) {
        Object.assign(this.footerData.author, newAuthor);
        this.updateFooter();
    }

    // Method to add custom links
    addCustomLink(link) {
        this.footerData.links.push(link);
        this.updateFooter();
    }

    // Method to remove the footer
    remove() {
        const footer = document.getElementById('simlab-footer');
        if (footer) {
            footer.remove();
        }
    }

    // Method to hide/show footer
    toggle(visible = null) {
        const footer = document.getElementById('simlab-footer');
        if (footer) {
            if (visible === null) {
                footer.style.display = footer.style.display === 'none' ? 'block' : 'none';
            } else {
                footer.style.display = visible ? 'block' : 'none';
            }
        }
    }
}

// Global footer instance
let footerComponent;

document.addEventListener('DOMContentLoaded', function() {
    // Initialize footer component
    footerComponent = new FooterComponent();
    
    // Update translations if language system is available
    if (typeof languageSystem !== 'undefined') {
        setTimeout(() => {
            languageSystem.translateDynamicContent('simlab-footer');
        }, 200);
    }
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FooterComponent;
}
