<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Interactive Lectures - SimLab HUB</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
    <script src="js/lecture-advanced.js"></script>
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #64748b;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: var(--bg-gradient);
            color: #333;
            overflow-x: hidden;
        }

        .lecture-container {
            display: grid;
            grid-template-areas: 
                "header header header header"
                "video video sidebar controls"
                "whiteboard whiteboard chat controls";
            grid-template-columns: 1fr 1fr 300px 280px;
            grid-template-rows: 70px 1fr 1fr;
            height: 100vh;
            gap: 12px;
            padding: 12px;
        }

        .header {
            grid-area: header;
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 0 25px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header h1 {
            color: var(--primary-color);
            font-size: 1.6rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .header-info {
            display: flex;
            align-items: center;
            gap: 20px;
            font-size: 0.9rem;
            color: #64748b;
        }

        .live-indicator {
            background: #ef4444;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .header-controls {
            display: flex;
            gap: 12px;
        }

        .btn {
            padding: 10px 18px;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), #1d4ed8);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(37, 99, 235, 0.3);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #64748b, #475569);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, var(--success-color), #059669);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, var(--warning-color), #d97706);
            color: white;
        }

        .video-section {
            grid-area: video;
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .video-container {
            position: relative;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #1e293b, #334155);
            border-radius: 15px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .video-placeholder {
            color: #e2e8f0;
            font-size: 1.3rem;
            text-align: center;
            z-index: 2;
        }

        .video-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .video-container:hover .video-overlay {
            opacity: 1;
        }

        .video-controls {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 15px;
            background: rgba(0, 0, 0, 0.8);
            padding: 15px 20px;
            border-radius: 30px;
            backdrop-filter: blur(10px);
        }

        .video-controls button {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            width: 45px;
            height: 45px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .video-controls button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .video-controls button.active {
            background: var(--primary-color);
        }

        .participant-videos {
            position: absolute;
            top: 15px;
            right: 15px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            max-width: 200px;
        }

        .participant-video {
            width: 60px;
            height: 45px;
            border-radius: 8px;
            background: #334155;
            border: 2px solid rgba(255, 255, 255, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .whiteboard-section {
            grid-area: whiteboard;
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .whiteboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .whiteboard-header h3 {
            color: var(--primary-color);
            font-size: 1.2rem;
            font-weight: 700;
        }

        .whiteboard-tools {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .tool-btn {
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
        }

        .tool-btn.active {
            background: var(--primary-color);
            color: white;
            transform: scale(1.1);
        }

        .tool-btn:not(.active) {
            background: #f1f5f9;
            color: #64748b;
        }

        .tool-btn:hover {
            transform: scale(1.05);
        }

        .whiteboard-canvas {
            width: 100%;
            height: calc(100% - 80px);
            border: 2px solid #e2e8f0;
            border-radius: 15px;
            background: white;
            cursor: crosshair;
            box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .sidebar {
            grid-area: sidebar;
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .sidebar-section {
            background: linear-gradient(135deg, #f8fafc, #f1f5f9);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(226, 232, 240, 0.5);
        }

        .sidebar-section h3 {
            color: var(--primary-color);
            margin-bottom: 15px;
            font-size: 1.1rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .poll-container {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .poll-option {
            background: white;
            padding: 15px;
            border-radius: 12px;
            border: 2px solid #e2e8f0;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .poll-option::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            background: var(--primary-color);
            width: 0;
            transition: width 0.3s ease;
            opacity: 0.1;
        }

        .poll-option:hover {
            border-color: var(--primary-color);
            background: #eff6ff;
            transform: translateX(5px);
        }

        .poll-option.selected {
            border-color: var(--primary-color);
            background: var(--primary-color);
            color: white;
        }

        .poll-option.selected::before {
            width: 100%;
        }

        .poll-results {
            font-size: 0.9rem;
            font-weight: 600;
        }

        .poll-progress {
            height: 4px;
            background: #e2e8f0;
            border-radius: 2px;
            margin-top: 8px;
            overflow: hidden;
        }

        .poll-progress-bar {
            height: 100%;
            background: var(--primary-color);
            transition: width 0.5s ease;
        }

        .qa-section {
            max-height: 250px;
            overflow-y: auto;
            padding-right: 5px;
        }

        .qa-section::-webkit-scrollbar {
            width: 6px;
        }

        .qa-section::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }

        .qa-section::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }

        .question {
            background: white;
            padding: 15px;
            border-radius: 12px;
            margin-bottom: 12px;
            border-left: 4px solid var(--primary-color);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .question:hover {
            transform: translateX(5px);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }

        .question-author {
            font-weight: 700;
            color: var(--primary-color);
            font-size: 0.9rem;
            margin-bottom: 5px;
        }

        .question-text {
            color: #475569;
            line-height: 1.5;
        }

        .question-time {
            font-size: 0.8rem;
            color: #94a3b8;
            margin-top: 5px;
        }

        .question-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            margin-top: 15px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .question-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .chat-section {
            grid-area: chat;
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            flex-direction: column;
        }

        .chat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e2e8f0;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding-right: 10px;
            margin-bottom: 15px;
        }

        .chat-message {
            background: #f8fafc;
            padding: 12px;
            border-radius: 12px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }

        .chat-message:hover {
            background: #f1f5f9;
        }

        .chat-message.own {
            background: var(--primary-color);
            color: white;
            margin-left: 20px;
        }

        .chat-input-container {
            display: flex;
            gap: 10px;
        }

        .chat-input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 14px;
        }

        .chat-send {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .controls-panel {
            grid-area: controls;
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .control-group {
            background: linear-gradient(135deg, #f8fafc, #f1f5f9);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(226, 232, 240, 0.5);
        }

        .control-group h4 {
            color: var(--primary-color);
            margin-bottom: 15px;
            font-size: 1rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .participants-list {
            max-height: 180px;
            overflow-y: auto;
        }

        .participant {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px 0;
            border-bottom: 1px solid rgba(226, 232, 240, 0.5);
            transition: all 0.3s ease;
        }

        .participant:last-child {
            border-bottom: none;
        }

        .participant:hover {
            background: rgba(37, 99, 235, 0.05);
            border-radius: 8px;
            padding-left: 8px;
        }

        .participant-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), #1d4ed8);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.9rem;
            font-weight: 700;
        }

        .participant-info {
            flex: 1;
        }

        .participant-name {
            font-weight: 600;
            color: #1e293b;
            font-size: 0.9rem;
        }

        .participant-role {
            font-size: 0.8rem;
            color: #64748b;
        }

        .participant-status {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: var(--success-color);
            position: relative;
        }

        .participant-status.speaking {
            animation: pulse 1s infinite;
        }

        .notification {
            position: fixed;
            top: 90px;
            right: 30px;
            background: white;
            color: #1e293b;
            padding: 20px 25px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
            transform: translateX(120%);
            transition: transform 0.3s ease;
            z-index: 1000;
            max-width: 300px;
            border-left: 4px solid var(--primary-color);
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            border-left-color: var(--success-color);
        }

        .notification.error {
            border-left-color: var(--error-color);
        }

        .notification.warning {
            border-left-color: var(--warning-color);
        }

        .floating-actions {
            position: fixed;
            bottom: 30px;
            right: 30px;
            display: flex;
            flex-direction: column;
            gap: 15px;
            z-index: 999;
        }

        .fab {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            border: none;
            background: linear-gradient(135deg, var(--primary-color), #1d4ed8);
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            box-shadow: 0 8px 24px rgba(37, 99, 235, 0.3);
            transition: all 0.3s ease;
        }

        .fab:hover {
            transform: scale(1.1);
            box-shadow: 0 12px 32px rgba(37, 99, 235, 0.4);
        }

        .fab.recording {
            background: linear-gradient(135deg, var(--error-color), #dc2626);
            animation: pulse 2s infinite;
        }

        /* Responsive Design */
        @media (max-width: 1400px) {
            .lecture-container {
                grid-template-columns: 1fr 280px 260px;
            }
        }

        @media (max-width: 1200px) {
            .lecture-container {
                grid-template-areas: 
                    "header header header"
                    "video video sidebar"
                    "whiteboard chat controls";
                grid-template-columns: 1fr 1fr 280px;
            }
        }

        @media (max-width: 1024px) {
            .lecture-container {
                grid-template-areas: 
                    "header"
                    "video"
                    "sidebar"
                    "whiteboard"
                    "chat"
                    "controls";
                grid-template-columns: 1fr;
                grid-template-rows: 70px repeat(5, auto);
            }
        }

        @media (max-width: 768px) {
            .lecture-container {
                padding: 8px;
                gap: 8px;
            }
            
            .header, .video-section, .whiteboard-section, 
            .sidebar, .chat-section, .controls-panel {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 1.3rem;
            }
            
            .floating-actions {
                bottom: 20px;
                right: 20px;
            }
        }

        /* Dark mode support */
        @media (prefers-color-scheme: dark) {
            :root {
                --bg-gradient: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            }
            
            .header, .video-section, .whiteboard-section, 
            .sidebar, .chat-section, .controls-panel {
                background: rgba(30, 41, 59, 0.9);
                color: #e2e8f0;
            }
            
            .sidebar-section, .control-group {
                background: linear-gradient(135deg, #334155, #475569);
            }
        }
    </style>
</head>
<body>
    <div class="lecture-container">
        <!-- Header -->
        <div class="header">
            <h1>
                <i class="fas fa-graduation-cap"></i>
                Advanced Interactive Lectures
            </h1>
            <div class="header-info">
                <div class="live-indicator">
                    <i class="fas fa-circle"></i> LIVE
                </div>
                <span><i class="fas fa-clock"></i> 45:23</span>
                <span><i class="fas fa-users"></i> 127 participants</span>
            </div>
            <div class="header-controls">
                <button class="btn btn-secondary" onclick="createBreakoutRooms()">
                    <i class="fas fa-users-cog"></i> Breakout Rooms
                </button>
                <button class="btn btn-warning" onclick="createAdvancedPoll()">
                    <i class="fas fa-poll"></i> Advanced Poll
                </button>
                <button class="btn btn-success" onclick="toggleRecording()">
                    <i class="fas fa-record-vinyl"></i> Record
                </button>
                <button class="btn btn-primary" onclick="toggleLecture()">
                    <i class="fas fa-play"></i> Start Lecture
                </button>
            </div>
        </div>

        <!-- Video Section with Enhanced Features -->
        <div class="video-section">
            <div class="video-container" id="videoContainer">
                <div class="video-placeholder">
                    <i class="fas fa-video" style="font-size: 3.5rem; margin-bottom: 15px;"></i>
                    <p style="font-size: 1.1rem; margin-bottom: 15px;">Advanced Video Conferencing</p>
                    <p style="font-size: 0.9rem; opacity: 0.8; margin-bottom: 20px;">
                        Features: HD Video, Virtual Backgrounds, Screen Annotation, Multi-presenter Mode
                    </p>
                    <button class="btn btn-primary" onclick="startAdvancedVideo()">
                        <i class="fas fa-play"></i> Start Advanced Video
                    </button>
                </div>
                <div class="video-overlay">
                    <div class="video-controls">
                        <button onclick="toggleMute()" id="muteBtn" title="Toggle Microphone">
                            <i class="fas fa-microphone"></i>
                        </button>
                        <button onclick="toggleVideo()" id="videoBtn" title="Toggle Video">
                            <i class="fas fa-video"></i>
                        </button>
                        <button onclick="shareScreen()" title="Share Screen">
                            <i class="fas fa-desktop"></i>
                        </button>
                        <button onclick="toggleVirtualBackground()" title="Virtual Background">
                            <i class="fas fa-image"></i>
                        </button>
                        <button onclick="enableAnnotation()" title="Screen Annotation">
                            <i class="fas fa-draw-polygon"></i>
                        </button>
                        <button onclick="toggleSettings()" title="Settings">
                            <i class="fas fa-cog"></i>
                        </button>
                    </div>
                </div>
                <div class="participant-videos">
                    <div class="participant-video">JD</div>
                    <div class="participant-video">MS</div>
                    <div class="participant-video">AL</div>
                    <div class="participant-video">+24</div>
                </div>
            </div>
        </div>

        <!-- Enhanced Whiteboard -->
        <div class="whiteboard-section">
            <div class="whiteboard-header">
                <h3><i class="fas fa-marker"></i> Collaborative Whiteboard</h3>
                <div class="whiteboard-tools">
                    <button class="tool-btn active" onclick="selectTool('pen')" data-tool="pen" title="Pen">
                        <i class="fas fa-pen"></i>
                    </button>
                    <button class="tool-btn" onclick="selectTool('highlighter')" data-tool="highlighter" title="Highlighter">
                        <i class="fas fa-highlighter"></i>
                    </button>
                    <button class="tool-btn" onclick="selectTool('eraser')" data-tool="eraser" title="Eraser">
                        <i class="fas fa-eraser"></i>
                    </button>
                    <button class="tool-btn" onclick="selectTool('text')" data-tool="text" title="Text">
                        <i class="fas fa-font"></i>
                    </button>
                    <button class="tool-btn" onclick="selectTool('shapes')" data-tool="shapes" title="Shapes">
                        <i class="fas fa-shapes"></i>
                    </button>
                    <button class="tool-btn" onclick="selectTool('sticky')" data-tool="sticky" title="Sticky Note">
                        <i class="fas fa-sticky-note"></i>
                    </button>
                    <button class="tool-btn" onclick="insertImage()" title="Insert Image">
                        <i class="fas fa-image"></i>
                    </button>
                    <button class="tool-btn" onclick="undoWhiteboard()" title="Undo">
                        <i class="fas fa-undo"></i>
                    </button>
                    <button class="tool-btn" onclick="redoWhiteboard()" title="Redo">
                        <i class="fas fa-redo"></i>
                    </button>
                    <button class="tool-btn" onclick="clearWhiteboard()" title="Clear">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            <canvas class="whiteboard-canvas" id="whiteboard"></canvas>
        </div>

        <!-- Enhanced Sidebar -->
        <div class="sidebar">
            <!-- Advanced Poll -->
            <div class="sidebar-section">
                <h3><i class="fas fa-poll-h"></i> Live Poll</h3>
                <div class="poll-container">
                    <div class="poll-option" onclick="votePoll(1)">
                        <span>Which topic needs more explanation?</span>
                    </div>
                    <div class="poll-option" onclick="votePoll(1)">
                        <span>Quantum Mechanics</span>
                        <span class="poll-results">45%</span>
                    </div>
                    <div class="poll-option" onclick="votePoll(2)">
                        <span>Molecular Biology</span>
                        <span class="poll-results">30%</span>
                    </div>
                    <div class="poll-option" onclick="votePoll(3)">
                        <span>Chemical Bonding</span>
                        <span class="poll-results">25%</span>
                    </div>
                    <div class="poll-progress">
                        <div class="poll-progress-bar" style="width: 45%"></div>
                    </div>
                </div>
            </div>

            <!-- Enhanced Q&A -->
            <div class="sidebar-section">
                <h3><i class="fas fa-question-circle"></i> Q&A Session</h3>
                <div class="qa-section">
                    <div class="question">
                        <div class="question-author">Dr. Sarah Johnson</div>
                        <div class="question-text">Could you explain the wave-particle duality in more detail?</div>
                        <div class="question-time">2 minutes ago</div>
                    </div>
                    <div class="question">
                        <div class="question-author">Student Mike</div>
                        <div class="question-text">What are the practical applications of quantum computing?</div>
                        <div class="question-time">5 minutes ago</div>
                    </div>
                    <div class="question">
                        <div class="question-author">Lisa Chen</div>
                        <div class="question-text">How does quantum entanglement work?</div>
                        <div class="question-time">8 minutes ago</div>
                    </div>
                </div>
                <input type="text" class="question-input" placeholder="Ask a question..." 
                       onkeypress="handleQuestionSubmit(event)">
            </div>
        </div>

        <!-- Live Chat -->
        <div class="chat-section">
            <div class="chat-header">
                <h3><i class="fas fa-comments"></i> Live Chat</h3>
                <div>
                    <button class="btn btn-secondary" onclick="toggleChat()">
                        <i class="fas fa-eye"></i>
                    </button>
                </div>
            </div>
            <div class="chat-messages" id="chatMessages">
                <div class="chat-message">
                    <strong>Alex:</strong> Great explanation of quantum mechanics!
                </div>
                <div class="chat-message">
                    <strong>Maria:</strong> Could you share the simulation link?
                </div>
                <div class="chat-message own">
                    <strong>You:</strong> I'll share it in the resources section
                </div>
            </div>
            <div class="chat-input-container">
                <input type="text" class="chat-input" placeholder="Type a message..." 
                       onkeypress="handleChatSubmit(event)">
                <button class="chat-send" onclick="sendChatMessage()">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
        </div>

        <!-- Enhanced Controls -->
        <div class="controls-panel">
            <!-- Participants with Advanced Features -->
            <div class="control-group">
                <h4><i class="fas fa-users"></i> Participants (127)</h4>
                <div class="participants-list">
                    <div class="participant">
                        <div class="participant-avatar">JD</div>
                        <div class="participant-info">
                            <div class="participant-name">John Doe</div>
                            <div class="participant-role">Student</div>
                        </div>
                        <div class="participant-status speaking"></div>
                    </div>
                    <div class="participant">
                        <div class="participant-avatar">MS</div>
                        <div class="participant-info">
                            <div class="participant-name">Maria Santos</div>
                            <div class="participant-role">TA</div>
                        </div>
                        <div class="participant-status"></div>
                    </div>
                    <div class="participant">
                        <div class="participant-avatar">AL</div>
                        <div class="participant-info">
                            <div class="participant-name">Alex Lee</div>
                            <div class="participant-role">Student</div>
                        </div>
                        <div class="participant-status"></div>
                    </div>
                </div>
            </div>

            <!-- Enhanced Resources -->
            <div class="control-group">
                <h4><i class="fas fa-folder-open"></i> Resources</h4>
                <div class="resources-list">
                    <div class="resource-item" onclick="openResource('quantum-notes')">
                        <div class="resource-icon">
                            <i class="fas fa-file-pdf"></i>
                        </div>
                        <div class="resource-info">
                            <div class="resource-name">Quantum Physics Notes</div>
                            <div class="resource-type">PDF • 2.5 MB</div>
                        </div>
                    </div>
                    <div class="resource-item" onclick="openResource('simulation')">
                        <div class="resource-icon">
                            <i class="fas fa-flask"></i>
                        </div>
                        <div class="resource-info">
                            <div class="resource-name">Quantum Simulation</div>
                            <div class="resource-type">Interactive • Web App</div>
                        </div>
                    </div>
                </div>
                <button class="btn btn-primary" onclick="uploadResource()" style="width: 100%; margin-top: 10px;">
                    <i class="fas fa-cloud-upload-alt"></i> Upload Resource
                </button>
            </div>
        </div>
    </div>

    <!-- Floating Action Buttons -->
    <div class="floating-actions">
        <button class="fab" onclick="toggleRecording()" id="recordFab" title="Record Lecture">
            <i class="fas fa-record-vinyl"></i>
        </button>
        <button class="fab" onclick="takeScreenshot()" title="Take Screenshot">
            <i class="fas fa-camera"></i>
        </button>
        <button class="fab" onclick="generateAnalytics()" title="View Analytics">
            <i class="fas fa-chart-line"></i>
        </button>
        <button class="fab" onclick="toggleFullscreen()" title="Fullscreen">
            <i class="fas fa-expand"></i>
        </button>
    </div>

    <!-- Enhanced Notification -->
    <div class="notification" id="notification"></div>

    <script>
        // Enhanced JavaScript functionality
        let isRecording = false;
        let lectureStartTime = Date.now();
        let chatMessages = [];
        let currentPoll = null;

        // Initialize enhanced features
        document.addEventListener('DOMContentLoaded', function() {
            initializeEnhancedFeatures();
            startLectureTimer();
            simulateRealTimeActivity();
        });

        function initializeEnhancedFeatures() {
            // Initialize all enhanced features
            showNotification('Advanced Interactive Lectures initialized!', 'success');
            
            // Add keyboard shortcuts
            document.addEventListener('keydown', handleKeyboardShortcuts);
            
            // Initialize drag and drop for resources
            initializeDragDrop();
        }

        function startLectureTimer() {
            setInterval(() => {
                const elapsed = Date.now() - lectureStartTime;
                const minutes = Math.floor(elapsed / 60000);
                const seconds = Math.floor((elapsed % 60000) / 1000);
                const timeString = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                
                // Update timer in header
                const timerElement = document.querySelector('.header-info span');
                if (timerElement) {
                    timerElement.innerHTML = `<i class="fas fa-clock"></i> ${timeString}`;
                }
            }, 1000);
        }

        function toggleRecording() {
            isRecording = !isRecording;
            const recordFab = document.getElementById('recordFab');
            
            if (isRecording) {
                recordFab.classList.add('recording');
                recordFab.innerHTML = '<i class="fas fa-stop"></i>';
                showNotification('Recording started', 'success');
            } else {
                recordFab.classList.remove('recording');
                recordFab.innerHTML = '<i class="fas fa-record-vinyl"></i>';
                showNotification('Recording stopped', 'info');
            }
        }

        function simulateRealTimeActivity() {
            // Simulate real-time chat messages
            setInterval(() => {
                if (Math.random() > 0.95) {
                    addRandomChatMessage();
                }
            }, 3000);

            // Simulate participant activity
            setInterval(() => {
                if (Math.random() > 0.9) {
                    updateParticipantStatus();
                }
            }, 2000);
        }

        function addRandomChatMessage() {
            const messages = [
                'This is fascinating!',
                'Could you repeat that?',
                'Great explanation!',
                'I have a question about this',
                'Thanks for the clarification'
            ];
            
            const authors = ['Student A', 'Student B', 'Dr. Wilson', 'TA Sarah', 'Guest Speaker'];
            const randomMessage = messages[Math.floor(Math.random() * messages.length)];
            const randomAuthor = authors[Math.floor(Math.random() * authors.length)];
            
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'chat-message';
            messageDiv.innerHTML = `<strong>${randomAuthor}:</strong> ${randomMessage}`;
            
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function handleKeyboardShortcuts(e) {
            // Enhanced keyboard shortcuts
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 'm':
                        e.preventDefault();
                        toggleMute();
                        break;
                    case 'd':
                        e.preventDefault();
                        toggleVideo();
                        break;
                    case 'r':
                        e.preventDefault();
                        toggleRecording();
                        break;
                    case 'p':
                        e.preventDefault();
                        createAdvancedPoll();
                        break;
                    case 'Enter':
                        e.preventDefault();
                        if (e.shiftKey) {
                            createBreakoutRooms();
                        } else {
                            toggleLecture();
                        }
                        break;
                }
            }
        }

        // Enhanced notification system
        function showNotification(message, type = 'info', duration = 3000) {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification ${type} show`;
            
            setTimeout(() => {
                notification.classList.remove('show');
            }, duration);
        }

        // Additional enhanced functions would go here...
        // These would include all the advanced features like:
        // - Advanced polling with different question types
        // - Breakout room management
        // - Screen annotation
        // - Virtual backgrounds
        // - Analytics generation
        // - Accessibility features
        // - And more...
    </script>
</body>
</html>