<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Virtual Electronics Laboratory - SimLab HUB</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="css/virtual-instruments.css" rel="stylesheet">
    <link href="css/electronics-lab.css" rel="stylesheet">
    <link href="css/language-switcher.css" rel="stylesheet">
    <link href="css/footer-styles.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #2d3748;
            overflow-x: hidden;
        }

        .electronics-lab {
            display: grid;
            grid-template-areas: 
                "header header header header"
                "components breadboard instruments measurements"
                "experiments circuit-analysis results tools";
            grid-template-columns: 250px 1fr 300px 250px;
            grid-template-rows: 80px 1fr 250px;
            height: 100vh;
            gap: 10px;
            padding: 10px;
        }

        .lab-header {
            grid-area: header;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 0 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .lab-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: #2563eb;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .lab-controls {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .lab-btn {
            background: linear-gradient(145deg, #4299e1, #3182ce);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 10px;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(66, 153, 225, 0.3);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .lab-btn:hover {
            background: linear-gradient(145deg, #3182ce, #2c5282);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(66, 153, 225, 0.4);
        }

        .panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            overflow-y: auto;
        }

        .components-panel {
            grid-area: components;
        }

        .breadboard-panel {
            grid-area: breadboard;
        }

        .instruments-panel {
            grid-area: instruments;
        }

        .measurements-panel {
            grid-area: measurements;
        }

        .experiments-panel {
            grid-area: experiments;
        }

        .circuit-analysis-panel {
            grid-area: circuit-analysis;
        }

        .results-panel {
            grid-area: results;
        }

        .tools-panel {
            grid-area: tools;
        }

        .panel-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e2e8f0;
        }

        .component-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }

        .component-item {
            background: linear-gradient(145deg, #f7fafc, #edf2f7);
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            padding: 12px;
            text-align: center;
            cursor: grab;
            transition: all 0.3s ease;
            user-select: none;
        }

        .component-item:hover {
            border-color: #4299e1;
            background: linear-gradient(145deg, #ebf8ff, #bee3f8);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(66, 153, 225, 0.2);
        }

        .component-icon {
            font-size: 1.5rem;
            color: #4299e1;
            margin-bottom: 8px;
        }

        .component-name {
            font-size: 0.8rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 4px;
        }

        .component-value {
            font-size: 0.7rem;
            color: #4a5568;
        }

        .breadboard-workspace {
            background: #2d5016;
            border-radius: 10px;
            height: 100%;
            position: relative;
            overflow: hidden;
            border: 3px solid #1a365d;
        }

        .breadboard-grid {
            position: absolute;
            top: 20px;
            left: 20px;
            right: 20px;
            bottom: 20px;
            background: repeating-linear-gradient(
                0deg,
                #4a5568 0px,
                #4a5568 2px,
                transparent 2px,
                transparent 12px
            );
        }

        .power-rails {
            position: absolute;
            top: 10px;
            left: 10px;
            right: 10px;
            height: 20px;
            background: linear-gradient(90deg, #e53e3e 0%, #e53e3e 48%, #2d3748 48%, #2d3748 52%, #4299e1 52%, #4299e1 100%);
            border-radius: 3px;
        }

        .experiment-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .experiment-item {
            background: linear-gradient(145deg, #f7fafc, #edf2f7);
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            padding: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .experiment-item:hover {
            border-color: #4299e1;
            background: linear-gradient(145deg, #ebf8ff, #bee3f8);
        }

        .experiment-item.active {
            border-color: #3182ce;
            background: linear-gradient(145deg, #3182ce, #2c5282);
            color: white;
        }

        .experiment-title {
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .experiment-description {
            font-size: 0.75rem;
            opacity: 0.8;
            line-height: 1.3;
        }

        .measurement-display {
            background: #1a202c;
            color: #00ff41;
            padding: 15px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 1.1rem;
            margin-bottom: 15px;
            border: 2px solid #4a5568;
            text-align: center;
        }

        .measurement-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }

        .measurement-card {
            background: #f7fafc;
            border-radius: 8px;
            padding: 12px;
            border: 2px solid #e2e8f0;
            text-align: center;
        }

        .measurement-value {
            font-size: 1.3rem;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 5px;
        }

        .measurement-label {
            font-size: 0.8rem;
            color: #4a5568;
            font-weight: 500;
        }

        .circuit-canvas {
            width: 100%;
            height: 200px;
            background: white;
            border-radius: 8px;
            border: 2px solid #e2e8f0;
            margin-bottom: 15px;
        }

        .analysis-controls {
            display: flex;
            gap: 8px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        .analysis-btn {
            background: linear-gradient(145deg, #48bb78, #38a169);
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .analysis-btn:hover {
            background: linear-gradient(145deg, #38a169, #2f855a);
            transform: translateY(-1px);
        }

        .tool-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }

        .tool-item {
            background: linear-gradient(145deg, #f7fafc, #edf2f7);
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            padding: 10px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .tool-item:hover {
            border-color: #4299e1;
            transform: scale(1.05);
        }

        .tool-item.active {
            border-color: #3182ce;
            background: linear-gradient(145deg, #3182ce, #2c5282);
            color: white;
        }

        .tool-icon {
            font-size: 1.5rem;
            color: #4299e1;
            margin-bottom: 5px;
        }

        .tool-item.active .tool-icon {
            color: white;
        }

        .tool-name {
            font-size: 0.8rem;
            font-weight: 600;
        }

        @media (max-width: 1400px) {
            .electronics-lab {
                grid-template-areas: 
                    "header header header"
                    "components breadboard instruments"
                    "experiments circuit-analysis measurements"
                    "results results tools";
                grid-template-columns: 200px 1fr 250px;
                grid-template-rows: 80px 1fr auto auto;
            }
        }

        @media (max-width: 1000px) {
            .electronics-lab {
                grid-template-areas: 
                    "header header"
                    "components breadboard"
                    "experiments instruments"
                    "circuit-analysis measurements"
                    "results tools";
                grid-template-columns: 200px 1fr;
                grid-template-rows: 80px 300px auto auto auto;
            }
        }

        @media (max-width: 768px) {
            .electronics-lab {
                grid-template-areas: 
                    "header"
                    "breadboard"
                    "components"
                    "experiments"
                    "instruments"
                    "measurements"
                    "circuit-analysis"
                    "results"
                    "tools";
                grid-template-columns: 1fr;
                grid-template-rows: 80px 250px auto auto auto auto auto auto auto;
            }
            
            .component-grid, .tool-grid {
                grid-template-columns: repeat(4, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="electronics-lab">
        <!-- Header -->
        <div class="lab-header">
            <div class="lab-title">
                <i class="fas fa-microchip"></i>
                <span data-translate="electronics.title">Virtual Electronics Laboratory</span>
            </div>
            <div class="lab-controls">
                <button class="lab-btn" id="power-btn">
                    <i class="fas fa-power-off"></i>
                    <span data-translate="ui.start">Power On</span>
                </button>
                <button class="lab-btn" id="simulate-btn">
                    <i class="fas fa-play"></i>
                    <span data-translate="electronics.simulation">Simulate</span>
                </button>
                <button class="lab-btn" id="analyze-btn">
                    <i class="fas fa-chart-line"></i>
                    <span data-translate="lab.analysis">Analyze</span>
                </button>
            </div>
        </div>

        <!-- Components Panel -->
        <div class="panel components-panel">
            <h3 class="panel-title">
                <i class="fas fa-puzzle-piece"></i>
                <span data-translate="electronics.components">Components</span>
            </h3>

            <div class="component-grid" id="component-library">
                <!-- Components will be populated here -->
            </div>
        </div>

        <!-- Breadboard Panel -->
        <div class="panel breadboard-panel">
            <h3 class="panel-title">
                <i class="fas fa-th"></i>
                <span data-translate="electronics.breadboard">Circuit Breadboard</span>
            </h3>

            <div class="breadboard-workspace" id="breadboard">
                <div class="power-rails"></div>
                <div class="breadboard-grid"></div>
                <canvas id="circuit-canvas" width="600" height="400" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%;"></canvas>
            </div>
        </div>

        <!-- Instruments Panel -->
        <div class="panel instruments-panel">
            <h3 class="panel-title">
                <i class="fas fa-tools"></i>
                Instruments
            </h3>
            
            <div id="active-instrument">
                <!-- Active instrument will be displayed here -->
            </div>
        </div>

        <!-- Measurements Panel -->
        <div class="panel measurements-panel">
            <h3 class="panel-title">
                <i class="fas fa-tachometer-alt"></i>
                Measurements
            </h3>
            
            <div class="measurement-display" id="primary-measurement">
                Select instrument to begin measurements
            </div>
            
            <div class="measurement-grid" id="measurement-grid">
                <!-- Measurement cards will be displayed here -->
            </div>
        </div>

        <!-- Experiments Panel -->
        <div class="panel experiments-panel">
            <h3 class="panel-title">
                <i class="fas fa-flask"></i>
                Experiments
            </h3>
            
            <div class="experiment-list" id="experiment-list">
                <!-- Experiments will be populated here -->
            </div>
        </div>

        <!-- Circuit Analysis Panel -->
        <div class="panel circuit-analysis-panel">
            <h3 class="panel-title">
                <i class="fas fa-calculator"></i>
                Circuit Analysis
            </h3>
            
            <div class="analysis-controls">
                <button class="analysis-btn" onclick="electronicsLab.analyzeOhmsLaw()">Ohm's Law</button>
                <button class="analysis-btn" onclick="electronicsLab.analyzeKirchhoff()">Kirchhoff</button>
                <button class="analysis-btn" onclick="electronicsLab.analyzePower()">Power</button>
                <button class="analysis-btn" onclick="electronicsLab.analyzeFrequency()">Frequency</button>
            </div>
            
            <canvas class="circuit-canvas" id="analysis-canvas" width="300" height="200"></canvas>
            
            <div id="analysis-results">
                <!-- Analysis results will be displayed here -->
            </div>
        </div>

        <!-- Results Panel -->
        <div class="panel results-panel">
            <h3 class="panel-title">
                <i class="fas fa-chart-bar"></i>
                Results & Data
            </h3>
            
            <div id="results-display">
                <!-- Results will be displayed here -->
            </div>
        </div>

        <!-- Tools Panel -->
        <div class="panel tools-panel">
            <h3 class="panel-title">
                <i class="fas fa-wrench"></i>
                Tools
            </h3>
            
            <div class="tool-grid" id="tool-grid">
                <!-- Tools will be populated here -->
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/virtual-instruments.js"></script>
    <script src="js/data-logger.js"></script>
    <script src="js/electronics-engine.js"></script>
    <script src="js/language-system.js"></script>
    <script src="js/footer-component.js"></script>
    
    <script>
        // Electronics Lab Controller
        class ElectronicsLab {
            constructor() {
                this.circuit = new CircuitBuilder();
                this.simulator = new CircuitSimulator();
                this.analyzer = new CircuitAnalyzer();
                this.currentExperiment = null;
                this.isPowered = false;
                this.selectedTool = null;
                
                this.init();
            }

            init() {
                this.loadComponents();
                this.loadExperiments();
                this.loadTools();
                this.setupEventListeners();
                this.initializeInstruments();
            }

            loadComponents() {
                const components = [
                    { id: 'resistor', name: 'Resistor', icon: 'fas fa-minus', values: ['1kΩ', '10kΩ', '100kΩ'] },
                    { id: 'capacitor', name: 'Capacitor', icon: 'fas fa-battery-quarter', values: ['1µF', '10µF', '100µF'] },
                    { id: 'inductor', name: 'Inductor', icon: 'fas fa-circle', values: ['1mH', '10mH', '100mH'] },
                    { id: 'diode', name: 'Diode', icon: 'fas fa-play', values: ['1N4148', '1N4007'] },
                    { id: 'led', name: 'LED', icon: 'fas fa-lightbulb', values: ['Red', 'Green', 'Blue'] },
                    { id: 'transistor', name: 'Transistor', icon: 'fas fa-microchip', values: ['2N2222', 'BC547'] },
                    { id: 'opamp', name: 'Op-Amp', icon: 'fas fa-triangle', values: ['LM741', 'LM358'] },
                    { id: 'ic555', name: '555 Timer', icon: 'fas fa-clock', values: ['NE555'] }
                ];

                const container = document.getElementById('component-library');
                container.innerHTML = '';

                components.forEach(component => {
                    const element = document.createElement('div');
                    element.className = 'component-item';
                    element.draggable = true;
                    element.dataset.componentId = component.id;
                    
                    element.innerHTML = `
                        <div class="component-icon">
                            <i class="${component.icon}"></i>
                        </div>
                        <div class="component-name">${component.name}</div>
                        <div class="component-value">${component.values[0]}</div>
                    `;
                    
                    element.addEventListener('dragstart', (e) => {
                        e.dataTransfer.setData('text/plain', component.id);
                    });
                    
                    container.appendChild(element);
                });
            }

            loadExperiments() {
                const experiments = [
                    { id: 'ohms_law', title: "Ohm's Law", description: "Verify V = I × R relationship" },
                    { id: 'series_parallel', title: "Series & Parallel", description: "Compare circuit configurations" },
                    { id: 'kirchhoff', title: "Kirchhoff's Laws", description: "Apply KCL and KVL" },
                    { id: 'rc_circuit', title: "RC Circuits", description: "Capacitor charging/discharging" },
                    { id: 'diode_char', title: "Diode I-V Curve", description: "Plot diode characteristics" },
                    { id: 'rectifier', title: "Rectifier Circuit", description: "AC to DC conversion" },
                    { id: 'amplifier', title: "Amplifier", description: "Signal amplification" },
                    { id: 'logic_gates', title: "Logic Gates", description: "Digital logic operations" }
                ];

                const container = document.getElementById('experiment-list');
                container.innerHTML = '';

                experiments.forEach(exp => {
                    const element = document.createElement('div');
                    element.className = 'experiment-item';
                    element.dataset.experimentId = exp.id;
                    
                    element.innerHTML = `
                        <div class="experiment-title">${exp.title}</div>
                        <div class="experiment-description">${exp.description}</div>
                    `;
                    
                    element.addEventListener('click', () => this.selectExperiment(exp));
                    container.appendChild(element);
                });
            }

            loadTools() {
                const tools = [
                    { id: 'multimeter', name: 'Multimeter', icon: 'fas fa-tachometer-alt' },
                    { id: 'oscilloscope', name: 'Oscilloscope', icon: 'fas fa-wave-square' },
                    { id: 'function_gen', name: 'Function Gen', icon: 'fas fa-signal' },
                    { id: 'power_supply', name: 'Power Supply', icon: 'fas fa-plug' }
                ];

                const container = document.getElementById('tool-grid');
                container.innerHTML = '';

                tools.forEach(tool => {
                    const element = document.createElement('div');
                    element.className = 'tool-item';
                    element.dataset.toolId = tool.id;
                    
                    element.innerHTML = `
                        <div class="tool-icon">
                            <i class="${tool.icon}"></i>
                        </div>
                        <div class="tool-name">${tool.name}</div>
                    `;
                    
                    element.addEventListener('click', () => this.selectTool(tool));
                    container.appendChild(element);
                });
            }

            selectExperiment(experiment) {
                // Update UI
                document.querySelectorAll('.experiment-item').forEach(item => {
                    item.classList.remove('active');
                });
                document.querySelector(`[data-experiment-id="${experiment.id}"]`).classList.add('active');

                this.currentExperiment = experiment;
                this.loadExperimentSetup(experiment);
            }

            selectTool(tool) {
                // Update UI
                document.querySelectorAll('.tool-item').forEach(item => {
                    item.classList.remove('active');
                });
                document.querySelector(`[data-tool-id="${tool.id}"]`).classList.add('active');

                this.selectedTool = tool;
                this.loadToolInterface(tool);
            }

            loadToolInterface(tool) {
                const container = document.getElementById('active-instrument');
                
                switch (tool.id) {
                    case 'multimeter':
                        virtualVoltmeter.createUI('active-instrument');
                        break;
                    case 'oscilloscope':
                        virtualOscilloscope.createUI('active-instrument');
                        break;
                    default:
                        container.innerHTML = `<div style="text-align: center; padding: 20px; color: #4a5568;">
                            <i class="${tool.icon}" style="font-size: 3rem; margin-bottom: 10px; color: #4299e1;"></i>
                            <div>${tool.name} interface will be loaded here</div>
                        </div>`;
                }
            }

            loadExperimentSetup(experiment) {
                // Load predefined circuit for experiment
                this.circuit.clear();
                
                switch (experiment.id) {
                    case 'ohms_law':
                        this.setupOhmsLawCircuit();
                        break;
                    case 'series_parallel':
                        this.setupSeriesParallelCircuit();
                        break;
                    // Add more experiment setups
                }
            }

            setupOhmsLawCircuit() {
                // Create a simple resistor circuit
                this.circuit.addComponent('voltage_source', { x: 100, y: 200, voltage: 5 });
                this.circuit.addComponent('resistor', { x: 300, y: 200, resistance: 1000 });
                this.circuit.addWire({ from: { x: 100, y: 200 }, to: { x: 300, y: 200 } });
                this.circuit.addWire({ from: { x: 300, y: 250 }, to: { x: 100, y: 250 } });
            }

            setupSeriesParallelCircuit() {
                // Create series and parallel resistor combinations
                // Implementation details...
            }

            setupEventListeners() {
                document.getElementById('power-btn').addEventListener('click', () => {
                    this.togglePower();
                });

                document.getElementById('simulate-btn').addEventListener('click', () => {
                    this.runSimulation();
                });

                document.getElementById('analyze-btn').addEventListener('click', () => {
                    this.analyzeCircuit();
                });
            }

            initializeInstruments() {
                // Initialize with multimeter by default
                this.selectTool({ id: 'multimeter', name: 'Multimeter', icon: 'fas fa-tachometer-alt' });
            }

            togglePower() {
                this.isPowered = !this.isPowered;
                const btn = document.getElementById('power-btn');
                
                if (this.isPowered) {
                    btn.innerHTML = '<i class="fas fa-power-off"></i> Power Off';
                    btn.style.background = 'linear-gradient(145deg, #e53e3e, #c53030)';
                } else {
                    btn.innerHTML = '<i class="fas fa-power-off"></i> Power On';
                    btn.style.background = 'linear-gradient(145deg, #4299e1, #3182ce)';
                }
                
                this.circuit.setPower(this.isPowered);
            }

            runSimulation() {
                if (!this.isPowered) {
                    alert('Please turn on power first');
                    return;
                }
                
                this.simulator.simulate(this.circuit);
                this.updateMeasurements();
            }

            analyzeCircuit() {
                const results = this.analyzer.analyze(this.circuit);
                this.displayAnalysisResults(results);
            }

            updateMeasurements() {
                // Update measurement displays
                const measurements = this.simulator.getMeasurements();
                
                document.getElementById('primary-measurement').textContent = 
                    `Voltage: ${measurements.voltage.toFixed(2)}V | Current: ${measurements.current.toFixed(3)}A`;
                
                // Update measurement cards
                this.updateMeasurementCards(measurements);
            }

            updateMeasurementCards(measurements) {
                const container = document.getElementById('measurement-grid');
                container.innerHTML = '';
                
                Object.entries(measurements).forEach(([key, value]) => {
                    const card = document.createElement('div');
                    card.className = 'measurement-card';
                    
                    card.innerHTML = `
                        <div class="measurement-value">${value.toFixed(3)}</div>
                        <div class="measurement-label">${key.toUpperCase()}</div>
                    `;
                    
                    container.appendChild(card);
                });
            }

            displayAnalysisResults(results) {
                const container = document.getElementById('analysis-results');
                container.innerHTML = `
                    <div style="background: #f7fafc; padding: 15px; border-radius: 8px; border: 1px solid #e2e8f0;">
                        <h4 style="margin-bottom: 10px; color: #2d3748;">Analysis Results</h4>
                        <div style="font-family: monospace; font-size: 0.9rem; line-height: 1.4;">
                            ${results.map(result => `<div>${result}</div>`).join('')}
                        </div>
                    </div>
                `;
            }

            // Analysis methods
            analyzeOhmsLaw() {
                const results = this.analyzer.calculateOhmsLaw(this.circuit);
                this.displayAnalysisResults(results);
            }

            analyzeKirchhoff() {
                const results = this.analyzer.applyKirchhoffLaws(this.circuit);
                this.displayAnalysisResults(results);
            }

            analyzePower() {
                const results = this.analyzer.calculatePower(this.circuit);
                this.displayAnalysisResults(results);
            }

            analyzeFrequency() {
                const results = this.analyzer.frequencyAnalysis(this.circuit);
                this.displayAnalysisResults(results);
            }
        }

        // Initialize Electronics Lab
        let electronicsLab;
        document.addEventListener('DOMContentLoaded', function() {
            electronicsLab = new ElectronicsLab();
        });
    </script>
</body>
</html>
