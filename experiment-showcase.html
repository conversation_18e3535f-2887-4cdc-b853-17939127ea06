<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>معرض التجارب الفيزيائية - Experiment Showcase | SimLab HUB</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800&display=swap" rel="stylesheet">
    <link href="css/virtual-instruments.css" rel="stylesheet">
    <link href="css/language-switcher.css" rel="stylesheet">
    <link href="css/footer-styles.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: '<PERSON><PERSON><PERSON>', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .showcase-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .header h1 {
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }

        .experiment-categories {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 50px;
        }

        .category-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .category-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .category-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
        }

        .category-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            margin: 0 auto 20px;
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }

        .category-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 15px;
            text-align: center;
            color: #2d3748;
        }

        .experiments-showcase {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .experiment-demo {
            background: #f7fafc;
            border-radius: 10px;
            padding: 15px;
            border: 2px solid #e2e8f0;
            transition: all 0.3s ease;
            cursor: pointer;
            text-align: center;
        }

        .experiment-demo:hover {
            border-color: #667eea;
            background: #f0f4ff;
            transform: scale(1.02);
        }

        .demo-visual {
            width: 100%;
            height: 120px;
            background: linear-gradient(135deg, #edf2f7, #e2e8f0);
            border-radius: 8px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .demo-title {
            font-size: 1rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 5px;
        }

        .demo-description {
            font-size: 0.85rem;
            color: #4a5568;
            line-height: 1.4;
        }

        /* Specific Physics Animations */
        .projectile-demo {
            position: relative;
        }

        .projectile-ball {
            width: 20px;
            height: 20px;
            background: radial-gradient(circle, #f6ad55, #ed8936);
            border-radius: 50%;
            position: absolute;
            box-shadow: 0 0 15px rgba(246, 173, 85, 0.6);
            animation: projectileMotion 3s ease-in-out infinite;
        }

        @keyframes projectileMotion {
            0% { left: 10px; bottom: 10px; }
            50% { left: 50%; bottom: 80px; }
            100% { left: calc(100% - 30px); bottom: 10px; }
        }

        .pendulum-demo {
            position: relative;
        }

        .pendulum-string {
            width: 2px;
            height: 80px;
            background: #4a5568;
            position: absolute;
            left: 50%;
            top: 10px;
            transform-origin: top center;
            animation: pendulumSwing 2s ease-in-out infinite;
        }

        .pendulum-bob {
            width: 16px;
            height: 16px;
            background: radial-gradient(circle, #90cdf4, #4299e1);
            border-radius: 50%;
            position: absolute;
            left: calc(50% - 8px);
            bottom: 30px;
            box-shadow: 0 0 10px rgba(144, 205, 244, 0.6);
            animation: pendulumBob 2s ease-in-out infinite;
        }

        @keyframes pendulumSwing {
            0%, 100% { transform: rotate(-30deg); }
            50% { transform: rotate(30deg); }
        }

        @keyframes pendulumBob {
            0%, 100% { transform: translateX(-20px); }
            50% { transform: translateX(20px); }
        }

        .wave-demo {
            position: relative;
            overflow: hidden;
        }

        .wave-line {
            position: absolute;
            width: 200%;
            height: 3px;
            background: linear-gradient(90deg, 
                transparent, #4299e1, transparent, #4299e1, 
                transparent, #4299e1, transparent);
            top: 50%;
            left: -50%;
            animation: waveMotion 2s linear infinite;
        }

        @keyframes waveMotion {
            0% { transform: translateX(0); }
            100% { transform: translateX(50px); }
        }

        .collision-demo {
            position: relative;
        }

        .collision-ball1, .collision-ball2 {
            width: 18px;
            height: 18px;
            border-radius: 50%;
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
        }

        .collision-ball1 {
            background: radial-gradient(circle, #f6ad55, #ed8936);
            animation: collisionBall1 3s ease-in-out infinite;
        }

        .collision-ball2 {
            background: radial-gradient(circle, #90cdf4, #4299e1);
            animation: collisionBall2 3s ease-in-out infinite;
        }

        @keyframes collisionBall1 {
            0% { left: 10px; }
            45% { left: calc(50% - 20px); }
            55% { left: calc(50% + 10px); }
            100% { left: calc(100% - 50px); }
        }

        @keyframes collisionBall2 {
            0% { left: calc(100% - 30px); }
            45% { left: calc(50% + 10px); }
            55% { left: calc(50% - 20px); }
            100% { left: 20px; }
        }

        .spring-demo {
            position: relative;
        }

        .spring-coil {
            width: 60px;
            height: 10px;
            background: repeating-linear-gradient(90deg, 
                #4a5568 0px, #4a5568 3px, 
                transparent 3px, transparent 6px);
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            animation: springCompress 2s ease-in-out infinite;
        }

        .spring-mass {
            width: 20px;
            height: 20px;
            background: #f6ad55;
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            animation: springMass 2s ease-in-out infinite;
        }

        @keyframes springCompress {
            0%, 100% { width: 60px; }
            50% { width: 40px; }
        }

        @keyframes springMass {
            0%, 100% { right: 20px; }
            50% { right: 40px; }
        }

        .optics-demo {
            position: relative;
        }

        .light-ray {
            width: 2px;
            height: 40px;
            background: linear-gradient(180deg, #ffd700, transparent);
            position: absolute;
            left: 20px;
            top: 20px;
            transform-origin: bottom center;
            animation: lightRay 3s ease-in-out infinite;
        }

        .lens {
            width: 30px;
            height: 60px;
            border: 3px solid #4a5568;
            border-radius: 50%;
            position: absolute;
            left: 50%;
            top: 30px;
            transform: translateX(-50%);
        }

        .refracted-ray {
            width: 2px;
            height: 40px;
            background: linear-gradient(180deg, #ff6b6b, transparent);
            position: absolute;
            right: 20px;
            bottom: 20px;
            transform-origin: top center;
            animation: refractedRay 3s ease-in-out infinite;
        }

        @keyframes lightRay {
            0%, 100% { transform: rotate(-10deg); }
            50% { transform: rotate(10deg); }
        }

        @keyframes refractedRay {
            0%, 100% { transform: rotate(10deg); }
            50% { transform: rotate(-10deg); }
        }

        .featured-experiment {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            backdrop-filter: blur(10px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }

        .featured-title {
            text-align: center;
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 30px;
            color: #2d3748;
        }

        .experiment-interface {
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: 30px;
            align-items: center;
        }

        .interactive-canvas {
            background: radial-gradient(circle at center, #2d3748 0%, #1a202c 100%);
            border-radius: 15px;
            height: 400px;
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid #4a5568;
        }

        .controls-panel {
            background: #f7fafc;
            border-radius: 15px;
            padding: 25px;
            border: 2px solid #e2e8f0;
        }

        .control-group {
            margin-bottom: 20px;
        }

        .control-label {
            display: block;
            font-size: 0.9rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 8px;
        }

        .control-slider {
            width: 100%;
            height: 6px;
            border-radius: 3px;
            background: #e2e8f0;
            outline: none;
            transition: all 0.3s ease;
        }

        .control-slider::-webkit-slider-thumb {
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #667eea;
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(102, 126, 234, 0.3);
        }

        .control-value {
            font-size: 0.9rem;
            color: #4a5568;
            font-weight: 600;
            margin-top: 5px;
        }

        .experiment-button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            margin-bottom: 15px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .experiment-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .data-display {
            background: #edf2f7;
            border-radius: 10px;
            padding: 15px;
            font-size: 0.9rem;
        }

        .data-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }

        .data-label {
            color: #4a5568;
        }

        .data-value {
            color: #2d3748;
            font-weight: 600;
        }

        @media (max-width: 768px) {
            .experiment-interface {
                grid-template-columns: 1fr;
            }
            
            .interactive-canvas {
                height: 300px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .showcase-container {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="showcase-container">
        <!-- Header -->
        <div class="header">
            <h1>🔬 معرض التجارب الفيزيائية</h1>
            <p>اكتشف عجائب الفيزياء من خلال تجارب تفاعلية مذهلة ومحاكاة واقعية</p>
        </div>

        <!-- Experiment Categories -->
        <div class="experiment-categories">
            <!-- Mechanics -->
            <div class="category-card">
                <div class="category-icon">
                    <i class="fas fa-globe"></i>
                </div>
                <h2 class="category-title">الميكانيكا</h2>
                <div class="experiments-showcase">
                    <div class="experiment-demo" onclick="launchExperiment('projectile')">
                        <div class="demo-visual projectile-demo">
                            <div class="projectile-ball"></div>
                        </div>
                        <div class="demo-title">حركة المقذوفات</div>
                        <div class="demo-description">استكشف مسار الأجسام المقذوفة</div>
                    </div>
                    
                    <div class="experiment-demo" onclick="launchExperiment('pendulum')">
                        <div class="demo-visual pendulum-demo">
                            <div class="pendulum-string"></div>
                            <div class="pendulum-bob"></div>
                        </div>
                        <div class="demo-title">البندول البسيط</div>
                        <div class="demo-description">دراسة الحركة التوافقية</div>
                    </div>

                    <div class="experiment-demo" onclick="launchExperiment('collision')">
                        <div class="demo-visual collision-demo">
                            <div class="collision-ball1"></div>
                            <div class="collision-ball2"></div>
                        </div>
                        <div class="demo-title">التصادمات</div>
                        <div class="demo-description">حفظ الزخم والطاقة</div>
                    </div>

                    <div class="experiment-demo" onclick="launchExperiment('spring')">
                        <div class="demo-visual spring-demo">
                            <div class="spring-coil"></div>
                            <div class="spring-mass"></div>
                        </div>
                        <div class="demo-title">نظام النابض</div>
                        <div class="demo-description">تحولات الطاقة الحركية</div>
                    </div>
                </div>
            </div>

            <!-- Thermodynamics -->
            <div class="category-card">
                <div class="category-icon">
                    <i class="fas fa-thermometer-half"></i>
                </div>
                <h2 class="category-title">الديناميكا الحرارية</h2>
                <div class="experiments-showcase">
                    <div class="experiment-demo" onclick="launchExperiment('gas-laws')">
                        <div class="demo-visual">
                            <i class="fas fa-flask" style="font-size: 3rem; color: #4299e1;"></i>
                        </div>
                        <div class="demo-title">قوانين الغازات</div>
                        <div class="demo-description">العلاقة بين الضغط والحجم</div>
                    </div>
                    
                    <div class="experiment-demo" onclick="launchExperiment('heat-transfer')">
                        <div class="demo-visual">
                            <i class="fas fa-fire" style="font-size: 3rem; color: #f6ad55;"></i>
                        </div>
                        <div class="demo-title">انتقال الحرارة</div>
                        <div class="demo-description">التوصيل والحمل والإشعاع</div>
                    </div>

                    <div class="experiment-demo" onclick="launchExperiment('phase-change')">
                        <div class="demo-visual">
                            <i class="fas fa-snowflake" style="font-size: 3rem; color: #90cdf4;"></i>
                        </div>
                        <div class="demo-title">تغيرات الطور</div>
                        <div class="demo-description">الانصهار والتبخر</div>
                    </div>

                    <div class="experiment-demo" onclick="launchExperiment('entropy')">
                        <div class="demo-visual">
                            <i class="fas fa-chart-line" style="font-size: 3rem; color: #48bb78;"></i>
                        </div>
                        <div class="demo-title">الانتروبيا</div>
                        <div class="demo-description">الترتيب والفوضى</div>
                    </div>
                </div>
            </div>

            <!-- Optics -->
            <div class="category-card">
                <div class="category-icon">
                    <i class="fas fa-eye"></i>
                </div>
                <h2 class="category-title">البصريات</h2>
                <div class="experiments-showcase">
                    <div class="experiment-demo" onclick="launchExperiment('snell-law')">
                        <div class="demo-visual optics-demo">
                            <div class="light-ray"></div>
                            <div class="lens"></div>
                            <div class="refracted-ray"></div>
                        </div>
                        <div class="demo-title">قانون سنيل</div>
                        <div class="demo-description">انكسار الضوء</div>
                    </div>
                    
                    <div class="experiment-demo" onclick="launchExperiment('interference')">
                        <div class="demo-visual wave-demo">
                            <div class="wave-line"></div>
                        </div>
                        <div class="demo-title">التداخل</div>
                        <div class="demo-description">تداخل الموجات الضوئية</div>
                    </div>

                    <div class="experiment-demo" onclick="launchExperiment('diffraction')">
                        <div class="demo-visual">
                            <i class="fas fa-adjust" style="font-size: 3rem; color: #ed8936;"></i>
                        </div>
                        <div class="demo-title">الحيود</div>
                        <div class="demo-description">انحناء الضوء</div>
                    </div>

                    <div class="experiment-demo" onclick="launchExperiment('polarization')">
                        <div class="demo-visual">
                            <i class="fas fa-filter" style="font-size: 3rem; color: #9f7aea;"></i>
                        </div>
                        <div class="demo-title">الاستقطاب</div>
                        <div class="demo-description">ترشيح الضوء</div>
                    </div>
                </div>
            </div>

            <!-- Modern Physics -->
            <div class="category-card">
                <div class="category-icon">
                    <i class="fas fa-atom"></i>
                </div>
                <h2 class="category-title">الفيزياء الحديثة</h2>
                <div class="experiments-showcase">
                    <div class="experiment-demo" onclick="launchExperiment('photoelectric')">
                        <div class="demo-visual">
                            <i class="fas fa-sun" style="font-size: 3rem; color: #ffd700;"></i>
                        </div>
                        <div class="demo-title">التأثير الكهروضوئي</div>
                        <div class="demo-description">إنبعاث الإلكترونات</div>
                    </div>
                    
                    <div class="experiment-demo" onclick="launchExperiment('radioactive')">
                        <div class="demo-visual">
                            <i class="fas fa-radiation" style="font-size: 3rem; color: #f56565;"></i>
                        </div>
                        <div class="demo-title">التفكك الإشعاعي</div>
                        <div class="demo-description">عمر النصف</div>
                    </div>

                    <div class="experiment-demo" onclick="launchExperiment('quantum')">
                        <div class="demo-visual">
                            <i class="fas fa-infinity" style="font-size: 3rem; color: #4299e1;"></i>
                        </div>
                        <div class="demo-title">الميكانيكا الكمية</div>
                        <div class="demo-description">عالم الذرات</div>
                    </div>

                    <div class="experiment-demo" onclick="launchExperiment('relativity')">
                        <div class="demo-visual">
                            <i class="fas fa-rocket" style="font-size: 3rem; color: #ed8936;"></i>
                        </div>
                        <div class="demo-title">النسبية</div>
                        <div class="demo-description">الزمان والمكان</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Featured Interactive Experiment -->
        <div class="featured-experiment">
            <h2 class="featured-title">🎯 تجربة تفاعلية مميزة: حركة المقذوفات</h2>
            
            <div class="experiment-interface">
                <div class="interactive-canvas" id="featuredCanvas">
                    <div style="color: #90cdf4; text-align: center;">
                        <i class="fas fa-rocket" style="font-size: 4rem; margin-bottom: 20px;"></i>
                        <div style="font-size: 1.2rem;">اضبط المتغيرات واضغط تشغيل</div>
                    </div>
                </div>
                
                <div class="controls-panel">
                    <div class="control-group">
                        <label class="control-label">السرعة الابتدائية (م/ث)</label>
                        <input type="range" class="control-slider" id="velocitySlider" 
                               min="5" max="50" value="25" oninput="updateControls()">
                        <div class="control-value" id="velocityValue">25 م/ث</div>
                    </div>
                    
                    <div class="control-group">
                        <label class="control-label">زاوية الإطلاق (درجة)</label>
                        <input type="range" class="control-slider" id="angleSlider" 
                               min="0" max="90" value="45" oninput="updateControls()">
                        <div class="control-value" id="angleValue">45°</div>
                    </div>
                    
                    <div class="control-group">
                        <label class="control-label">الكتلة (كغ)</label>
                        <input type="range" class="control-slider" id="massSlider" 
                               min="0.1" max="5" value="1" step="0.1" oninput="updateControls()">
                        <div class="control-value" id="massValue">1.0 كغ</div>
                    </div>
                    
                    <button class="experiment-button" onclick="runFeaturedExperiment()">
                        <i class="fas fa-play"></i> تشغيل التجربة
                    </button>
                    
                    <button class="experiment-button" onclick="resetFeaturedExperiment()" 
                            style="background: linear-gradient(135deg, #f56565, #e53e3e);">
                        <i class="fas fa-redo"></i> إعادة تعيين
                    </button>
                    
                    <div class="data-display" id="experimentData">
                        <div class="data-row">
                            <span class="data-label">المدى الأقصى:</span>
                            <span class="data-value" id="maxRange">0 م</span>
                        </div>
                        <div class="data-row">
                            <span class="data-label">الارتفاع الأقصى:</span>
                            <span class="data-value" id="maxHeight">0 م</span>
                        </div>
                        <div class="data-row">
                            <span class="data-label">زمن الطيران:</span>
                            <span class="data-value" id="flightTime">0 ث</span>
                        </div>
                        <div class="data-row">
                            <span class="data-label">الطاقة الابتدائية:</span>
                            <span class="data-value" id="initialEnergy">0 J</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Access -->
        <div style="text-align: center; margin-bottom: 40px;">
            <button class="experiment-button" onclick="goToVirtualLabs()" 
                    style="display: inline-block; width: auto; padding: 15px 30px; font-size: 1.1rem;">
                <i class="fas fa-flask"></i> دخول المختبرات الافتراضية
            </button>
        </div>
    </div>

    <script>
        let isExperimentRunning = false;
        let projectile = null;

        function updateControls() {
            const velocity = document.getElementById('velocitySlider').value;
            const angle = document.getElementById('angleSlider').value;
            const mass = document.getElementById('massSlider').value;
            
            document.getElementById('velocityValue').textContent = velocity + ' م/ث';
            document.getElementById('angleValue').textContent = angle + '°';
            document.getElementById('massValue').textContent = parseFloat(mass).toFixed(1) + ' كغ';
            
            // Calculate and display results
            calculateProjectileData(parseFloat(velocity), parseFloat(angle), parseFloat(mass));
        }

        function calculateProjectileData(v0, angle, mass) {
            const g = 9.8;
            const angleRad = angle * Math.PI / 180;
            const v0y = v0 * Math.sin(angleRad);
            const v0x = v0 * Math.cos(angleRad);
            
            const flightTime = (2 * v0y) / g;
            const maxRange = (v0 * v0 * Math.sin(2 * angleRad)) / g;
            const maxHeight = (v0y * v0y) / (2 * g);
            const initialEnergy = 0.5 * mass * v0 * v0;
            
            document.getElementById('maxRange').textContent = maxRange.toFixed(1) + ' م';
            document.getElementById('maxHeight').textContent = maxHeight.toFixed(1) + ' م';
            document.getElementById('flightTime').textContent = flightTime.toFixed(2) + ' ث';
            document.getElementById('initialEnergy').textContent = initialEnergy.toFixed(1) + ' J';
        }

        function runFeaturedExperiment() {
            if (isExperimentRunning) return;
            
            isExperimentRunning = true;
            const canvas = document.getElementById('featuredCanvas');
            
            // Clear canvas
            canvas.innerHTML = '';
            
            // Create projectile
            projectile = document.createElement('div');
            projectile.style.cssText = `
                width: 20px;
                height: 20px;
                background: radial-gradient(circle, #f6ad55, #ed8936);
                border-radius: 50%;
                position: absolute;
                left: 50px;
                bottom: 50px;
                box-shadow: 0 0 15px rgba(246, 173, 85, 0.6);
                transition: all 0.1s linear;
                z-index: 10;
            `;
            
            canvas.appendChild(projectile);
            
            // Get parameters
            const velocity = parseFloat(document.getElementById('velocitySlider').value);
            const angle = parseFloat(document.getElementById('angleSlider').value);
            const mass = parseFloat(document.getElementById('massSlider').value);
            
            // Start animation
            animateProjectile(velocity, angle);
        }

        function animateProjectile(v0, angle) {
            const g = 9.8;
            const angleRad = angle * Math.PI / 180;
            const v0x = v0 * Math.cos(angleRad);
            const v0y = v0 * Math.sin(angleRad);
            
            const startTime = Date.now();
            const scale = 8; // pixels per meter
            
            function animate() {
                const t = (Date.now() - startTime) / 1000;
                
                const x = v0x * t;
                const y = v0y * t - 0.5 * g * t * t;
                
                if (y >= 0 && projectile) {
                    projectile.style.left = (50 + x * scale) + 'px';
                    projectile.style.bottom = (50 + y * scale) + 'px';
                    
                    // Add trail effect
                    createTrail(projectile.style.left, projectile.style.bottom);
                    
                    requestAnimationFrame(animate);
                } else {
                    // Animation finished
                    isExperimentRunning = false;
                    if (projectile) {
                        projectile.style.background = 'radial-gradient(circle, #48bb78, #38a169)';
                    }
                }
            }
            
            animate();
        }

        function createTrail(x, y) {
            const canvas = document.getElementById('featuredCanvas');
            const trail = document.createElement('div');
            trail.style.cssText = `
                width: 4px;
                height: 4px;
                background: rgba(246, 173, 85, 0.6);
                border-radius: 50%;
                position: absolute;
                left: ${x};
                bottom: ${y};
                pointer-events: none;
                animation: fadeTrail 2s ease-out forwards;
            `;
            
            canvas.appendChild(trail);
            
            // Remove trail after animation
            setTimeout(() => trail.remove(), 2000);
        }

        function resetFeaturedExperiment() {
            isExperimentRunning = false;
            const canvas = document.getElementById('featuredCanvas');
            canvas.innerHTML = `
                <div style="color: #90cdf4; text-align: center;">
                    <i class="fas fa-rocket" style="font-size: 4rem; margin-bottom: 20px;"></i>
                    <div style="font-size: 1.2rem;">اضبط المتغيرات واضغط تشغيل</div>
                </div>
            `;
            projectile = null;
        }

        function launchExperiment(experimentType) {
            // Show loading animation
            const overlay = document.createElement('div');
            overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.8);
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-size: 1.2rem;
                z-index: 10000;
            `;
            
            overlay.innerHTML = `
                <div style="text-align: center;">
                    <div style="width: 50px; height: 50px; border: 3px solid rgba(255,255,255,0.3); 
                                border-top: 3px solid white; border-radius: 50%; 
                                animation: spin 1s linear infinite; margin: 0 auto 20px;"></div>
                    <div>جاري تحميل التجربة...</div>
                </div>
            `;
            
            document.body.appendChild(overlay);
            
            // Simulate loading and redirect
            setTimeout(() => {
                window.location.href = `physics-lab.html?experiment=${experimentType}`;
            }, 1500);
        }

        function goToVirtualLabs() {
            window.location.href = 'virtual-labs.html';
        }

        // Add CSS animation for trail
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeTrail {
                from { opacity: 0.6; transform: scale(1); }
                to { opacity: 0; transform: scale(0.5); }
            }
            
            @keyframes spin {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            updateControls();
        });
    </script>

    <!-- Language System and Footer -->
    <script src="js/language-system.js"></script>
    <script src="js/footer-component.js"></script>
    </script>
</body>
</html>