// Advanced Chemistry Engine for Virtual Labs
class ChemistryEngine {
    constructor() {
        this.constants = {
            R: 8.314,           // Gas constant (J/mol⋅K)
            Na: 6.022e23,       // Avogadro's number
            F: 96485,           // Faraday constant (C/mol)
            Kb: 1.381e-23,      // <PERSON><PERSON>mann constant (J/K)
            h: 6.626e-34,       // Planck constant (J⋅s)
            c: 299792458,       // Speed of light (m/s)
            Kw: 1e-14          // Water ionization constant at 25°C
        };
        
        this.experiments = {
            titration: new Titration(),
            calorimetry: new Calorimetry(),
            synthesis: new Synthesis(),
            kinetics: new ChemicalKinetics(),
            equilibrium: new ChemicalEquilibrium(),
            gasLaws: new GasLaws(),
            stoichiometry: new Stoichiometry(),
            redox: new RedoxReactions(),
            electrochemistry: new Electrochemistry(),
            spectrophotometry: new Spectrophotometry(),
            qualitative: new QualitativeAnalysis(),
            molecularGeometry: new MolecularGeometry(),
            acidsAndBases: new AcidsAndBases(),
            solutionPreparation: new SolutionPreparation(),
            safetyScenarios: new SafetyScenarios()
        };
        
        this.periodicTable = new PeriodicTable();
        this.molecularBuilder = new MolecularBuilder();
    }

    getExperiment(type) {
        return this.experiments[type];
    }
}

// Base Chemistry Experiment Class
class BaseChemistryExperiment {
    constructor() {
        this.data = [];
        this.isRunning = false;
        this.startTime = 0;
        this.parameters = {};
        this.safetyLevel = 'safe'; // safe, warning, danger
    }

    start(parameters) {
        this.parameters = { ...parameters };
        this.data = [];
        this.isRunning = true;
        this.startTime = Date.now();
    }

    stop() {
        this.isRunning = false;
    }

    reset() {
        this.stop();
        this.data = [];
        this.startTime = 0;
    }

    addDataPoint(dataPoint) {
        this.data.push({
            ...dataPoint,
            timestamp: Date.now() - this.startTime
        });
    }

    getData() {
        return this.data;
    }

    setSafetyLevel(level) {
        this.safetyLevel = level;
    }
}

// Titration Experiment
class Titration extends BaseChemistryExperiment {
    constructor() {
        super();
        this.analyte = null;
        this.titrant = null;
        this.indicator = null;
        this.volumeAdded = 0;
        this.equivalencePoints = [];
    }

    setupTitration(analyte, titrant, indicator = null) {
        this.analyte = analyte;
        this.titrant = titrant;
        this.indicator = indicator;
        this.volumeAdded = 0;
        this.calculateEquivalencePoints();
    }

    calculateEquivalencePoints() {
        // Calculate equivalence points based on analyte and titrant
        if (this.analyte && this.titrant) {
            const analyteType = this.getAcidBaseType(this.analyte);
            const titrantType = this.getAcidBaseType(this.titrant);
            
            if (analyteType && titrantType) {
                const equivalenceVolume = this.calculateEquivalenceVolume();
                this.equivalencePoints.push({
                    volume: equivalenceVolume,
                    ph: this.calculateEquivalencePH()
                });
            }
        }
    }

    getAcidBaseType(compound) {
        const strongAcids = ['HCl', 'HNO3', 'H2SO4', 'HBr', 'HI', 'HClO4'];
        const strongBases = ['NaOH', 'KOH', 'Ca(OH)2', 'Ba(OH)2'];
        const weakAcids = ['CH3COOH', 'HF', 'H2CO3', 'H3PO4'];
        const weakBases = ['NH3', 'CH3NH2'];
        
        if (strongAcids.includes(compound.formula)) return 'strong_acid';
        if (strongBases.includes(compound.formula)) return 'strong_base';
        if (weakAcids.includes(compound.formula)) return 'weak_acid';
        if (weakBases.includes(compound.formula)) return 'weak_base';
        return null;
    }

    calculateEquivalenceVolume() {
        // n_analyte = n_titrant at equivalence point
        const analyteConcentration = this.analyte.concentration;
        const analyteVolume = this.analyte.volume;
        const titrantConcentration = this.titrant.concentration;
        
        return (analyteConcentration * analyteVolume) / titrantConcentration;
    }

    calculateEquivalencePH() {
        const analyteType = this.getAcidBaseType(this.analyte);
        const titrantType = this.getAcidBaseType(this.titrant);
        
        if (analyteType === 'strong_acid' && titrantType === 'strong_base') {
            return 7.0; // Neutral
        } else if (analyteType === 'weak_acid' && titrantType === 'strong_base') {
            // pH > 7 due to hydrolysis of conjugate base
            return 8.5; // Approximate
        } else if (analyteType === 'strong_acid' && titrantType === 'weak_base') {
            // pH < 7 due to hydrolysis of conjugate acid
            return 5.5; // Approximate
        }
        
        return 7.0;
    }

    calculatePH(volumeAdded) {
        const totalVolume = this.analyte.volume + volumeAdded;
        const analyteType = this.getAcidBaseType(this.analyte);
        const titrantType = this.getAcidBaseType(this.titrant);
        
        // Strong acid - Strong base titration
        if (analyteType === 'strong_acid' && titrantType === 'strong_base') {
            return this.strongAcidStrongBasePH(volumeAdded, totalVolume);
        }
        
        // Add more titration types as needed
        return 7.0;
    }

    strongAcidStrongBasePH(volumeAdded, totalVolume) {
        const initialMolesAcid = this.analyte.concentration * this.analyte.volume / 1000;
        const molesBaseAdded = this.titrant.concentration * volumeAdded / 1000;
        
        if (molesBaseAdded < initialMolesAcid) {
            // Before equivalence point - excess acid
            const excessAcid = initialMolesAcid - molesBaseAdded;
            const concentration = excessAcid / (totalVolume / 1000);
            return -Math.log10(concentration);
        } else if (molesBaseAdded > initialMolesAcid) {
            // After equivalence point - excess base
            const excessBase = molesBaseAdded - initialMolesAcid;
            const concentration = excessBase / (totalVolume / 1000);
            const pOH = -Math.log10(concentration);
            return 14 - pOH;
        } else {
            // At equivalence point
            return 7.0;
        }
    }

    addTitrant(volume) {
        this.volumeAdded += volume;
        const ph = this.calculatePH(this.volumeAdded);
        
        this.addDataPoint({
            volumeAdded: this.volumeAdded,
            ph: ph,
            temperature: this.parameters.temperature || 25
        });
        
        return { ph, volumeAdded: this.volumeAdded };
    }
}

// Calorimetry Experiment
class Calorimetry extends BaseChemistryExperiment {
    constructor() {
        super();
        this.calorimeter = {
            heatCapacity: 100, // J/°C
            mass: 50,          // g
            temperature: 25    // °C
        };
    }

    calculateHeatOfReaction(reactants, products, temperature) {
        // Using standard enthalpies of formation
        const enthalpyFormation = {
            'H2O(l)': -285.8,   // kJ/mol
            'CO2(g)': -393.5,
            'CH4(g)': -74.8,
            'C2H5OH(l)': -277.7,
            'NaCl(s)': -411.2,
            'CaO(s)': -635.1,
            'Ca(OH)2(s)': -986.1
        };
        
        let deltaH = 0;
        
        // ΔH = Σ(ΔHf products) - Σ(ΔHf reactants)
        products.forEach(product => {
            const enthalpy = enthalpyFormation[product.formula] || 0;
            deltaH += product.moles * enthalpy;
        });
        
        reactants.forEach(reactant => {
            const enthalpy = enthalpyFormation[reactant.formula] || 0;
            deltaH -= reactant.moles * enthalpy;
        });
        
        return deltaH; // kJ
    }

    simulateTemperatureChange(deltaH, solutionMass, specificHeat = 4.18) {
        // q = mcΔT
        // ΔT = q / (mc)
        const heatReleased = Math.abs(deltaH * 1000); // Convert to J
        const totalMass = solutionMass + this.calorimeter.mass;
        const totalHeatCapacity = totalMass * specificHeat + this.calorimeter.heatCapacity;
        
        const temperatureChange = heatReleased / totalHeatCapacity;
        
        // If exothermic (negative ΔH), temperature increases
        return deltaH < 0 ? temperatureChange : -temperatureChange;
    }

    performCalorimetry(reactants, products, solutionMass) {
        const deltaH = this.calculateHeatOfReaction(reactants, products);
        const tempChange = this.simulateTemperatureChange(deltaH, solutionMass);
        const finalTemp = this.calorimeter.temperature + tempChange;
        
        this.addDataPoint({
            deltaH: deltaH,
            temperatureChange: tempChange,
            finalTemperature: finalTemp,
            heatReleased: Math.abs(deltaH * 1000)
        });
        
        return {
            deltaH,
            temperatureChange: tempChange,
            finalTemperature: finalTemp
        };
    }
}

// Chemical Kinetics
class ChemicalKinetics extends BaseChemistryExperiment {
    constructor() {
        super();
        this.reactionOrders = {};
        this.rateConstant = 0;
        this.activationEnergy = 0;
    }

    calculateReactionRate(concentrations, temperature, rateConstant, orders) {
        // Rate = k * [A]^m * [B]^n * ...
        let rate = rateConstant;
        
        Object.keys(concentrations).forEach(reactant => {
            const concentration = concentrations[reactant];
            const order = orders[reactant] || 1;
            rate *= Math.pow(concentration, order);
        });
        
        return rate;
    }

    calculateRateConstant(temperature, activationEnergy, preExponentialFactor) {
        // Arrhenius equation: k = A * exp(-Ea/RT)
        const R = 8.314; // J/mol⋅K
        const T = temperature + 273.15; // Convert to Kelvin
        
        return preExponentialFactor * Math.exp(-activationEnergy / (R * T));
    }

    simulateReaction(initialConcentrations, time, temperature) {
        const k = this.calculateRateConstant(temperature, this.activationEnergy, 1e12);
        const concentrations = { ...initialConcentrations };
        const timeStep = 0.1;
        const results = [];
        
        for (let t = 0; t <= time; t += timeStep) {
            const rate = this.calculateReactionRate(concentrations, temperature, k, this.reactionOrders);
            
            // Update concentrations (simplified first-order reaction)
            Object.keys(concentrations).forEach(reactant => {
                concentrations[reactant] -= rate * timeStep;
                if (concentrations[reactant] < 0) concentrations[reactant] = 0;
            });
            
            results.push({
                time: t,
                concentrations: { ...concentrations },
                rate: rate
            });
        }
        
        return results;
    }

    determineReactionOrder(concentrationData, rateData) {
        // Use method of initial rates
        const orders = {};
        
        // Simplified analysis - would need more sophisticated algorithm in practice
        Object.keys(concentrationData[0]).forEach(reactant => {
            // Calculate apparent order from concentration vs rate data
            const order = this.calculateOrder(concentrationData, rateData, reactant);
            orders[reactant] = order;
        });
        
        return orders;
    }

    calculateOrder(concData, rateData, reactant) {
        // Simple linear regression on log-log plot
        const logConc = concData.map(data => Math.log(data[reactant]));
        const logRate = rateData.map(rate => Math.log(rate));
        
        // Calculate slope (reaction order)
        const n = logConc.length;
        const sumX = logConc.reduce((sum, x) => sum + x, 0);
        const sumY = logRate.reduce((sum, y) => sum + y, 0);
        const sumXY = logConc.reduce((sum, x, i) => sum + x * logRate[i], 0);
        const sumX2 = logConc.reduce((sum, x) => sum + x * x, 0);
        
        const slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
        return Math.round(slope * 2) / 2; // Round to nearest 0.5
    }
}

// Chemical Equilibrium
class ChemicalEquilibrium extends BaseBaseChemistryExperiment {
    constructor() {
        super();
        this.equilibriumConstant = 1;
        this.temperature = 298; // K
    }

    calculateEquilibriumConstant(concentrations, stoichiometry) {
        // K = [products]^coeffs / [reactants]^coeffs
        let numerator = 1;
        let denominator = 1;
        
        Object.keys(stoichiometry.products).forEach(product => {
            const concentration = concentrations[product];
            const coefficient = stoichiometry.products[product];
            numerator *= Math.pow(concentration, coefficient);
        });
        
        Object.keys(stoichiometry.reactants).forEach(reactant => {
            const concentration = concentrations[reactant];
            const coefficient = stoichiometry.reactants[reactant];
            denominator *= Math.pow(concentration, coefficient);
        });
        
        return numerator / denominator;
    }

    applyLeChatelier(equilibrium, change) {
        // Le Chatelier's principle simulation
        const effects = {
            concentration: this.concentrationEffect(equilibrium, change),
            temperature: this.temperatureEffect(equilibrium, change),
            pressure: this.pressureEffect(equilibrium, change)
        };
        
        return effects;
    }

    concentrationEffect(equilibrium, change) {
        // If reactant concentration increases, equilibrium shifts right
        // If product concentration increases, equilibrium shifts left
        const { species, delta } = change;
        
        if (equilibrium.reactants.includes(species)) {
            return delta > 0 ? 'right' : 'left';
        } else if (equilibrium.products.includes(species)) {
            return delta > 0 ? 'left' : 'right';
        }
        
        return 'no change';
    }

    temperatureEffect(equilibrium, change) {
        // For exothermic reactions (ΔH < 0), increasing T shifts left
        // For endothermic reactions (ΔH > 0), increasing T shifts right
        const { deltaT } = change;
        const { deltaH } = equilibrium;
        
        if (deltaT > 0) {
            return deltaH > 0 ? 'right' : 'left';
        } else {
            return deltaH > 0 ? 'left' : 'right';
        }
    }

    pressureEffect(equilibrium, change) {
        // Increasing pressure favors side with fewer gas molecules
        const { deltaP } = change;
        const { gaseous } = equilibrium;
        
        const reactantGases = gaseous.reactants || 0;
        const productGases = gaseous.products || 0;
        
        if (deltaP > 0) {
            return reactantGases > productGases ? 'right' : 'left';
        } else {
            return reactantGases > productGases ? 'left' : 'right';
        }
    }
}

// Spectrophotometry
class Spectrophotometry extends BaseChemistryExperiment {
    constructor() {
        super();
        this.calibrationCurve = [];
        this.unknownSamples = [];
    }

    calculateAbsorbance(concentration, pathLength, molarExtinctivity) {
        // Beer's Law: A = εcl
        return molarExtinctivity * concentration * pathLength;
    }

    calculateTransmittance(absorbance) {
        // T = 10^(-A)
        return Math.pow(10, -absorbance);
    }

    calculateConcentration(absorbance, pathLength, molarExtinctivity) {
        // c = A / (εl)
        return absorbance / (molarExtinctivity * pathLength);
    }

    createCalibrationCurve(standards) {
        // Create calibration curve from standard solutions
        this.calibrationCurve = standards.map(standard => ({
            concentration: standard.concentration,
            absorbance: this.calculateAbsorbance(
                standard.concentration,
                standard.pathLength,
                standard.molarExtinctivity
            )
        }));
        
        return this.calibrationCurve;
    }

    linearRegression(xData, yData) {
        const n = xData.length;
        const sumX = xData.reduce((sum, x) => sum + x, 0);
        const sumY = yData.reduce((sum, y) => sum + y, 0);
        const sumXY = xData.reduce((sum, x, i) => sum + x * yData[i], 0);
        const sumX2 = xData.reduce((sum, x) => sum + x * x, 0);
        
        const slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
        const intercept = (sumY - slope * sumX) / n;
        
        // Calculate R²
        const yMean = sumY / n;
        const ssTotal = yData.reduce((sum, y) => sum + (y - yMean) ** 2, 0);
        const ssResidual = yData.reduce((sum, y, i) => {
            const predicted = slope * xData[i] + intercept;
            return sum + (y - predicted) ** 2;
        }, 0);
        const rSquared = 1 - (ssResidual / ssTotal);
        
        return { slope, intercept, rSquared };
    }

    determineUnknownConcentration(unknownAbsorbance) {
        if (this.calibrationCurve.length < 2) {
            throw new Error('Need at least 2 calibration points');
        }
        
        const concentrations = this.calibrationCurve.map(point => point.concentration);
        const absorbances = this.calibrationCurve.map(point => point.absorbance);
        
        const regression = this.linearRegression(concentrations, absorbances);
        
        // Calculate concentration from absorbance
        const concentration = (unknownAbsorbance - regression.intercept) / regression.slope;
        
        return {
            concentration,
            regression,
            confidence: regression.rSquared
        };
    }
}

// Electrochemistry
class Electrochemistry extends BaseChemistryExperiment {
    constructor() {
        super();
        this.standardPotentials = {
            'Li+/Li': -3.05,
            'K+/K': -2.92,
            'Ca2+/Ca': -2.87,
            'Na+/Na': -2.71,
            'Mg2+/Mg': -2.37,
            'Al3+/Al': -1.66,
            'Zn2+/Zn': -0.76,
            'Fe2+/Fe': -0.44,
            'Ni2+/Ni': -0.25,
            'Sn2+/Sn': -0.14,
            'Pb2+/Pb': -0.13,
            'H+/H2': 0.00,
            'Cu2+/Cu': 0.34,
            'I2/I-': 0.54,
            'Fe3+/Fe2+': 0.77,
            'Ag+/Ag': 0.80,
            'Br2/Br-': 1.07,
            'Cl2/Cl-': 1.36,
            'Au3+/Au': 1.50,
            'F2/F-': 2.87
        };
    }

    calculateCellPotential(cathode, anode) {
        const cathodePotential = this.standardPotentials[cathode];
        const anodePotential = this.standardPotentials[anode];
        
        if (cathodePotential === undefined || anodePotential === undefined) {
            throw new Error('Unknown electrode potential');
        }
        
        return cathodePotential - anodePotential;
    }

    nernstEquation(standardPotential, temperature, n, Q) {
        // E = E° - (RT/nF) * ln(Q)
        const R = 8.314; // J/mol⋅K
        const F = 96485; // C/mol
        const T = temperature + 273.15; // Convert to Kelvin
        
        return standardPotential - (R * T / (n * F)) * Math.log(Q);
    }

    calculateElectrolysisProducts(compound, current, time) {
        // Faraday's laws of electrolysis
        const F = 96485; // C/mol
        const charge = current * time; // Coulombs
        const moles = charge / F;
        
        // Products depend on the compound being electrolyzed
        const products = this.getElectrolysisProducts(compound);
        
        return {
            molesProduced: moles,
            products: products,
            charge: charge
        };
    }

    getElectrolysisProducts(compound) {
        const products = {
            'NaCl': { cathode: 'Na', anode: 'Cl2' },
            'H2O': { cathode: 'H2', anode: 'O2' },
            'CuSO4': { cathode: 'Cu', anode: 'O2' },
            'AgNO3': { cathode: 'Ag', anode: 'O2' }
        };
        
        return products[compound] || { cathode: 'Unknown', anode: 'Unknown' };
    }

    designGalvanicCell(oxidant, reductant) {
        const cathodePotential = this.standardPotentials[oxidant];
        const anodePotential = this.standardPotentials[reductant];
        
        if (cathodePotential > anodePotential) {
            return {
                cathode: oxidant,
                anode: reductant,
                cellPotential: cathodePotential - anodePotential,
                spontaneous: true
            };
        } else {
            return {
                cathode: reductant,
                anode: oxidant,
                cellPotential: anodePotential - cathodePotential,
                spontaneous: false
            };
        }
    }
}

// Molecular Geometry
class MolecularGeometry extends BaseChemistryExperiment {
    constructor() {
        super();
        this.vseprShapes = {
            2: { linear: 'linear' },
            3: { trigonal_planar: 'trigonal planar', bent: 'bent' },
            4: { tetrahedral: 'tetrahedral', trigonal_pyramidal: 'trigonal pyramidal', bent: 'bent' },
            5: { trigonal_bipyramidal: 'trigonal bipyramidal', seesaw: 'seesaw', T_shaped: 'T-shaped', linear: 'linear' },
            6: { octahedral: 'octahedral', square_pyramidal: 'square pyramidal', square_planar: 'square planar' }
        };
    }

    determineVSEPRShape(centralAtom, bondingPairs, lonePairs) {
        const totalPairs = bondingPairs + lonePairs;
        const electronGeometry = this.getElectronGeometry(totalPairs);
        const molecularGeometry = this.getMolecularGeometry(totalPairs, lonePairs);
        
        return {
            electronGeometry,
            molecularGeometry,
            bondAngle: this.getBondAngle(molecularGeometry, lonePairs)
        };
    }

    getElectronGeometry(totalPairs) {
        const geometries = {
            2: 'linear',
            3: 'trigonal planar',
            4: 'tetrahedral',
            5: 'trigonal bipyramidal',
            6: 'octahedral'
        };
        
        return geometries[totalPairs] || 'unknown';
    }

    getMolecularGeometry(totalPairs, lonePairs) {
        const bondingPairs = totalPairs - lonePairs;
        
        if (totalPairs === 4) {
            if (lonePairs === 0) return 'tetrahedral';
            if (lonePairs === 1) return 'trigonal pyramidal';
            if (lonePairs === 2) return 'bent';
        } else if (totalPairs === 3) {
            if (lonePairs === 0) return 'trigonal planar';
            if (lonePairs === 1) return 'bent';
        } else if (totalPairs === 2) {
            return 'linear';
        }
        
        return 'complex';
    }

    getBondAngle(geometry, lonePairs) {
        const angles = {
            'linear': 180,
            'trigonal planar': 120,
            'bent': lonePairs === 1 ? 119 : 104.5,
            'tetrahedral': 109.5,
            'trigonal pyramidal': 107,
            'octahedral': 90,
            'square planar': 90
        };
        
        return angles[geometry] || 'variable';
    }

    calculateFormalCharge(valenceElectrons, bondingElectrons, nonBondingElectrons) {
        // FC = V - N - B/2
        // V = valence electrons, N = non-bonding electrons, B = bonding electrons
        return valenceElectrons - nonBondingElectrons - (bondingElectrons / 2);
    }

    predictHybridization(bondingPairs, lonePairs) {
        const totalPairs = bondingPairs + lonePairs;
        
        const hybridizations = {
            2: 'sp',
            3: 'sp2',
            4: 'sp3',
            5: 'sp3d',
            6: 'sp3d2'
        };
        
        return hybridizations[totalPairs] || 'unknown';
    }
}

// Periodic Table Class
class PeriodicTable {
    constructor() {
        this.elements = {
            'H': { name: 'Hydrogen', atomicNumber: 1, atomicMass: 1.008, group: 1, period: 1 },
            'He': { name: 'Helium', atomicNumber: 2, atomicMass: 4.003, group: 18, period: 1 },
            'Li': { name: 'Lithium', atomicNumber: 3, atomicMass: 6.941, group: 1, period: 2 },
            'Be': { name: 'Beryllium', atomicNumber: 4, atomicMass: 9.012, group: 2, period: 2 },
            'B': { name: 'Boron', atomicNumber: 5, atomicMass: 10.81, group: 13, period: 2 },
            'C': { name: 'Carbon', atomicNumber: 6, atomicMass: 12.01, group: 14, period: 2 },
            'N': { name: 'Nitrogen', atomicNumber: 7, atomicMass: 14.01, group: 15, period: 2 },
            'O': { name: 'Oxygen', atomicNumber: 8, atomicMass: 16.00, group: 16, period: 2 },
            'F': { name: 'Fluorine', atomicNumber: 9, atomicMass: 19.00, group: 17, period: 2 },
            'Ne': { name: 'Neon', atomicNumber: 10, atomicMass: 20.18, group: 18, period: 2 },
            'Na': { name: 'Sodium', atomicNumber: 11, atomicMass: 22.99, group: 1, period: 3 },
            'Mg': { name: 'Magnesium', atomicNumber: 12, atomicMass: 24.31, group: 2, period: 3 },
            'Al': { name: 'Aluminum', atomicNumber: 13, atomicMass: 26.98, group: 13, period: 3 },
            'Si': { name: 'Silicon', atomicNumber: 14, atomicMass: 28.09, group: 14, period: 3 },
            'P': { name: 'Phosphorus', atomicNumber: 15, atomicMass: 30.97, group: 15, period: 3 },
            'S': { name: 'Sulfur', atomicNumber: 16, atomicMass: 32.07, group: 16, period: 3 },
            'Cl': { name: 'Chlorine', atomicNumber: 17, atomicMass: 35.45, group: 17, period: 3 },
            'Ar': { name: 'Argon', atomicNumber: 18, atomicMass: 39.95, group: 18, period: 3 },
            'K': { name: 'Potassium', atomicNumber: 19, atomicMass: 39.10, group: 1, period: 4 },
            'Ca': { name: 'Calcium', atomicNumber: 20, atomicMass: 40.08, group: 2, period: 4 }
            // Add more elements as needed
        };
    }

    getElement(symbol) {
        return this.elements[symbol];
    }

    calculateMolarMass(formula) {
        // Simple parser for molecular formulas like H2O, C6H12O6, etc.
        const elements = formula.match(/[A-Z][a-z]?\d*/g);
        let molarMass = 0;
        
        elements.forEach(element => {
            const match = element.match(/([A-Z][a-z]?)(\d*)/);
            const symbol = match[1];
            const count = parseInt(match[2]) || 1;
            
            const elementData = this.getElement(symbol);
            if (elementData) {
                molarMass += elementData.atomicMass * count;
            }
        });
        
        return molarMass;
    }

    getElementsByGroup(group) {
        return Object.keys(this.elements).filter(symbol => 
            this.elements[symbol].group === group
        );
    }

    getElementsByPeriod(period) {
        return Object.keys(this.elements).filter(symbol => 
            this.elements[symbol].period === period
        );
    }
}

// Safety Scenarios
class SafetyScenarios extends BaseChemistryExperiment {
    constructor() {
        super();
        this.scenarios = {
            fire: {
                name: 'حريق في المختبر',
                description: 'اشتعل حريق صغير في المختبر',
                correctActions: ['إخلاء المنطقة', 'استخدام طفاية الحريق', 'قطع مصادر الغاز'],
                incorrectActions: ['استخدام الماء على الحرائق الكيميائية', 'محاولة إطفاء النار باليد']
            },
            spill: {
                name: 'انسكاب حمض',
                description: 'انسكب حمض مركز على المنضدة',
                correctActions: ['ارتداء معدات الحماية', 'تحييد الحمض', 'تنظيف المنطقة'],
                incorrectActions: ['لمس الحمض مباشرة', 'استخدام ملابس عادية']
            },
            gas: {
                name: 'تسرب غاز',
                description: 'تسرب غاز سام في المختبر',
                correctActions: ['إخلاء المنطقة فوراً', 'تشغيل نظام التهوية', 'إبلاغ الطوارئ'],
                incorrectActions: ['محاولة إصلاح التسرب بنفسك', 'البقاء في المنطقة']
            }
        };
    }

    getScenario(scenarioType) {
        return this.scenarios[scenarioType];
    }

    evaluateResponse(scenarioType, selectedActions) {
        const scenario = this.scenarios[scenarioType];
        if (!scenario) return null;
        
        const correctCount = selectedActions.filter(action => 
            scenario.correctActions.includes(action)
        ).length;
        
        const incorrectCount = selectedActions.filter(action => 
            scenario.incorrectActions.includes(action)
        ).length;
        
        const score = (correctCount - incorrectCount) / scenario.correctActions.length;
        
        return {
            score: Math.max(0, score),
            correctActions: correctCount,
            incorrectActions: incorrectCount,
            feedback: this.generateFeedback(score)
        };
    }

    generateFeedback(score) {
        if (score >= 0.8) return 'ممتاز! تعاملت مع الحالة بشكل صحيح';
        if (score >= 0.6) return 'جيد، لكن يمكن تحسين استجابتك';
        if (score >= 0.4) return 'يحتاج إلى تحسين في إجراءات السلامة';
        return 'يجب مراجعة بروتوكولات السلامة';
    }
}

// Utility Classes
class ChemistryCalculator {
    static molesToGrams(moles, molarMass) {
        return moles * molarMass;
    }

    static gramsToMoles(grams, molarMass) {
        return grams / molarMass;
    }

    static molarity(moles, volumeLiters) {
        return moles / volumeLiters;
    }

    static dilution(c1, v1, c2, v2) {
        // c1v1 = c2v2
        if (!c2) return (c1 * v1) / v2;
        if (!v2) return (c1 * v1) / c2;
        if (!c1) return (c2 * v2) / v1;
        if (!v1) return (c2 * v2) / c1;
    }

    static idealGas(p, v, n, t) {
        const R = 0.08206; // L⋅atm/mol⋅K
        // PV = nRT
        if (!p) return (n * R * t) / v;
        if (!v) return (n * R * t) / p;
        if (!n) return (p * v) / (R * t);
        if (!t) return (p * v) / (n * R);
    }

    static phFromConcentration(concentration, isBase = false) {
        if (isBase) {
            const pOH = -Math.log10(concentration);
            return 14 - pOH;
        } else {
            return -Math.log10(concentration);
        }
    }

    static concentrationFromPH(ph, isBase = false) {
        if (isBase) {
            const pOH = 14 - ph;
            return Math.pow(10, -pOH);
        } else {
            return Math.pow(10, -ph);
        }
    }
}

// Export the chemistry engine
if (typeof window !== 'undefined') {
    window.ChemistryEngine = ChemistryEngine;
    window.ChemistryCalculator = ChemistryCalculator;
} else if (typeof module !== 'undefined' && module.exports) {
    module.exports = { ChemistryEngine, ChemistryCalculator };
}