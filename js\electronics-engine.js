// Electronics Engine for Virtual Electronics Lab
// Comprehensive circuit building, simulation, and analysis system

class CircuitBuilder {
    constructor() {
        this.components = new Map();
        this.wires = [];
        this.nodes = new Map();
        this.canvas = null;
        this.ctx = null;
        this.nextComponentId = 1;
        this.nextNodeId = 1;
        this.isPowered = false;
    }

    setCanvas(canvas) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.setupCanvas();
    }

    setupCanvas() {
        if (!this.ctx) return;
        
        this.ctx.fillStyle = '#f8f9fa';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        this.drawGrid();
    }

    drawGrid() {
        if (!this.ctx) return;
        
        const gridSize = 20;
        this.ctx.strokeStyle = 'rgba(0, 0, 0, 0.1)';
        this.ctx.lineWidth = 1;
        
        for (let x = 0; x <= this.canvas.width; x += gridSize) {
            this.ctx.beginPath();
            this.ctx.moveTo(x, 0);
            this.ctx.lineTo(x, this.canvas.height);
            this.ctx.stroke();
        }
        
        for (let y = 0; y <= this.canvas.height; y += gridSize) {
            this.ctx.beginPath();
            this.ctx.moveTo(0, y);
            this.ctx.lineTo(this.canvas.width, y);
            this.ctx.stroke();
        }
    }

    addComponent(type, properties) {
        const componentId = `${type}_${this.nextComponentId++}`;
        const component = new ElectronicComponent(componentId, type, properties);
        this.components.set(componentId, component);
        
        // Create nodes for component terminals
        component.terminals.forEach(terminal => {
            const nodeId = `node_${this.nextNodeId++}`;
            this.nodes.set(nodeId, {
                id: nodeId,
                x: terminal.x,
                y: terminal.y,
                connections: [componentId]
            });
            terminal.nodeId = nodeId;
        });
        
        this.redraw();
        return componentId;
    }

    addWire(wireData) {
        const wire = {
            id: `wire_${this.wires.length + 1}`,
            from: wireData.from,
            to: wireData.to,
            current: 0
        };
        
        this.wires.push(wire);
        this.connectNodes(wireData.from, wireData.to);
        this.redraw();
        return wire.id;
    }

    connectNodes(point1, point2) {
        // Find or create nodes at connection points
        const node1 = this.findOrCreateNode(point1);
        const node2 = this.findOrCreateNode(point2);
        
        // Merge nodes if they're at the same location
        if (node1.id !== node2.id && this.distance(node1, node2) < 10) {
            this.mergeNodes(node1, node2);
        }
    }

    findOrCreateNode(point) {
        // Find existing node at this location
        for (let [nodeId, node] of this.nodes) {
            if (this.distance(node, point) < 10) {
                return node;
            }
        }
        
        // Create new node
        const nodeId = `node_${this.nextNodeId++}`;
        const node = {
            id: nodeId,
            x: point.x,
            y: point.y,
            connections: []
        };
        this.nodes.set(nodeId, node);
        return node;
    }

    mergeNodes(node1, node2) {
        // Merge connections
        node1.connections = [...new Set([...node1.connections, ...node2.connections])];
        
        // Update component references
        this.components.forEach(component => {
            component.terminals.forEach(terminal => {
                if (terminal.nodeId === node2.id) {
                    terminal.nodeId = node1.id;
                }
            });
        });
        
        // Remove merged node
        this.nodes.delete(node2.id);
    }

    distance(point1, point2) {
        const dx = point1.x - point2.x;
        const dy = point1.y - point2.y;
        return Math.sqrt(dx * dx + dy * dy);
    }

    removeComponent(componentId) {
        const component = this.components.get(componentId);
        if (component) {
            // Remove associated nodes
            component.terminals.forEach(terminal => {
                if (terminal.nodeId) {
                    this.nodes.delete(terminal.nodeId);
                }
            });
            
            this.components.delete(componentId);
            this.redraw();
        }
    }

    clear() {
        this.components.clear();
        this.wires = [];
        this.nodes.clear();
        this.nextComponentId = 1;
        this.nextNodeId = 1;
        this.redraw();
    }

    setPower(powered) {
        this.isPowered = powered;
        this.redraw();
    }

    redraw() {
        if (!this.ctx) return;
        
        // Clear canvas
        this.ctx.fillStyle = '#f8f9fa';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        this.drawGrid();
        
        // Draw wires
        this.drawWires();
        
        // Draw components
        this.drawComponents();
        
        // Draw nodes
        this.drawNodes();
    }

    drawWires() {
        this.ctx.strokeStyle = '#2d3748';
        this.ctx.lineWidth = 2;
        
        this.wires.forEach(wire => {
            this.ctx.beginPath();
            this.ctx.moveTo(wire.from.x, wire.from.y);
            this.ctx.lineTo(wire.to.x, wire.to.y);
            this.ctx.stroke();
            
            // Draw current flow if powered
            if (this.isPowered && wire.current > 0) {
                this.drawCurrentFlow(wire);
            }
        });
    }

    drawCurrentFlow(wire) {
        const midX = (wire.from.x + wire.to.x) / 2;
        const midY = (wire.from.y + wire.to.y) / 2;
        
        this.ctx.fillStyle = '#e53e3e';
        this.ctx.beginPath();
        this.ctx.arc(midX, midY, 3, 0, 2 * Math.PI);
        this.ctx.fill();
        
        // Draw arrow indicating direction
        const angle = Math.atan2(wire.to.y - wire.from.y, wire.to.x - wire.from.x);
        this.drawArrow(midX, midY, angle);
    }

    drawArrow(x, y, angle) {
        const arrowSize = 8;
        this.ctx.fillStyle = '#e53e3e';
        this.ctx.save();
        this.ctx.translate(x, y);
        this.ctx.rotate(angle);
        
        this.ctx.beginPath();
        this.ctx.moveTo(0, 0);
        this.ctx.lineTo(-arrowSize, -arrowSize/2);
        this.ctx.lineTo(-arrowSize, arrowSize/2);
        this.ctx.closePath();
        this.ctx.fill();
        
        this.ctx.restore();
    }

    drawComponents() {
        this.components.forEach(component => {
            component.draw(this.ctx);
        });
    }

    drawNodes() {
        this.ctx.fillStyle = '#4299e1';
        
        this.nodes.forEach(node => {
            if (node.connections.length > 1) {
                this.ctx.beginPath();
                this.ctx.arc(node.x, node.y, 4, 0, 2 * Math.PI);
                this.ctx.fill();
            }
        });
    }

    getCircuitData() {
        return {
            components: Array.from(this.components.values()),
            wires: this.wires,
            nodes: Array.from(this.nodes.values())
        };
    }
}

// Electronic Component Class
class ElectronicComponent {
    constructor(id, type, properties) {
        this.id = id;
        this.type = type;
        this.properties = properties;
        this.terminals = [];
        this.voltage = 0;
        this.current = 0;
        
        this.initializeComponent();
    }

    initializeComponent() {
        switch (this.type) {
            case 'resistor':
                this.resistance = this.properties.resistance || 1000;
                this.terminals = [
                    { x: this.properties.x - 20, y: this.properties.y },
                    { x: this.properties.x + 20, y: this.properties.y }
                ];
                break;
                
            case 'capacitor':
                this.capacitance = this.properties.capacitance || 1e-6;
                this.terminals = [
                    { x: this.properties.x - 15, y: this.properties.y },
                    { x: this.properties.x + 15, y: this.properties.y }
                ];
                break;
                
            case 'voltage_source':
                this.voltage = this.properties.voltage || 5;
                this.terminals = [
                    { x: this.properties.x, y: this.properties.y - 15 },
                    { x: this.properties.x, y: this.properties.y + 15 }
                ];
                break;
                
            case 'diode':
                this.forwardVoltage = this.properties.forwardVoltage || 0.7;
                this.terminals = [
                    { x: this.properties.x - 15, y: this.properties.y },
                    { x: this.properties.x + 15, y: this.properties.y }
                ];
                break;
                
            case 'transistor':
                this.beta = this.properties.beta || 100;
                this.terminals = [
                    { x: this.properties.x - 10, y: this.properties.y - 15 }, // Base
                    { x: this.properties.x + 10, y: this.properties.y - 15 }, // Collector
                    { x: this.properties.x + 10, y: this.properties.y + 15 }  // Emitter
                ];
                break;
        }
    }

    draw(ctx) {
        ctx.save();
        ctx.translate(this.properties.x, this.properties.y);
        
        switch (this.type) {
            case 'resistor':
                this.drawResistor(ctx);
                break;
            case 'capacitor':
                this.drawCapacitor(ctx);
                break;
            case 'voltage_source':
                this.drawVoltageSource(ctx);
                break;
            case 'diode':
                this.drawDiode(ctx);
                break;
            case 'transistor':
                this.drawTransistor(ctx);
                break;
        }
        
        ctx.restore();
        
        // Draw terminals
        ctx.fillStyle = '#2d3748';
        this.terminals.forEach(terminal => {
            ctx.beginPath();
            ctx.arc(terminal.x, terminal.y, 2, 0, 2 * Math.PI);
            ctx.fill();
        });
        
        // Draw component label
        this.drawLabel(ctx);
    }

    drawResistor(ctx) {
        ctx.strokeStyle = '#2d3748';
        ctx.lineWidth = 2;
        
        // Draw zigzag pattern
        ctx.beginPath();
        ctx.moveTo(-20, 0);
        ctx.lineTo(-15, 0);
        ctx.lineTo(-10, -8);
        ctx.lineTo(-5, 8);
        ctx.lineTo(0, -8);
        ctx.lineTo(5, 8);
        ctx.lineTo(10, -8);
        ctx.lineTo(15, 0);
        ctx.lineTo(20, 0);
        ctx.stroke();
    }

    drawCapacitor(ctx) {
        ctx.strokeStyle = '#2d3748';
        ctx.lineWidth = 3;
        
        // Draw two parallel lines
        ctx.beginPath();
        ctx.moveTo(-5, -12);
        ctx.lineTo(-5, 12);
        ctx.moveTo(5, -12);
        ctx.lineTo(5, 12);
        ctx.stroke();
        
        // Draw connecting lines
        ctx.lineWidth = 1;
        ctx.beginPath();
        ctx.moveTo(-15, 0);
        ctx.lineTo(-5, 0);
        ctx.moveTo(5, 0);
        ctx.lineTo(15, 0);
        ctx.stroke();
    }

    drawVoltageSource(ctx) {
        ctx.strokeStyle = '#2d3748';
        ctx.lineWidth = 2;
        
        // Draw circle
        ctx.beginPath();
        ctx.arc(0, 0, 12, 0, 2 * Math.PI);
        ctx.stroke();
        
        // Draw + and - symbols
        ctx.font = '12px Arial';
        ctx.fillStyle = '#2d3748';
        ctx.textAlign = 'center';
        ctx.fillText('+', 0, -5);
        ctx.fillText('-', 0, 8);
        
        // Draw connecting lines
        ctx.beginPath();
        ctx.moveTo(0, -15);
        ctx.lineTo(0, -12);
        ctx.moveTo(0, 12);
        ctx.lineTo(0, 15);
        ctx.stroke();
    }

    drawDiode(ctx) {
        ctx.strokeStyle = '#2d3748';
        ctx.lineWidth = 2;
        ctx.fillStyle = '#2d3748';
        
        // Draw triangle (anode)
        ctx.beginPath();
        ctx.moveTo(-5, -8);
        ctx.lineTo(-5, 8);
        ctx.lineTo(5, 0);
        ctx.closePath();
        ctx.fill();
        
        // Draw cathode line
        ctx.beginPath();
        ctx.moveTo(5, -8);
        ctx.lineTo(5, 8);
        ctx.stroke();
        
        // Draw connecting lines
        ctx.lineWidth = 1;
        ctx.beginPath();
        ctx.moveTo(-15, 0);
        ctx.lineTo(-5, 0);
        ctx.moveTo(5, 0);
        ctx.lineTo(15, 0);
        ctx.stroke();
    }

    drawTransistor(ctx) {
        ctx.strokeStyle = '#2d3748';
        ctx.lineWidth = 2;
        
        // Draw base line
        ctx.beginPath();
        ctx.moveTo(-10, -15);
        ctx.lineTo(-10, 15);
        ctx.stroke();
        
        // Draw collector line
        ctx.beginPath();
        ctx.moveTo(-10, -8);
        ctx.lineTo(10, -15);
        ctx.stroke();
        
        // Draw emitter line
        ctx.beginPath();
        ctx.moveTo(-10, 8);
        ctx.lineTo(10, 15);
        ctx.stroke();
        
        // Draw arrow on emitter
        ctx.fillStyle = '#2d3748';
        ctx.beginPath();
        ctx.moveTo(5, 10);
        ctx.lineTo(8, 8);
        ctx.lineTo(8, 12);
        ctx.closePath();
        ctx.fill();
    }

    drawLabel(ctx) {
        ctx.fillStyle = '#4a5568';
        ctx.font = '10px Arial';
        ctx.textAlign = 'center';
        
        let label = this.type.toUpperCase();
        if (this.type === 'resistor') {
            label += `\n${this.resistance}Ω`;
        } else if (this.type === 'voltage_source') {
            label += `\n${this.voltage}V`;
        }
        
        ctx.fillText(label, this.properties.x, this.properties.y + 25);
    }
}

// Circuit Simulator Class
class CircuitSimulator {
    constructor() {
        this.results = {};
        this.tolerance = 1e-6;
        this.maxIterations = 100;
    }

    simulate(circuit) {
        const circuitData = circuit.getCircuitData();
        
        // Build circuit matrix
        const matrix = this.buildCircuitMatrix(circuitData);
        
        // Solve using nodal analysis
        const solution = this.solveMatrix(matrix);
        
        // Calculate component voltages and currents
        this.calculateComponentValues(circuitData, solution);
        
        return this.results;
    }

    buildCircuitMatrix(circuitData) {
        const nodes = circuitData.nodes;
        const components = circuitData.components;
        
        const nodeCount = nodes.length;
        const matrix = Array(nodeCount).fill().map(() => Array(nodeCount + 1).fill(0));
        
        // Apply Kirchhoff's Current Law at each node
        components.forEach(component => {
            this.addComponentToMatrix(component, matrix, nodes);
        });
        
        return matrix;
    }

    addComponentToMatrix(component, matrix, nodes) {
        switch (component.type) {
            case 'resistor':
                this.addResistorToMatrix(component, matrix, nodes);
                break;
            case 'voltage_source':
                this.addVoltageSourceToMatrix(component, matrix, nodes);
                break;
            case 'current_source':
                this.addCurrentSourceToMatrix(component, matrix, nodes);
                break;
        }
    }

    addResistorToMatrix(resistor, matrix, nodes) {
        const conductance = 1 / resistor.resistance;
        const node1Index = this.findNodeIndex(resistor.terminals[0], nodes);
        const node2Index = this.findNodeIndex(resistor.terminals[1], nodes);
        
        if (node1Index >= 0) {
            matrix[node1Index][node1Index] += conductance;
            if (node2Index >= 0) {
                matrix[node1Index][node2Index] -= conductance;
            }
        }
        
        if (node2Index >= 0) {
            matrix[node2Index][node2Index] += conductance;
            if (node1Index >= 0) {
                matrix[node2Index][node1Index] -= conductance;
            }
        }
    }

    addVoltageSourceToMatrix(voltageSource, matrix, nodes) {
        const node1Index = this.findNodeIndex(voltageSource.terminals[0], nodes);
        const node2Index = this.findNodeIndex(voltageSource.terminals[1], nodes);
        
        if (node1Index >= 0 && node2Index >= 0) {
            // Set voltage difference
            matrix[node1Index][matrix[0].length - 1] += voltageSource.voltage;
            matrix[node2Index][matrix[0].length - 1] -= voltageSource.voltage;
        }
    }

    findNodeIndex(terminal, nodes) {
        return nodes.findIndex(node => node.id === terminal.nodeId);
    }

    solveMatrix(matrix) {
        // Gaussian elimination
        const n = matrix.length;
        
        // Forward elimination
        for (let i = 0; i < n; i++) {
            // Find pivot
            let maxRow = i;
            for (let k = i + 1; k < n; k++) {
                if (Math.abs(matrix[k][i]) > Math.abs(matrix[maxRow][i])) {
                    maxRow = k;
                }
            }
            
            // Swap rows
            [matrix[i], matrix[maxRow]] = [matrix[maxRow], matrix[i]];
            
            // Make all rows below this one 0 in current column
            for (let k = i + 1; k < n; k++) {
                const factor = matrix[k][i] / matrix[i][i];
                for (let j = i; j <= n; j++) {
                    matrix[k][j] -= factor * matrix[i][j];
                }
            }
        }
        
        // Back substitution
        const solution = Array(n).fill(0);
        for (let i = n - 1; i >= 0; i--) {
            solution[i] = matrix[i][n];
            for (let j = i + 1; j < n; j++) {
                solution[i] -= matrix[i][j] * solution[j];
            }
            solution[i] /= matrix[i][i];
        }
        
        return solution;
    }

    calculateComponentValues(circuitData, solution) {
        this.results = {
            voltage: 0,
            current: 0,
            power: 0,
            resistance: 0
        };
        
        // Calculate values for each component
        circuitData.components.forEach(component => {
            const voltage = this.calculateComponentVoltage(component, solution, circuitData.nodes);
            const current = this.calculateComponentCurrent(component, voltage);
            
            component.voltage = voltage;
            component.current = current;
            
            // Update overall results
            if (component.type === 'voltage_source') {
                this.results.voltage = Math.abs(voltage);
                this.results.current = Math.abs(current);
                this.results.power = Math.abs(voltage * current);
            }
        });
        
        // Calculate total resistance
        if (this.results.current > 0) {
            this.results.resistance = this.results.voltage / this.results.current;
        }
    }

    calculateComponentVoltage(component, solution, nodes) {
        const node1Index = this.findNodeIndex(component.terminals[0], nodes);
        const node2Index = this.findNodeIndex(component.terminals[1], nodes);
        
        const v1 = node1Index >= 0 ? solution[node1Index] : 0;
        const v2 = node2Index >= 0 ? solution[node2Index] : 0;
        
        return v1 - v2;
    }

    calculateComponentCurrent(component, voltage) {
        switch (component.type) {
            case 'resistor':
                return voltage / component.resistance;
            case 'voltage_source':
                // Current through voltage source depends on circuit
                return 0; // Simplified
            default:
                return 0;
        }
    }

    getMeasurements() {
        return this.results;
    }
}

// Circuit Analyzer Class
class CircuitAnalyzer {
    constructor() {
        this.analysisResults = [];
    }

    analyze(circuit) {
        this.analysisResults = [];
        const circuitData = circuit.getCircuitData();
        
        // Perform various analyses
        this.analyzeTopology(circuitData);
        this.analyzeComponents(circuitData);
        this.checkCircuitRules(circuitData);
        
        return this.analysisResults;
    }

    analyzeTopology(circuitData) {
        const componentCount = circuitData.components.length;
        const nodeCount = circuitData.nodes.length;
        const wireCount = circuitData.wires.length;
        
        this.analysisResults.push(`Circuit Topology:`);
        this.analysisResults.push(`- Components: ${componentCount}`);
        this.analysisResults.push(`- Nodes: ${nodeCount}`);
        this.analysisResults.push(`- Wires: ${wireCount}`);
    }

    analyzeComponents(circuitData) {
        const componentTypes = {};
        
        circuitData.components.forEach(component => {
            componentTypes[component.type] = (componentTypes[component.type] || 0) + 1;
        });
        
        this.analysisResults.push(`Component Analysis:`);
        Object.entries(componentTypes).forEach(([type, count]) => {
            this.analysisResults.push(`- ${type}: ${count}`);
        });
    }

    checkCircuitRules(circuitData) {
        this.analysisResults.push(`Circuit Validation:`);
        
        // Check for floating components
        const floatingComponents = this.findFloatingComponents(circuitData);
        if (floatingComponents.length > 0) {
            this.analysisResults.push(`⚠️ Floating components detected: ${floatingComponents.length}`);
        } else {
            this.analysisResults.push(`✓ No floating components`);
        }
        
        // Check for short circuits
        const shortCircuits = this.findShortCircuits(circuitData);
        if (shortCircuits.length > 0) {
            this.analysisResults.push(`⚠️ Potential short circuits: ${shortCircuits.length}`);
        } else {
            this.analysisResults.push(`✓ No short circuits detected`);
        }
    }

    findFloatingComponents(circuitData) {
        // Simplified implementation
        return [];
    }

    findShortCircuits(circuitData) {
        // Simplified implementation
        return [];
    }

    calculateOhmsLaw(circuit) {
        const results = [`Ohm's Law Analysis (V = I × R):`];
        const circuitData = circuit.getCircuitData();
        
        circuitData.components.forEach(component => {
            if (component.type === 'resistor') {
                const voltage = component.voltage || 0;
                const current = component.current || 0;
                const resistance = component.resistance;
                
                results.push(`${component.id}:`);
                results.push(`  V = ${voltage.toFixed(3)}V`);
                results.push(`  I = ${current.toFixed(6)}A`);
                results.push(`  R = ${resistance}Ω`);
                results.push(`  P = ${(voltage * current).toFixed(6)}W`);
            }
        });
        
        return results;
    }

    applyKirchhoffLaws(circuit) {
        const results = [`Kirchhoff's Laws Analysis:`];
        const circuitData = circuit.getCircuitData();
        
        results.push(`KCL (Current Law): ΣI = 0 at each node`);
        results.push(`KVL (Voltage Law): ΣV = 0 around each loop`);
        
        // Simplified analysis
        circuitData.nodes.forEach((node, index) => {
            results.push(`Node ${index + 1}: Current sum ≈ 0`);
        });
        
        return results;
    }

    calculatePower(circuit) {
        const results = [`Power Analysis (P = V × I):`];
        const circuitData = circuit.getCircuitData();
        
        let totalPower = 0;
        
        circuitData.components.forEach(component => {
            const voltage = component.voltage || 0;
            const current = component.current || 0;
            const power = voltage * current;
            
            results.push(`${component.id}: ${power.toFixed(6)}W`);
            totalPower += Math.abs(power);
        });
        
        results.push(`Total Power: ${totalPower.toFixed(6)}W`);
        
        return results;
    }

    frequencyAnalysis(circuit) {
        const results = [`Frequency Analysis:`];
        const circuitData = circuit.getCircuitData();
        
        // Check for reactive components
        const hasCapacitors = circuitData.components.some(c => c.type === 'capacitor');
        const hasInductors = circuitData.components.some(c => c.type === 'inductor');
        
        if (hasCapacitors || hasInductors) {
            results.push(`Reactive components detected`);
            results.push(`Frequency response analysis available`);
            
            if (hasCapacitors && hasInductors) {
                results.push(`LC circuit - resonance possible`);
            }
        } else {
            results.push(`No reactive components - DC analysis only`);
        }
        
        return results;
    }
}

// Enhanced Virtual Breadboard Class
class VirtualBreadboard {
    constructor(canvasId) {
        this.canvas = document.getElementById(canvasId);
        this.ctx = this.canvas.getContext('2d');
        this.components = new Map();
        this.connections = [];
        this.holes = [];
        this.currentFlow = [];
        this.isSimulating = false;

        this.initializeBreadboard();
        this.setupEventListeners();
    }

    initializeBreadboard() {
        this.createHoleGrid();
        this.drawBreadboard();
    }

    createHoleGrid() {
        const rows = 30;
        const cols = 60;
        const holeSpacing = 10;
        const startX = 50;
        const startY = 50;

        for (let row = 0; row < rows; row++) {
            for (let col = 0; col < cols; col++) {
                this.holes.push({
                    id: `${row}-${col}`,
                    x: startX + col * holeSpacing,
                    y: startY + row * holeSpacing,
                    connected: false,
                    component: null
                });
            }
        }
    }

    drawBreadboard() {
        // Clear canvas
        this.ctx.fillStyle = '#2d5016';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // Draw power rails
        this.drawPowerRails();

        // Draw holes
        this.drawHoles();

        // Draw components
        this.drawComponents();

        // Draw connections
        this.drawConnections();

        // Draw current flow if simulating
        if (this.isSimulating) {
            this.drawCurrentFlow();
        }
    }

    drawPowerRails() {
        // Positive rail
        this.ctx.fillStyle = '#e53e3e';
        this.ctx.fillRect(10, 20, this.canvas.width - 20, 8);

        // Negative rail
        this.ctx.fillStyle = '#4299e1';
        this.ctx.fillRect(10, this.canvas.height - 28, this.canvas.width - 20, 8);

        // Rail labels
        this.ctx.fillStyle = 'white';
        this.ctx.font = '12px Arial';
        this.ctx.fillText('+', 15, 27);
        this.ctx.fillText('-', 15, this.canvas.height - 22);
    }

    drawHoles() {
        this.holes.forEach(hole => {
            this.ctx.fillStyle = hole.connected ? '#4a5568' : '#1a202c';
            this.ctx.beginPath();
            this.ctx.arc(hole.x, hole.y, 3, 0, 2 * Math.PI);
            this.ctx.fill();
        });
    }

    drawComponents() {
        this.components.forEach(component => {
            component.draw(this.ctx);
        });
    }

    drawConnections() {
        this.ctx.strokeStyle = '#ffd700';
        this.ctx.lineWidth = 2;

        this.connections.forEach(connection => {
            this.ctx.beginPath();
            this.ctx.moveTo(connection.from.x, connection.from.y);
            this.ctx.lineTo(connection.to.x, connection.to.y);
            this.ctx.stroke();
        });
    }

    drawCurrentFlow() {
        this.currentFlow.forEach(flow => {
            this.ctx.fillStyle = '#ff6b6b';
            this.ctx.beginPath();
            this.ctx.arc(flow.x, flow.y, 2, 0, 2 * Math.PI);
            this.ctx.fill();
        });
    }

    setupEventListeners() {
        this.canvas.addEventListener('click', (e) => {
            const rect = this.canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            this.handleCanvasClick(x, y);
        });

        this.canvas.addEventListener('dragover', (e) => {
            e.preventDefault();
        });

        this.canvas.addEventListener('drop', (e) => {
            e.preventDefault();
            const componentType = e.dataTransfer.getData('text/plain');
            const rect = this.canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            this.placeComponent(componentType, x, y);
        });
    }

    handleCanvasClick(x, y) {
        const hole = this.findNearestHole(x, y);
        if (hole) {
            this.selectHole(hole);
        }
    }

    findNearestHole(x, y) {
        let nearest = null;
        let minDistance = Infinity;

        this.holes.forEach(hole => {
            const distance = Math.sqrt((x - hole.x) ** 2 + (y - hole.y) ** 2);
            if (distance < 10 && distance < minDistance) {
                minDistance = distance;
                nearest = hole;
            }
        });

        return nearest;
    }

    placeComponent(componentType, x, y) {
        const hole = this.findNearestHole(x, y);
        if (hole) {
            const component = new BreadboardComponent(componentType, hole);
            this.components.set(component.id, component);
            hole.component = component.id;
            hole.connected = true;
            this.drawBreadboard();
        }
    }

    startSimulation() {
        this.isSimulating = true;
        this.calculateCurrentFlow();
        this.animateCurrentFlow();
    }

    stopSimulation() {
        this.isSimulating = false;
        this.currentFlow = [];
        this.drawBreadboard();
    }

    calculateCurrentFlow() {
        // Simplified current flow calculation
        this.currentFlow = [];

        this.connections.forEach(connection => {
            const steps = 10;
            for (let i = 0; i <= steps; i++) {
                const t = i / steps;
                this.currentFlow.push({
                    x: connection.from.x + t * (connection.to.x - connection.from.x),
                    y: connection.from.y + t * (connection.to.y - connection.from.y),
                    speed: 1 + Math.random()
                });
            }
        });
    }

    animateCurrentFlow() {
        if (!this.isSimulating) return;

        this.currentFlow.forEach(flow => {
            flow.x += flow.speed;
            if (flow.x > this.canvas.width) {
                flow.x = 0;
            }
        });

        this.drawBreadboard();
        requestAnimationFrame(() => this.animateCurrentFlow());
    }
}

// Breadboard Component Class
class BreadboardComponent {
    constructor(type, hole) {
        this.id = `${type}_${Date.now()}`;
        this.type = type;
        this.hole = hole;
        this.x = hole.x;
        this.y = hole.y;
        this.properties = this.getDefaultProperties(type);
    }

    getDefaultProperties(type) {
        const defaults = {
            resistor: { value: 1000, unit: 'Ω', color: '#8b4513' },
            capacitor: { value: 1, unit: 'µF', color: '#4169e1' },
            led: { value: 2.1, unit: 'V', color: '#ff0000' },
            diode: { value: 0.7, unit: 'V', color: '#2f4f4f' },
            transistor: { value: 100, unit: 'β', color: '#000000' }
        };
        return defaults[type] || { value: 0, unit: '', color: '#666666' };
    }

    draw(ctx) {
        ctx.save();
        ctx.translate(this.x, this.y);

        switch (this.type) {
            case 'resistor':
                this.drawResistor(ctx);
                break;
            case 'capacitor':
                this.drawCapacitor(ctx);
                break;
            case 'led':
                this.drawLED(ctx);
                break;
            case 'diode':
                this.drawDiode(ctx);
                break;
            case 'transistor':
                this.drawTransistor(ctx);
                break;
        }

        ctx.restore();
    }

    drawResistor(ctx) {
        ctx.strokeStyle = this.properties.color;
        ctx.lineWidth = 3;
        ctx.beginPath();
        ctx.rect(-15, -3, 30, 6);
        ctx.stroke();

        // Color bands for resistance value
        this.drawResistorBands(ctx);
    }

    drawResistorBands(ctx) {
        const bands = this.getResistorBands(this.properties.value);
        bands.forEach((color, index) => {
            ctx.fillStyle = color;
            ctx.fillRect(-12 + index * 6, -3, 2, 6);
        });
    }

    getResistorBands(value) {
        // Simplified resistor color code
        const colors = ['#000', '#8b4513', '#ff0000', '#ffa500'];
        return colors.slice(0, 4);
    }

    drawCapacitor(ctx) {
        ctx.strokeStyle = this.properties.color;
        ctx.lineWidth = 2;

        // Two parallel lines
        ctx.beginPath();
        ctx.moveTo(-3, -8);
        ctx.lineTo(-3, 8);
        ctx.moveTo(3, -8);
        ctx.lineTo(3, 8);
        ctx.stroke();
    }

    drawLED(ctx) {
        ctx.fillStyle = this.properties.color;
        ctx.beginPath();
        ctx.arc(0, 0, 5, 0, 2 * Math.PI);
        ctx.fill();

        // LED symbol
        ctx.strokeStyle = '#fff';
        ctx.lineWidth = 1;
        ctx.beginPath();
        ctx.moveTo(-3, -3);
        ctx.lineTo(3, 0);
        ctx.lineTo(-3, 3);
        ctx.closePath();
        ctx.stroke();
    }

    drawDiode(ctx) {
        ctx.fillStyle = this.properties.color;
        ctx.beginPath();
        ctx.moveTo(-5, -5);
        ctx.lineTo(5, 0);
        ctx.lineTo(-5, 5);
        ctx.closePath();
        ctx.fill();

        ctx.strokeStyle = '#fff';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.moveTo(5, -5);
        ctx.lineTo(5, 5);
        ctx.stroke();
    }

    drawTransistor(ctx) {
        ctx.strokeStyle = this.properties.color;
        ctx.lineWidth = 2;

        // Base
        ctx.beginPath();
        ctx.moveTo(-8, -8);
        ctx.lineTo(-8, 8);
        ctx.stroke();

        // Collector
        ctx.beginPath();
        ctx.moveTo(-8, -4);
        ctx.lineTo(8, -8);
        ctx.stroke();

        // Emitter
        ctx.beginPath();
        ctx.moveTo(-8, 4);
        ctx.lineTo(8, 8);
        ctx.stroke();
    }
}

// Circuit Diagram Generator
class CircuitDiagramGenerator {
    constructor() {
        this.components = [];
        this.connections = [];
        this.canvas = null;
        this.ctx = null;
    }

    setCanvas(canvas) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
    }

    generateFromBreadboard(breadboard) {
        this.components = Array.from(breadboard.components.values());
        this.connections = breadboard.connections;
        this.drawSchematic();
    }

    drawSchematic() {
        if (!this.ctx) return;

        // Clear canvas
        this.ctx.fillStyle = '#ffffff';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // Draw grid
        this.drawGrid();

        // Arrange components in schematic layout
        this.arrangeComponents();

        // Draw connections
        this.drawSchematicConnections();

        // Add labels and values
        this.addLabels();
    }

    drawGrid() {
        this.ctx.strokeStyle = '#e0e0e0';
        this.ctx.lineWidth = 1;

        const gridSize = 20;
        for (let x = 0; x <= this.canvas.width; x += gridSize) {
            this.ctx.beginPath();
            this.ctx.moveTo(x, 0);
            this.ctx.lineTo(x, this.canvas.height);
            this.ctx.stroke();
        }

        for (let y = 0; y <= this.canvas.height; y += gridSize) {
            this.ctx.beginPath();
            this.ctx.moveTo(0, y);
            this.ctx.lineTo(this.canvas.width, y);
            this.ctx.stroke();
        }
    }

    arrangeComponents() {
        // Simple linear arrangement for now
        const spacing = 100;
        const startX = 100;
        const y = this.canvas.height / 2;

        this.components.forEach((component, index) => {
            component.schematicX = startX + index * spacing;
            component.schematicY = y;
        });
    }

    drawSchematicConnections() {
        this.ctx.strokeStyle = '#000000';
        this.ctx.lineWidth = 2;

        this.connections.forEach(connection => {
            this.ctx.beginPath();
            this.ctx.moveTo(connection.from.schematicX || connection.from.x,
                           connection.from.schematicY || connection.from.y);
            this.ctx.lineTo(connection.to.schematicX || connection.to.x,
                           connection.to.schematicY || connection.to.y);
            this.ctx.stroke();
        });
    }

    addLabels() {
        this.ctx.fillStyle = '#000000';
        this.ctx.font = '12px Arial';
        this.ctx.textAlign = 'center';

        this.components.forEach(component => {
            const label = `${component.type.toUpperCase()}\n${component.properties.value}${component.properties.unit}`;
            this.ctx.fillText(label, component.schematicX, component.schematicY + 25);
        });
    }

    exportSVG() {
        // Generate SVG representation
        let svg = `<svg width="${this.canvas.width}" height="${this.canvas.height}" xmlns="http://www.w3.org/2000/svg">`;

        // Add grid
        svg += '<defs><pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">';
        svg += '<path d="M 20 0 L 0 0 0 20" fill="none" stroke="#e0e0e0" stroke-width="1"/>';
        svg += '</pattern></defs>';
        svg += '<rect width="100%" height="100%" fill="url(#grid)" />';

        // Add components and connections
        this.components.forEach(component => {
            svg += this.componentToSVG(component);
        });

        this.connections.forEach(connection => {
            svg += this.connectionToSVG(connection);
        });

        svg += '</svg>';
        return svg;
    }

    componentToSVG(component) {
        const x = component.schematicX;
        const y = component.schematicY;

        switch (component.type) {
            case 'resistor':
                return `<rect x="${x-15}" y="${y-3}" width="30" height="6" fill="none" stroke="${component.properties.color}" stroke-width="2"/>`;
            case 'capacitor':
                return `<g><line x1="${x-3}" y1="${y-8}" x2="${x-3}" y2="${y+8}" stroke="${component.properties.color}" stroke-width="2"/>
                        <line x1="${x+3}" y1="${y-8}" x2="${x+3}" y2="${y+8}" stroke="${component.properties.color}" stroke-width="2"/></g>`;
            default:
                return `<circle cx="${x}" cy="${y}" r="5" fill="${component.properties.color}"/>`;
        }
    }

    connectionToSVG(connection) {
        return `<line x1="${connection.from.schematicX}" y1="${connection.from.schematicY}"
                      x2="${connection.to.schematicX}" y2="${connection.to.schematicY}"
                      stroke="#000000" stroke-width="2"/>`;
    }
}

// Global instances
let circuitBuilder, circuitSimulator, circuitAnalyzer, virtualBreadboard, diagramGenerator;

document.addEventListener('DOMContentLoaded', function() {
    circuitBuilder = new CircuitBuilder();
    circuitSimulator = new CircuitSimulator();
    circuitAnalyzer = new CircuitAnalyzer();

    // Initialize canvas if available
    const canvas = document.getElementById('circuit-canvas');
    if (canvas) {
        circuitBuilder.setCanvas(canvas);
        virtualBreadboard = new VirtualBreadboard('circuit-canvas');
    }

    // Initialize diagram generator
    const diagramCanvas = document.getElementById('analysis-canvas');
    if (diagramCanvas) {
        diagramGenerator = new CircuitDiagramGenerator();
        diagramGenerator.setCanvas(diagramCanvas);
    }
});
