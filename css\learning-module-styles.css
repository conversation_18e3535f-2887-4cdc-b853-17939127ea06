/* Enhanced Learning Module Styles */

/* Arabic Typography Enhancements */
@import url('https://fonts.googleapis.com/css2?family=Almarai:wght@300;400;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;600;700;900&display=swap');

:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    --error-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    
    --shadow-light: 0 4px 15px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 8px 25px rgba(0, 0, 0, 0.15);
    --shadow-heavy: 0 15px 35px rgba(0, 0, 0, 0.2);
    
    --border-radius: 15px;
    --border-radius-small: 8px;
    --border-radius-large: 20px;
    
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.2s ease;
    --transition-slow: all 0.5s ease;
}

/* Enhanced Typography */
.arabic-title {
    font-family: 'Almarai', 'Tajawal', Arial, sans-serif;
    font-weight: 700;
    line-height: 1.2;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.arabic-text {
    font-family: 'Cairo', 'Tajawal', Arial, sans-serif;
    font-weight: 400;
    line-height: 1.8;
    letter-spacing: 0.3px;
}

.arabic-heading {
    font-family: 'Almarai', 'Tajawal', Arial, sans-serif;
    font-weight: 600;
    line-height: 1.4;
}

/* Enhanced Slide Transitions */
.slide-enter {
    animation: slideEnter 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.slide-exit {
    animation: slideExit 0.4s cubic-bezier(0.55, 0.06, 0.68, 0.19);
}

@keyframes slideEnter {
    0% {
        opacity: 0;
        transform: translateX(100px) scale(0.95);
    }
    100% {
        opacity: 1;
        transform: translateX(0) scale(1);
    }
}

@keyframes slideExit {
    0% {
        opacity: 1;
        transform: translateX(0) scale(1);
    }
    100% {
        opacity: 0;
        transform: translateX(-100px) scale(0.95);
    }
}

/* Enhanced Interactive Elements */
.interactive-element {
    position: relative;
    overflow: hidden;
    transition: var(--transition);
}

.interactive-element::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition);
}

.interactive-element:hover::before {
    left: 100%;
}

/* Enhanced Circuit Elements */
.circuit-element-advanced {
    position: relative;
    background: var(--primary-gradient);
    border-radius: 50%;
    box-shadow: var(--shadow-medium);
    transition: var(--transition);
    cursor: pointer;
}

.circuit-element-advanced::before {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    background: var(--primary-gradient);
    border-radius: 50%;
    opacity: 0;
    transition: var(--transition);
    z-index: -1;
}

.circuit-element-advanced:hover::before {
    opacity: 0.3;
    animation: ripple 0.6s ease-out;
}

@keyframes ripple {
    to {
        transform: scale(1.2);
        opacity: 0;
    }
}

/* Enhanced Cards with Glassmorphism */
.glass-card {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    transition: var(--transition);
}

.glass-card:hover {
    background: rgba(255, 255, 255, 0.35);
    transform: translateY(-8px);
    box-shadow: var(--shadow-heavy);
}

/* Enhanced Progress Indicators */
.progress-ring {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: conic-gradient(var(--primary-gradient) 0deg, #e2e8f0 0deg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: #2d3748;
    position: relative;
}

.progress-ring::before {
    content: '';
    position: absolute;
    top: 3px;
    left: 3px;
    right: 3px;
    bottom: 3px;
    background: white;
    border-radius: 50%;
}

.progress-ring span {
    position: relative;
    z-index: 1;
}

/* Enhanced Tooltips */
.tooltip-advanced {
    position: relative;
    display: inline-block;
}

.tooltip-advanced::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 8px 12px;
    border-radius: var(--border-radius-small);
    font-size: 0.8rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    z-index: 1000;
}

.tooltip-advanced::after {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.9);
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.tooltip-advanced:hover::before,
.tooltip-advanced:hover::after {
    opacity: 1;
    visibility: visible;
}

/* Enhanced Animation Classes */
.animate-float {
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

.animate-bounce {
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translateY(0);
    }
    40%, 43% {
        transform: translateY(-15px);
    }
    70% {
        transform: translateY(-7px);
    }
    90% {
        transform: translateY(-3px);
    }
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

.animate-spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Enhanced Loading States */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Enhanced Form Elements */
.form-group-enhanced {
    position: relative;
    margin-bottom: 20px;
}

.form-input-enhanced {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e2e8f0;
    border-radius: var(--border-radius-small);
    font-size: 1rem;
    transition: var(--transition);
    background: rgba(255, 255, 255, 0.9);
}

.form-input-enhanced:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    background: white;
}

.form-label-enhanced {
    position: absolute;
    top: 12px;
    right: 16px;
    background: white;
    padding: 0 4px;
    color: #4a5568;
    font-size: 1rem;
    transition: var(--transition);
    pointer-events: none;
}

.form-input-enhanced:focus + .form-label-enhanced,
.form-input-enhanced:not(:placeholder-shown) + .form-label-enhanced {
    top: -8px;
    font-size: 0.8rem;
    color: #667eea;
}

/* Enhanced Button Styles */
.btn-enhanced {
    background: var(--primary-gradient);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: var(--border-radius-small);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: var(--shadow-light);
    position: relative;
    overflow: hidden;
}

.btn-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition);
}

.btn-enhanced:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.btn-enhanced:hover::before {
    left: 100%;
}

.btn-enhanced:active {
    transform: translateY(0);
}

/* Enhanced Data Visualization */
.chart-container {
    position: relative;
    width: 100%;
    height: 300px;
    background: white;
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: var(--shadow-light);
}

.bar-chart {
    display: flex;
    align-items: end;
    height: 100%;
    gap: 10px;
}

.bar {
    flex: 1;
    background: var(--primary-gradient);
    border-radius: 4px 4px 0 0;
    transition: var(--transition);
    position: relative;
}

.bar:hover {
    opacity: 0.8;
    transform: scaleY(1.05);
}

.bar::before {
    content: attr(data-value);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    opacity: 0;
    transition: var(--transition);
}

.bar:hover::before {
    opacity: 1;
}

/* Enhanced Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: white;
    border-radius: var(--border-radius);
    padding: 30px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: var(--shadow-heavy);
    transform: scale(0.9);
    transition: var(--transition);
}

.modal-overlay.active .modal-content {
    transform: scale(1);
}

/* Enhanced Dark Mode */
@media (prefers-color-scheme: dark) {
    :root {
        --text-primary: #e2e8f0;
        --text-secondary: #a0aec0;
        --bg-primary: #1a202c;
        --bg-secondary: #2d3748;
        --border-color: #4a5568;
    }
    
    .slide {
        background: rgba(45, 55, 72, 0.95);
        color: var(--text-primary);
    }
    
    .concept-card {
        background: linear-gradient(135deg, #2d3748, #4a5568);
        border-color: var(--border-color);
    }
    
    .interactive-diagram {
        background: var(--bg-secondary);
        border-color: var(--border-color);
    }
}

/* Enhanced Print Styles */
@media print {
    .navigation,
    .slide-counter,
    .progress-bar {
        display: none !important;
    }
    
    .slide {
        page-break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ccc;
    }
    
    .slide-title {
        color: #000 !important;
    }
    
    .btn-enhanced {
        background: #fff !important;
        color: #000 !important;
        border: 1px solid #000 !important;
    }
}

/* Enhanced Accessibility */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.focus-visible:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* Enhanced Mobile Optimizations */
@media (max-width: 768px) {
    .slide {
        padding: 20px;
        height: 95vh;
    }
    
    .slide-title {
        font-size: 1.8rem;
    }
    
    .concept-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .real-world-section {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .navigation {
        position: fixed;
        bottom: 10px;
        left: 10px;
        right: 10px;
        justify-content: space-between;
    }
    
    .interactive-diagram {
        padding: 20px;
    }
    
    .diagram-container {
        height: 250px;
    }
}

/* Enhanced High-DPI Display Support */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .slide {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
    
    .circuit-element {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}