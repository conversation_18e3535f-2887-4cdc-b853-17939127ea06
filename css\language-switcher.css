/* Language Switcher Styling */

.language-switcher {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    display: flex;
    gap: 5px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 25px;
    padding: 5px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.lang-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    border: none;
    background: transparent;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    font-weight: 500;
    color: #4a5568;
    min-width: 70px;
    justify-content: center;
}

.lang-btn:hover {
    background: rgba(66, 153, 225, 0.1);
    color: #3182ce;
    transform: translateY(-1px);
}

.lang-btn.active {
    background: linear-gradient(145deg, #4299e1, #3182ce);
    color: white;
    box-shadow: 0 2px 8px rgba(66, 153, 225, 0.3);
}

.lang-btn.active:hover {
    background: linear-gradient(145deg, #3182ce, #2c5282);
}

.flag {
    font-size: 1.1rem;
    line-height: 1;
}

.lang-text {
    font-size: 0.85rem;
    font-weight: 600;
    white-space: nowrap;
}

/* Responsive Language Switcher */
@media (max-width: 768px) {
    .language-switcher {
        top: 10px;
        right: 10px;
        padding: 3px;
    }
    
    .lang-btn {
        padding: 6px 8px;
        min-width: 50px;
        gap: 4px;
    }
    
    .lang-text {
        font-size: 0.75rem;
    }
    
    .flag {
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .language-switcher {
        position: relative;
        top: auto;
        right: auto;
        margin: 10px auto;
        width: fit-content;
    }
    
    .lang-btn {
        flex-direction: column;
        gap: 2px;
        padding: 8px 6px;
        min-width: 45px;
    }
    
    .lang-text {
        font-size: 0.7rem;
    }
}

/* Integration with Headers */
.lab-header .language-switcher,
.showcase-header .language-switcher {
    position: relative;
    top: auto;
    right: auto;
    margin: 0;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(5px);
}

.lab-header .lang-btn,
.showcase-header .lang-btn {
    color: rgba(255, 255, 255, 0.8);
}

.lab-header .lang-btn:hover,
.showcase-header .lang-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.lab-header .lang-btn.active,
.showcase-header .lang-btn.active {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

/* Animation Effects */
.lang-btn {
    position: relative;
    overflow: hidden;
}

.lang-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.lang-btn:hover::before {
    left: 100%;
}

.lang-btn.active::before {
    display: none;
}

/* Language Transition Effects */
.lang-transition {
    transition: all 0.3s ease;
}

.lang-transition.changing {
    opacity: 0.7;
    transform: scale(0.98);
}

/* Accessibility */
.lang-btn:focus {
    outline: 2px solid #4299e1;
    outline-offset: 2px;
}

.lang-btn:focus:not(:focus-visible) {
    outline: none;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .language-switcher {
        background: white;
        border: 2px solid #000;
    }
    
    .lang-btn {
        color: #000;
        border: 1px solid transparent;
    }
    
    .lang-btn:hover {
        border-color: #4299e1;
        background: #f0f8ff;
    }
    
    .lang-btn.active {
        background: #4299e1;
        color: white;
        border-color: #3182ce;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .lang-btn,
    .lang-btn::before {
        transition: none;
    }
    
    .lang-transition {
        transition: none;
    }
}

/* Print Styles */
@media print {
    .language-switcher {
        display: none;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .language-switcher {
        background: rgba(45, 55, 72, 0.95);
        border-color: rgba(255, 255, 255, 0.1);
    }
    
    .lang-btn {
        color: #e2e8f0;
    }
    
    .lang-btn:hover {
        background: rgba(66, 153, 225, 0.2);
        color: #bee3f8;
    }
    
    .lang-btn.active {
        background: linear-gradient(145deg, #4299e1, #3182ce);
        color: white;
    }
}

/* Language-specific Adjustments */
.lang-ar .language-switcher {
    left: 20px;
    right: auto;
}

.lang-ar .lang-btn {
    flex-direction: row-reverse;
}

/* Tooltip for Language Switcher */
.lang-btn[title] {
    position: relative;
}

.lang-btn[title]:hover::after {
    content: attr(title);
    position: absolute;
    bottom: -35px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(45, 55, 72, 0.9);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    white-space: nowrap;
    z-index: 1001;
    opacity: 0;
    animation: tooltip-fade-in 0.3s ease forwards;
}

@keyframes tooltip-fade-in {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(-5px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

/* Loading State */
.lang-btn.loading {
    pointer-events: none;
    opacity: 0.6;
}

.lang-btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Success/Error States */
.lang-btn.success {
    background: linear-gradient(145deg, #48bb78, #38a169);
    color: white;
}

.lang-btn.error {
    background: linear-gradient(145deg, #e53e3e, #c53030);
    color: white;
}

/* Compact Mode */
.language-switcher.compact {
    padding: 2px;
    gap: 2px;
}

.language-switcher.compact .lang-btn {
    padding: 4px 6px;
    min-width: 40px;
}

.language-switcher.compact .lang-text {
    display: none;
}

.language-switcher.compact .flag {
    font-size: 1.2rem;
}
