// نظام التجارب التفاعلية لمختبر الفيزياء
// Interactive Physics Experiments System

class InteractivePhysicsExperiments {
    constructor() {
        this.experiments = new Map();
        this.currentExperiment = null;
        this.dataLogger = null;
        this.visualizer = null;
        this.instruments = new Map();

        this.init();
    }

    init() {
        this.loadExperiments();
        this.setupEventListeners();
        this.initializeInstruments();
    }

    loadExperiments() {
        // تجارب الميكانيكا - Mechanics Experiments
        this.addExperiment(new MotionExperiment());
        this.addExperiment(new ForceExperiment());
        this.addExperiment(new EnergyExperiment());
        this.addExperiment(new MomentumExperiment());
        this.addExperiment(new RotationalMotionExperiment());
        this.addExperiment(new OscillationExperiment());
        this.addExperiment(new GravityExperiment());
        this.addExperiment(new FluidMechanicsExperiment());

        // تجارب الديناميكا الحرارية - Thermodynamics Experiments
        this.addExperiment(new HeatTransferExperiment());
        this.addExperiment(new GasLawsExperiment());
        this.addExperiment(new HeatEngineExperiment());
        this.addExperiment(new PhaseTransitionExperiment());

        // تجارب الكهرباء والمغناطيسية - Electricity & Magnetism
        this.addExperiment(new ElectricFieldExperiment());
        this.addExperiment(new MagneticFieldExperiment());
        this.addExperiment(new ElectromagneticInductionExperiment());
        this.addExperiment(new ACCircuitExperiment());

        // تجارب البصريات - Optics Experiments
        this.addExperiment(new ReflectionRefractionExperiment());
        this.addExperiment(new InterferenceExperiment());
        this.addExperiment(new DiffractionExperiment());
        this.addExperiment(new PolarizationExperiment());

        // تجارب الفيزياء الحديثة - Modern Physics
        this.addExperiment(new PhotoelectricExperiment());
        this.addExperiment(new AtomicSpectraExperiment());
        this.addExperiment(new RadioactivityExperiment());
        this.addExperiment(new QuantumTunnelingExperiment());
    }

    addExperiment(experiment) {
        this.experiments.set(experiment.id, experiment);
    }

    startExperiment(experimentId) {
        const experiment = this.experiments.get(experimentId);
        if (experiment) {
            this.currentExperiment = experiment;
            experiment.setup();
            experiment.start();
            this.updateUI();
        }
    }

    stopExperiment() {
        if (this.currentExperiment) {
            this.currentExperiment.stop();
            this.currentExperiment = null;
            this.updateUI();
        }
    }

    setupEventListeners() {
        document.addEventListener('experimentStart', (e) => {
            this.startExperiment(e.detail.experimentId);
        });

        document.addEventListener('experimentStop', () => {
            this.stopExperiment();
        });
    }

    initializeInstruments() {
        // تهيئة الأجهزة المختبرية
        this.instruments.set('ruler', new VirtualRuler());
        this.instruments.set('stopwatch', new VirtualStopwatch());
        this.instruments.set('scale', new VirtualScale());
        this.instruments.set('thermometer', new VirtualThermometer());
        this.instruments.set('voltmeter', new VirtualVoltmeter());
        this.instruments.set('ammeter', new VirtualAmmeter());
    }

    updateUI() {
        const container = document.getElementById('experiment-container');
        if (container && this.currentExperiment) {
            container.innerHTML = this.currentExperiment.getHTML();
        }
    }

    getExperimentsList() {
        return Array.from(this.experiments.values()).map(exp => ({
            id: exp.id,
            name: exp.name,
            nameAr: exp.nameAr,
            category: exp.category,
            difficulty: exp.difficulty,
            duration: exp.duration
        }));
    }
}

// فئة أساسية للتجارب - Base Experiment Class
class PhysicsExperiment {
    constructor(id, name, nameAr, category) {
        this.id = id;
        this.name = name;
        this.nameAr = nameAr;
        this.category = category;
        this.isRunning = false;
        this.data = [];
        this.parameters = new Map();
        this.results = new Map();
        this.canvas = null;
        this.ctx = null;
        this.animationId = null;
    }

    setup() {
        this.createCanvas();
        this.initializeParameters();
        this.setupControls();
    }

    start() {
        this.isRunning = true;
        this.animate();
        this.startDataCollection();
    }

    stop() {
        this.isRunning = false;
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }
        this.stopDataCollection();
    }

    createCanvas() {
        const container = document.getElementById('experiment-canvas');
        if (container) {
            this.canvas = document.createElement('canvas');
            this.canvas.width = 800;
            this.canvas.height = 600;
            this.ctx = this.canvas.getContext('2d');
            container.appendChild(this.canvas);
        }
    }

    animate() {
        if (!this.isRunning) return;

        this.update();
        this.draw();

        this.animationId = requestAnimationFrame(() => this.animate());
    }

    update() {
        // يتم تنفيذها في الفئات المشتقة
    }

    draw() {
        // يتم تنفيذها في الفئات المشتقة
    }

    initializeParameters() {
        // يتم تنفيذها في الفئات المشتقة
    }

    setupControls() {
        // يتم تنفيذها في الفئات المشتقة
    }

    startDataCollection() {
        // بدء جمع البيانات
    }

    stopDataCollection() {
        // إيقاف جمع البيانات
    }

    getHTML() {
        return `
            <div class="experiment-interface">
                <div class="experiment-header">
                    <h2>${this.nameAr} - ${this.name}</h2>
                    <div class="experiment-controls">
                        <button onclick="physicsExperiments.stopExperiment()" class="btn-stop">
                            <i class="fas fa-stop"></i> إيقاف
                        </button>
                    </div>
                </div>
                <div class="experiment-content">
                    <div class="experiment-canvas" id="experiment-canvas"></div>
                    <div class="experiment-controls" id="experiment-controls"></div>
                    <div class="experiment-data" id="experiment-data"></div>
                </div>
            </div>
        `;
    }

    setParameter(name, value) {
        this.parameters.set(name, value);
        this.onParameterChange(name, value);
    }

    getParameter(name) {
        return this.parameters.get(name);
    }

    onParameterChange(name, value) {
        // يتم تنفيذها في الفئات المشتقة
    }

    recordData(time, values) {
        this.data.push({
            time: time,
            ...values
        });
    }

    calculateResults() {
        // يتم تنفيذها في الفئات المشتقة
    }

    exportData() {
        const csvContent = this.convertToCSV(this.data);
        this.downloadCSV(csvContent, `${this.id}_data.csv`);
    }

    convertToCSV(data) {
        if (data.length === 0) return '';

        const headers = Object.keys(data[0]);
        const csvRows = [headers.join(',')];

        data.forEach(row => {
            const values = headers.map(header => row[header]);
            csvRows.push(values.join(','));
        });

        return csvRows.join('\n');
    }

    downloadCSV(content, filename) {
        const blob = new Blob([content], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        a.click();
        window.URL.revokeObjectURL(url);
    }
}

// تجربة الحركة - Motion Experiment
class MotionExperiment extends PhysicsExperiment {
    constructor() {
        super('motion', 'Motion Analysis', 'تحليل الحركة', 'mechanics');
        this.ball = {
            x: 50,
            y: 300,
            vx: 0,
            vy: 0,
            ax: 0,
            ay: 9.8, // الجاذبية
            radius: 10,
            mass: 1,
            trail: []
        };
        this.startTime = 0;
        this.timeScale = 1;
    }

    initializeParameters() {
        this.setParameter('initialVelocityX', 20);
        this.setParameter('initialVelocityY', 30);
        this.setParameter('gravity', 9.8);
        this.setParameter('airResistance', 0);
        this.setParameter('timeScale', 1);
    }

    setupControls() {
        const controlsContainer = document.getElementById('experiment-controls');
        if (controlsContainer) {
            controlsContainer.innerHTML = `
                <div class="control-panel">
                    <h3>متحكمات التجربة</h3>

                    <div class="control-group">
                        <label>السرعة الأولية الأفقية (م/ث)</label>
                        <input type="range" id="vx-slider" min="0" max="50" value="20"
                               oninput="physicsExperiments.currentExperiment.setParameter('initialVelocityX', this.value)">
                        <span id="vx-value">20</span>
                    </div>

                    <div class="control-group">
                        <label>السرعة الأولية الرأسية (م/ث)</label>
                        <input type="range" id="vy-slider" min="-30" max="50" value="30"
                               oninput="physicsExperiments.currentExperiment.setParameter('initialVelocityY', this.value)">
                        <span id="vy-value">30</span>
                    </div>

                    <div class="control-group">
                        <label>الجاذبية (م/ث²)</label>
                        <input type="range" id="gravity-slider" min="0" max="20" value="9.8" step="0.1"
                               oninput="physicsExperiments.currentExperiment.setParameter('gravity', this.value)">
                        <span id="gravity-value">9.8</span>
                    </div>

                    <div class="control-group">
                        <label>مقاومة الهواء</label>
                        <input type="range" id="air-slider" min="0" max="0.1" value="0" step="0.01"
                               oninput="physicsExperiments.currentExperiment.setParameter('airResistance', this.value)">
                        <span id="air-value">0</span>
                    </div>

                    <div class="control-buttons">
                        <button onclick="physicsExperiments.currentExperiment.resetExperiment()" class="btn-reset">
                            <i class="fas fa-redo"></i> إعادة تعيين
                        </button>
                        <button onclick="physicsExperiments.currentExperiment.exportData()" class="btn-export">
                            <i class="fas fa-download"></i> تصدير البيانات
                        </button>
                    </div>
                </div>
            `;
        }
    }

    start() {
        super.start();
        this.resetBall();
        this.startTime = Date.now();
    }

    resetBall() {
        this.ball.x = 50;
        this.ball.y = 300;
        this.ball.vx = this.getParameter('initialVelocityX') * 2; // تحويل للبكسل
        this.ball.vy = -this.getParameter('initialVelocityY') * 2; // سالب للأعلى
        this.ball.trail = [];
        this.data = [];
    }

    update() {
        if (!this.isRunning) return;

        const dt = 0.016; // 60 FPS
        const gravity = this.getParameter('gravity') * 2; // تحويل للبكسل
        const airResistance = this.getParameter('airResistance');

        // تطبيق مقاومة الهواء
        if (airResistance > 0) {
            const speed = Math.sqrt(this.ball.vx * this.ball.vx + this.ball.vy * this.ball.vy);
            const dragForce = airResistance * speed * speed;
            const dragX = -dragForce * (this.ball.vx / speed) / this.ball.mass;
            const dragY = -dragForce * (this.ball.vy / speed) / this.ball.mass;

            this.ball.ax = dragX;
            this.ball.ay = gravity + dragY;
        } else {
            this.ball.ax = 0;
            this.ball.ay = gravity;
        }

        // تحديث السرعة والموقع
        this.ball.vx += this.ball.ax * dt;
        this.ball.vy += this.ball.ay * dt;
        this.ball.x += this.ball.vx * dt;
        this.ball.y += this.ball.vy * dt;

        // إضافة نقطة للمسار
        this.ball.trail.push({ x: this.ball.x, y: this.ball.y });
        if (this.ball.trail.length > 100) {
            this.ball.trail.shift();
        }

        // تسجيل البيانات
        const currentTime = (Date.now() - this.startTime) / 1000;
        this.recordData(currentTime, {
            x: (this.ball.x - 50) / 2, // تحويل للمتر
            y: (300 - this.ball.y) / 2, // تحويل للمتر
            vx: this.ball.vx / 2,
            vy: -this.ball.vy / 2,
            ax: this.ball.ax / 2,
            ay: -this.ball.ay / 2
        });

        // إعادة تعيين إذا خرج من الشاشة
        if (this.ball.x > this.canvas.width || this.ball.y > this.canvas.height) {
            this.resetBall();
            this.startTime = Date.now();
        }
    }

    draw() {
        if (!this.ctx) return;

        // مسح الشاشة
        this.ctx.fillStyle = '#f0f8ff';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // رسم الشبكة
        this.drawGrid();

        // رسم المسار
        this.drawTrail();

        // رسم الكرة
        this.drawBall();

        // رسم المعلومات
        this.drawInfo();

        // رسم المتجهات
        this.drawVectors();
    }

    drawGrid() {
        this.ctx.strokeStyle = '#e0e0e0';
        this.ctx.lineWidth = 1;

        // خطوط عمودية
        for (let x = 0; x <= this.canvas.width; x += 20) {
            this.ctx.beginPath();
            this.ctx.moveTo(x, 0);
            this.ctx.lineTo(x, this.canvas.height);
            this.ctx.stroke();
        }

        // خطوط أفقية
        for (let y = 0; y <= this.canvas.height; y += 20) {
            this.ctx.beginPath();
            this.ctx.moveTo(0, y);
            this.ctx.lineTo(this.canvas.width, y);
            this.ctx.stroke();
        }
    }

    drawTrail() {
        if (this.ball.trail.length < 2) return;

        this.ctx.strokeStyle = '#4299e1';
        this.ctx.lineWidth = 2;
        this.ctx.beginPath();
        this.ctx.moveTo(this.ball.trail[0].x, this.ball.trail[0].y);

        for (let i = 1; i < this.ball.trail.length; i++) {
            this.ctx.lineTo(this.ball.trail[i].x, this.ball.trail[i].y);
        }
        this.ctx.stroke();
    }

    drawBall() {
        this.ctx.fillStyle = '#e53e3e';
        this.ctx.beginPath();
        this.ctx.arc(this.ball.x, this.ball.y, this.ball.radius, 0, 2 * Math.PI);
        this.ctx.fill();

        // ظل الكرة
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.2)';
        this.ctx.beginPath();
        this.ctx.arc(this.ball.x + 2, this.ball.y + 2, this.ball.radius, 0, 2 * Math.PI);
        this.ctx.fill();
    }

    drawVectors() {
        const scale = 0.1;

        // متجه السرعة
        this.ctx.strokeStyle = '#48bb78';
        this.ctx.lineWidth = 3;
        this.ctx.beginPath();
        this.ctx.moveTo(this.ball.x, this.ball.y);
        this.ctx.lineTo(this.ball.x + this.ball.vx * scale, this.ball.y + this.ball.vy * scale);
        this.ctx.stroke();

        // رأس السهم للسرعة
        this.drawArrowHead(
            this.ball.x + this.ball.vx * scale,
            this.ball.y + this.ball.vy * scale,
            Math.atan2(this.ball.vy, this.ball.vx),
            '#48bb78'
        );

        // متجه التسارع
        this.ctx.strokeStyle = '#f56565';
        this.ctx.lineWidth = 2;
        this.ctx.beginPath();
        this.ctx.moveTo(this.ball.x, this.ball.y);
        this.ctx.lineTo(this.ball.x + this.ball.ax * scale * 10, this.ball.y + this.ball.ay * scale * 10);
        this.ctx.stroke();
    }

    drawArrowHead(x, y, angle, color) {
        this.ctx.fillStyle = color;
        this.ctx.save();
        this.ctx.translate(x, y);
        this.ctx.rotate(angle);
        this.ctx.beginPath();
        this.ctx.moveTo(0, 0);
        this.ctx.lineTo(-10, -5);
        this.ctx.lineTo(-10, 5);
        this.ctx.closePath();
        this.ctx.fill();
        this.ctx.restore();
    }

    drawInfo() {
        this.ctx.fillStyle = '#2d3748';
        this.ctx.font = '14px Arial';

        const currentTime = (Date.now() - this.startTime) / 1000;
        const info = [
            `الوقت: ${currentTime.toFixed(2)} ث`,
            `الموقع: (${((this.ball.x - 50) / 2).toFixed(1)}, ${((300 - this.ball.y) / 2).toFixed(1)}) م`,
            `السرعة: (${(this.ball.vx / 2).toFixed(1)}, ${(-this.ball.vy / 2).toFixed(1)}) م/ث`,
            `التسارع: (${(this.ball.ax / 2).toFixed(1)}, ${(-this.ball.ay / 2).toFixed(1)}) م/ث²`
        ];

        info.forEach((text, index) => {
            this.ctx.fillText(text, 10, 20 + index * 20);
        });
    }

    onParameterChange(name, value) {
        // تحديث القيم في الواجهة
        const valueSpan = document.getElementById(name.replace(/([A-Z])/g, '-$1').toLowerCase() + '-value');
        if (valueSpan) {
            valueSpan.textContent = value;
        }

        // إعادة تعيين الكرة إذا تغيرت السرعة الأولية
        if (name === 'initialVelocityX' || name === 'initialVelocityY') {
            this.resetBall();
            this.startTime = Date.now();
        }
    }

    resetExperiment() {
        this.resetBall();
        this.startTime = Date.now();
    }
}

// تجربة القوى - Forces Experiment
class ForceExperiment extends PhysicsExperiment {
    constructor() {
        super('forces', 'Forces and Newton\'s Laws', 'القوى وقوانين نيوتن', 'mechanics');
        this.objects = [];
        this.forces = [];
        this.selectedObject = null;
        this.isDragging = false;
        this.mousePos = { x: 0, y: 0 };
    }

    initializeParameters() {
        this.setParameter('friction', 0.1);
        this.setParameter('gravity', 9.8);
        this.setParameter('showVectors', true);
        this.setParameter('showGrid', true);

        // إنشاء الكائنات
        this.objects = [
            {
                id: 1,
                x: 200,
                y: 300,
                vx: 0,
                vy: 0,
                mass: 2,
                radius: 20,
                color: '#e53e3e',
                forces: [],
                selected: false
            },
            {
                id: 2,
                x: 400,
                y: 300,
                vx: 0,
                vy: 0,
                mass: 1,
                radius: 15,
                color: '#4299e1',
                forces: [],
                selected: false
            }
        ];
    }

    setupControls() {
        const controlsContainer = document.getElementById('experiment-controls');
        if (controlsContainer) {
            controlsContainer.innerHTML = `
                <div class="control-panel">
                    <h3>متحكمات القوى</h3>

                    <div class="control-group">
                        <label>معامل الاحتكاك</label>
                        <input type="range" id="friction-slider" min="0" max="1" value="0.1" step="0.01"
                               oninput="physicsExperiments.currentExperiment.setParameter('friction', this.value)">
                        <span id="friction-value">0.1</span>
                    </div>

                    <div class="control-group">
                        <label>الجاذبية (م/ث²)</label>
                        <input type="range" id="gravity-slider" min="0" max="20" value="9.8" step="0.1"
                               oninput="physicsExperiments.currentExperiment.setParameter('gravity', this.value)">
                        <span id="gravity-value">9.8</span>
                    </div>

                    <div class="control-group">
                        <label>
                            <input type="checkbox" id="vectors-check" checked
                                   onchange="physicsExperiments.currentExperiment.setParameter('showVectors', this.checked)">
                            إظهار متجهات القوى
                        </label>
                    </div>

                    <div class="force-controls">
                        <h4>إضافة قوة</h4>
                        <div class="force-inputs">
                            <label>القوة الأفقية (N)</label>
                            <input type="number" id="force-x" value="0" step="0.1">

                            <label>القوة الرأسية (N)</label>
                            <input type="number" id="force-y" value="0" step="0.1">

                            <button onclick="physicsExperiments.currentExperiment.addForce()" class="btn-add-force">
                                <i class="fas fa-plus"></i> إضافة قوة
                            </button>
                        </div>
                    </div>

                    <div class="control-buttons">
                        <button onclick="physicsExperiments.currentExperiment.resetExperiment()" class="btn-reset">
                            <i class="fas fa-redo"></i> إعادة تعيين
                        </button>
                        <button onclick="physicsExperiments.currentExperiment.clearForces()" class="btn-clear">
                            <i class="fas fa-trash"></i> مسح القوى
                        </button>
                    </div>
                </div>
            `;
        }
    }

    start() {
        super.start();
        this.setupMouseEvents();
    }

    setupMouseEvents() {
        if (!this.canvas) return;

        this.canvas.addEventListener('mousedown', (e) => {
            const rect = this.canvas.getBoundingClientRect();
            this.mousePos.x = e.clientX - rect.left;
            this.mousePos.y = e.clientY - rect.top;

            // البحث عن كائن تحت الماوس
            this.selectedObject = this.getObjectAtPosition(this.mousePos.x, this.mousePos.y);
            if (this.selectedObject) {
                this.isDragging = true;
                this.selectedObject.selected = true;
            }
        });

        this.canvas.addEventListener('mousemove', (e) => {
            const rect = this.canvas.getBoundingClientRect();
            this.mousePos.x = e.clientX - rect.left;
            this.mousePos.y = e.clientY - rect.top;

            if (this.isDragging && this.selectedObject) {
                // تطبيق قوة بناءً على حركة الماوس
                const dx = this.mousePos.x - this.selectedObject.x;
                const dy = this.mousePos.y - this.selectedObject.y;

                this.selectedObject.forces.push({
                    fx: dx * 0.1,
                    fy: dy * 0.1,
                    type: 'applied',
                    duration: 1
                });
            }
        });

        this.canvas.addEventListener('mouseup', () => {
            this.isDragging = false;
            if (this.selectedObject) {
                this.selectedObject.selected = false;
                this.selectedObject = null;
            }
        });
    }

    getObjectAtPosition(x, y) {
        for (let obj of this.objects) {
            const dx = x - obj.x;
            const dy = y - obj.y;
            const distance = Math.sqrt(dx * dx + dy * dy);
            if (distance <= obj.radius) {
                return obj;
            }
        }
        return null;
    }

    update() {
        if (!this.isRunning) return;

        const dt = 0.016;
        const friction = this.getParameter('friction');
        const gravity = this.getParameter('gravity');

        this.objects.forEach(obj => {
            // حساب القوة الإجمالية
            let totalFx = 0;
            let totalFy = gravity * obj.mass; // قوة الجاذبية

            // إضافة القوى المطبقة
            obj.forces.forEach((force, index) => {
                totalFx += force.fx;
                totalFy += force.fy;

                // تقليل مدة القوة
                force.duration -= dt;
                if (force.duration <= 0) {
                    obj.forces.splice(index, 1);
                }
            });

            // قوة الاحتكاك
            if (obj.vy === 0) { // على الأرض
                const frictionForce = friction * obj.mass * gravity;
                if (obj.vx > 0) {
                    totalFx -= Math.min(frictionForce, Math.abs(totalFx));
                } else if (obj.vx < 0) {
                    totalFx += Math.min(frictionForce, Math.abs(totalFx));
                }
            }

            // تطبيق قانون نيوتن الثاني F = ma
            const ax = totalFx / obj.mass;
            const ay = totalFy / obj.mass;

            // تحديث السرعة والموقع
            obj.vx += ax * dt;
            obj.vy += ay * dt;
            obj.x += obj.vx * dt;
            obj.y += obj.vy * dt;

            // التحقق من الحدود
            if (obj.y + obj.radius > this.canvas.height) {
                obj.y = this.canvas.height - obj.radius;
                obj.vy = 0;
            }
            if (obj.x - obj.radius < 0) {
                obj.x = obj.radius;
                obj.vx = 0;
            }
            if (obj.x + obj.radius > this.canvas.width) {
                obj.x = this.canvas.width - obj.radius;
                obj.vx = 0;
            }

            // تسجيل البيانات
            const currentTime = (Date.now() - this.startTime) / 1000;
            this.recordData(currentTime, {
                objectId: obj.id,
                x: obj.x,
                y: obj.y,
                vx: obj.vx,
                vy: obj.vy,
                totalForceX: totalFx,
                totalForceY: totalFy,
                acceleration: Math.sqrt(ax * ax + ay * ay)
            });
        });
    }

    draw() {
        if (!this.ctx) return;

        // مسح الشاشة
        this.ctx.fillStyle = '#f0f8ff';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // رسم الشبكة
        if (this.getParameter('showGrid')) {
            this.drawGrid();
        }

        // رسم الأرض
        this.ctx.fillStyle = '#8b4513';
        this.ctx.fillRect(0, this.canvas.height - 10, this.canvas.width, 10);

        // رسم الكائنات
        this.objects.forEach(obj => {
            this.drawObject(obj);

            if (this.getParameter('showVectors')) {
                this.drawForceVectors(obj);
            }
        });

        // رسم المعلومات
        this.drawForceInfo();
    }

    drawObject(obj) {
        // رسم الكائن
        this.ctx.fillStyle = obj.selected ? '#ffd700' : obj.color;
        this.ctx.beginPath();
        this.ctx.arc(obj.x, obj.y, obj.radius, 0, 2 * Math.PI);
        this.ctx.fill();

        // رسم الحدود
        this.ctx.strokeStyle = obj.selected ? '#ff6b6b' : '#2d3748';
        this.ctx.lineWidth = obj.selected ? 3 : 1;
        this.ctx.stroke();

        // رسم الكتلة
        this.ctx.fillStyle = '#fff';
        this.ctx.font = '12px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(`${obj.mass}kg`, obj.x, obj.y + 4);
    }

    drawForceVectors(obj) {
        const scale = 0.05;

        obj.forces.forEach(force => {
            this.ctx.strokeStyle = '#e53e3e';
            this.ctx.lineWidth = 3;
            this.ctx.beginPath();
            this.ctx.moveTo(obj.x, obj.y);
            this.ctx.lineTo(obj.x + force.fx * scale, obj.y + force.fy * scale);
            this.ctx.stroke();

            // رأس السهم
            this.drawArrowHead(
                obj.x + force.fx * scale,
                obj.y + force.fy * scale,
                Math.atan2(force.fy, force.fx),
                '#e53e3e'
            );
        });
    }

    drawArrowHead(x, y, angle, color) {
        this.ctx.fillStyle = color;
        this.ctx.save();
        this.ctx.translate(x, y);
        this.ctx.rotate(angle);
        this.ctx.beginPath();
        this.ctx.moveTo(0, 0);
        this.ctx.lineTo(-10, -5);
        this.ctx.lineTo(-10, 5);
        this.ctx.closePath();
        this.ctx.fill();
        this.ctx.restore();
    }

    drawForceInfo() {
        this.ctx.fillStyle = '#2d3748';
        this.ctx.font = '14px Arial';
        this.ctx.textAlign = 'left';

        let y = 20;
        this.ctx.fillText('انقر واسحب الكائنات لتطبيق القوى', 10, y);
        y += 20;

        this.objects.forEach(obj => {
            this.ctx.fillText(`الكائن ${obj.id}: الكتلة = ${obj.mass}kg`, 10, y);
            y += 15;
            this.ctx.fillText(`السرعة: (${obj.vx.toFixed(1)}, ${obj.vy.toFixed(1)}) م/ث`, 10, y);
            y += 25;
        });
    }

    addForce() {
        const fx = parseFloat(document.getElementById('force-x').value) || 0;
        const fy = parseFloat(document.getElementById('force-y').value) || 0;

        if (this.selectedObject) {
            this.selectedObject.forces.push({
                fx: fx * 10, // تحويل للبكسل
                fy: fy * 10,
                type: 'manual',
                duration: 2
            });
        } else {
            alert('يرجى اختيار كائن أولاً');
        }
    }

    clearForces() {
        this.objects.forEach(obj => {
            obj.forces = [];
            obj.vx = 0;
            obj.vy = 0;
        });
    }

    resetExperiment() {
        this.objects.forEach(obj => {
            obj.x = obj.id === 1 ? 200 : 400;
            obj.y = 300;
            obj.vx = 0;
            obj.vy = 0;
            obj.forces = [];
            obj.selected = false;
        });
        this.selectedObject = null;
        this.isDragging = false;
        this.data = [];
    }

    onParameterChange(name, value) {
        const valueSpan = document.getElementById(name + '-value');
        if (valueSpan) {
            valueSpan.textContent = value;
        }
    }
}

// تجربة الطاقة - Energy Experiment
class EnergyExperiment extends PhysicsExperiment {
    constructor() {
        super('energy', 'Energy Conservation', 'حفظ الطاقة', 'mechanics');
        this.pendulum = {
            x: 400,
            y: 100,
            length: 200,
            angle: Math.PI / 4,
            angularVelocity: 0,
            mass: 1,
            gravity: 9.8
        };
        this.energyHistory = [];
        this.maxHistoryLength = 200;
    }

    initializeParameters() {
        this.setParameter('length', 200);
        this.setParameter('mass', 1);
        this.setParameter('gravity', 9.8);
        this.setParameter('damping', 0.999);
        this.setParameter('initialAngle', 45);
    }

    setupControls() {
        const controlsContainer = document.getElementById('experiment-controls');
        if (controlsContainer) {
            controlsContainer.innerHTML = `
                <div class="control-panel">
                    <h3>متحكمات البندول</h3>

                    <div class="control-group">
                        <label>طول البندول (سم)</label>
                        <input type="range" id="length-slider" min="100" max="300" value="200"
                               oninput="physicsExperiments.currentExperiment.setParameter('length', this.value)">
                        <span id="length-value">200</span>
                    </div>

                    <div class="control-group">
                        <label>الكتلة (كغ)</label>
                        <input type="range" id="mass-slider" min="0.5" max="3" value="1" step="0.1"
                               oninput="physicsExperiments.currentExperiment.setParameter('mass', this.value)">
                        <span id="mass-value">1</span>
                    </div>

                    <div class="control-group">
                        <label>الجاذبية (م/ث²)</label>
                        <input type="range" id="gravity-slider" min="1" max="20" value="9.8" step="0.1"
                               oninput="physicsExperiments.currentExperiment.setParameter('gravity', this.value)">
                        <span id="gravity-value">9.8</span>
                    </div>

                    <div class="control-group">
                        <label>التخميد</label>
                        <input type="range" id="damping-slider" min="0.99" max="1" value="0.999" step="0.001"
                               oninput="physicsExperiments.currentExperiment.setParameter('damping', this.value)">
                        <span id="damping-value">0.999</span>
                    </div>

                    <div class="control-group">
                        <label>الزاوية الأولية (درجة)</label>
                        <input type="range" id="angle-slider" min="10" max="80" value="45"
                               oninput="physicsExperiments.currentExperiment.setParameter('initialAngle', this.value)">
                        <span id="angle-value">45</span>
                    </div>

                    <div class="energy-display">
                        <h4>الطاقات</h4>
                        <div id="energy-values">
                            <div>الطاقة الحركية: <span id="kinetic-energy">0</span> J</div>
                            <div>طاقة الوضع: <span id="potential-energy">0</span> J</div>
                            <div>الطاقة الكلية: <span id="total-energy">0</span> J</div>
                        </div>
                    </div>

                    <div class="control-buttons">
                        <button onclick="physicsExperiments.currentExperiment.resetExperiment()" class="btn-reset">
                            <i class="fas fa-redo"></i> إعادة تعيين
                        </button>
                        <button onclick="physicsExperiments.currentExperiment.toggleEnergyGraph()" class="btn-graph">
                            <i class="fas fa-chart-line"></i> رسم الطاقة
                        </button>
                    </div>
                </div>
            `;
        }
    }

    start() {
        super.start();
        this.resetPendulum();
        this.showEnergyGraph = false;
    }

    resetPendulum() {
        this.pendulum.angle = (this.getParameter('initialAngle') * Math.PI) / 180;
        this.pendulum.angularVelocity = 0;
        this.pendulum.length = this.getParameter('length');
        this.pendulum.mass = this.getParameter('mass');
        this.pendulum.gravity = this.getParameter('gravity');
        this.energyHistory = [];
    }

    update() {
        if (!this.isRunning) return;

        const dt = 0.016;
        const g = this.getParameter('gravity');
        const L = this.getParameter('length') / 100; // تحويل للمتر
        const damping = this.getParameter('damping');

        // معادلة البندول البسيط
        const angularAcceleration = -(g / L) * Math.sin(this.pendulum.angle);

        // تحديث السرعة الزاوية والزاوية
        this.pendulum.angularVelocity += angularAcceleration * dt;
        this.pendulum.angularVelocity *= damping; // تطبيق التخميد
        this.pendulum.angle += this.pendulum.angularVelocity * dt;

        // حساب الطاقات
        const height = L * (1 - Math.cos(this.pendulum.angle));
        const velocity = L * Math.abs(this.pendulum.angularVelocity);

        const kineticEnergy = 0.5 * this.pendulum.mass * velocity * velocity;
        const potentialEnergy = this.pendulum.mass * g * height;
        const totalEnergy = kineticEnergy + potentialEnergy;

        // تحديث عرض الطاقة
        this.updateEnergyDisplay(kineticEnergy, potentialEnergy, totalEnergy);

        // إضافة للتاريخ
        this.energyHistory.push({
            time: (Date.now() - this.startTime) / 1000,
            kinetic: kineticEnergy,
            potential: potentialEnergy,
            total: totalEnergy,
            angle: this.pendulum.angle
        });

        if (this.energyHistory.length > this.maxHistoryLength) {
            this.energyHistory.shift();
        }

        // تسجيل البيانات
        const currentTime = (Date.now() - this.startTime) / 1000;
        this.recordData(currentTime, {
            angle: this.pendulum.angle * 180 / Math.PI,
            angularVelocity: this.pendulum.angularVelocity,
            kineticEnergy: kineticEnergy,
            potentialEnergy: potentialEnergy,
            totalEnergy: totalEnergy,
            height: height
        });
    }

    draw() {
        if (!this.ctx) return;

        // مسح الشاشة
        this.ctx.fillStyle = '#f0f8ff';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        if (this.showEnergyGraph) {
            this.drawEnergyGraph();
        } else {
            this.drawPendulum();
        }
    }

    drawPendulum() {
        const centerX = this.pendulum.x;
        const centerY = this.pendulum.y;
        const length = this.getParameter('length');

        // حساب موقع الكتلة
        const bobX = centerX + length * Math.sin(this.pendulum.angle);
        const bobY = centerY + length * Math.cos(this.pendulum.angle);

        // رسم نقطة التعليق
        this.ctx.fillStyle = '#2d3748';
        this.ctx.beginPath();
        this.ctx.arc(centerX, centerY, 5, 0, 2 * Math.PI);
        this.ctx.fill();

        // رسم الخيط
        this.ctx.strokeStyle = '#4a5568';
        this.ctx.lineWidth = 2;
        this.ctx.beginPath();
        this.ctx.moveTo(centerX, centerY);
        this.ctx.lineTo(bobX, bobY);
        this.ctx.stroke();

        // رسم الكتلة
        const radius = Math.max(10, this.pendulum.mass * 15);
        this.ctx.fillStyle = '#e53e3e';
        this.ctx.beginPath();
        this.ctx.arc(bobX, bobY, radius, 0, 2 * Math.PI);
        this.ctx.fill();

        // رسم ظل الكتلة
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.2)';
        this.ctx.beginPath();
        this.ctx.arc(bobX + 2, bobY + 2, radius, 0, 2 * Math.PI);
        this.ctx.fill();

        // رسم مسار الحركة
        this.drawTrajectory(centerX, centerY, length);

        // رسم متجه السرعة
        this.drawVelocityVector(bobX, bobY);

        // رسم مستويات الطاقة
        this.drawEnergyLevels(centerX, centerY, length);
    }

    drawTrajectory(centerX, centerY, length) {
        this.ctx.strokeStyle = 'rgba(66, 153, 225, 0.3)';
        this.ctx.lineWidth = 1;
        this.ctx.setLineDash([5, 5]);

        this.ctx.beginPath();
        for (let angle = -Math.PI/2; angle <= Math.PI/2; angle += 0.1) {
            const x = centerX + length * Math.sin(angle);
            const y = centerY + length * Math.cos(angle);

            if (angle === -Math.PI/2) {
                this.ctx.moveTo(x, y);
            } else {
                this.ctx.lineTo(x, y);
            }
        }
        this.ctx.stroke();
        this.ctx.setLineDash([]);
    }

    drawVelocityVector(bobX, bobY) {
        const scale = 20;
        const vx = this.pendulum.angularVelocity * this.getParameter('length') / 100 * Math.cos(this.pendulum.angle) * scale;
        const vy = -this.pendulum.angularVelocity * this.getParameter('length') / 100 * Math.sin(this.pendulum.angle) * scale;

        this.ctx.strokeStyle = '#48bb78';
        this.ctx.lineWidth = 3;
        this.ctx.beginPath();
        this.ctx.moveTo(bobX, bobY);
        this.ctx.lineTo(bobX + vx, bobY + vy);
        this.ctx.stroke();

        // رأس السهم
        if (Math.abs(vx) > 1 || Math.abs(vy) > 1) {
            this.drawArrowHead(bobX + vx, bobY + vy, Math.atan2(vy, vx), '#48bb78');
        }
    }

    drawEnergyLevels(centerX, centerY, length) {
        const g = this.getParameter('gravity');
        const L = length / 100;
        const maxHeight = L * (1 - Math.cos(Math.PI/4)); // عند 45 درجة

        // خط الطاقة الكلية
        const totalEnergyHeight = centerY + length - (maxHeight * 100);
        this.ctx.strokeStyle = '#f56565';
        this.ctx.lineWidth = 2;
        this.ctx.setLineDash([10, 5]);
        this.ctx.beginPath();
        this.ctx.moveTo(centerX - length, totalEnergyHeight);
        this.ctx.lineTo(centerX + length, totalEnergyHeight);
        this.ctx.stroke();
        this.ctx.setLineDash([]);

        // تسمية
        this.ctx.fillStyle = '#f56565';
        this.ctx.font = '12px Arial';
        this.ctx.fillText('مستوى الطاقة الكلية', centerX + length + 10, totalEnergyHeight);
    }

    drawEnergyGraph() {
        if (this.energyHistory.length < 2) return;

        const margin = 50;
        const graphWidth = this.canvas.width - 2 * margin;
        const graphHeight = this.canvas.height - 2 * margin;

        // رسم المحاور
        this.ctx.strokeStyle = '#2d3748';
        this.ctx.lineWidth = 2;
        this.ctx.beginPath();
        this.ctx.moveTo(margin, margin);
        this.ctx.lineTo(margin, margin + graphHeight);
        this.ctx.lineTo(margin + graphWidth, margin + graphHeight);
        this.ctx.stroke();

        // العثور على القيم القصوى
        const maxEnergy = Math.max(...this.energyHistory.map(h => h.total));
        const minTime = this.energyHistory[0].time;
        const maxTime = this.energyHistory[this.energyHistory.length - 1].time;

        // رسم الطاقة الحركية
        this.drawEnergyLine(this.energyHistory.map(h => h.kinetic), '#48bb78', maxEnergy, minTime, maxTime, margin, graphWidth, graphHeight);

        // رسم طاقة الوضع
        this.drawEnergyLine(this.energyHistory.map(h => h.potential), '#4299e1', maxEnergy, minTime, maxTime, margin, graphWidth, graphHeight);

        // رسم الطاقة الكلية
        this.drawEnergyLine(this.energyHistory.map(h => h.total), '#e53e3e', maxEnergy, minTime, maxTime, margin, graphWidth, graphHeight);

        // رسم المفاتيح
        this.drawLegend(margin, margin - 30);
    }

    drawEnergyLine(energyData, color, maxEnergy, minTime, maxTime, margin, graphWidth, graphHeight) {
        this.ctx.strokeStyle = color;
        this.ctx.lineWidth = 2;
        this.ctx.beginPath();

        energyData.forEach((energy, index) => {
            const time = this.energyHistory[index].time;
            const x = margin + (time - minTime) / (maxTime - minTime) * graphWidth;
            const y = margin + graphHeight - (energy / maxEnergy) * graphHeight;

            if (index === 0) {
                this.ctx.moveTo(x, y);
            } else {
                this.ctx.lineTo(x, y);
            }
        });

        this.ctx.stroke();
    }

    drawLegend(x, y) {
        const legends = [
            { color: '#48bb78', text: 'الطاقة الحركية' },
            { color: '#4299e1', text: 'طاقة الوضع' },
            { color: '#e53e3e', text: 'الطاقة الكلية' }
        ];

        legends.forEach((legend, index) => {
            const legendX = x + index * 120;

            // رسم الخط
            this.ctx.strokeStyle = legend.color;
            this.ctx.lineWidth = 3;
            this.ctx.beginPath();
            this.ctx.moveTo(legendX, y);
            this.ctx.lineTo(legendX + 20, y);
            this.ctx.stroke();

            // رسم النص
            this.ctx.fillStyle = '#2d3748';
            this.ctx.font = '12px Arial';
            this.ctx.fillText(legend.text, legendX + 25, y + 4);
        });
    }

    updateEnergyDisplay(kinetic, potential, total) {
        const kineticSpan = document.getElementById('kinetic-energy');
        const potentialSpan = document.getElementById('potential-energy');
        const totalSpan = document.getElementById('total-energy');

        if (kineticSpan) kineticSpan.textContent = kinetic.toFixed(3);
        if (potentialSpan) potentialSpan.textContent = potential.toFixed(3);
        if (totalSpan) totalSpan.textContent = total.toFixed(3);
    }

    toggleEnergyGraph() {
        this.showEnergyGraph = !this.showEnergyGraph;
    }

    onParameterChange(name, value) {
        const valueSpan = document.getElementById(name + '-value');
        if (valueSpan) {
            valueSpan.textContent = value;
        }

        if (name === 'initialAngle') {
            this.resetPendulum();
        }
    }

    resetExperiment() {
        this.resetPendulum();
        this.startTime = Date.now();
        this.data = [];
    }

    drawArrowHead(x, y, angle, color) {
        this.ctx.fillStyle = color;
        this.ctx.save();
        this.ctx.translate(x, y);
        this.ctx.rotate(angle);
        this.ctx.beginPath();
        this.ctx.moveTo(0, 0);
        this.ctx.lineTo(-8, -4);
        this.ctx.lineTo(-8, 4);
        this.ctx.closePath();
        this.ctx.fill();
        this.ctx.restore();
    }
}

// تجربة الزخم - Momentum Experiment
class MomentumExperiment extends PhysicsExperiment {
    constructor() {
        super('momentum', 'Momentum Conservation', 'حفظ الزخم', 'mechanics');
        this.balls = [];
        this.collisionHistory = [];
        this.showVectors = true;
    }

    initializeParameters() {
        this.setParameter('ball1Mass', 2);
        this.setParameter('ball2Mass', 1);
        this.setParameter('ball1Velocity', 5);
        this.setParameter('ball2Velocity', -3);
        this.setParameter('elasticity', 1);
        this.setParameter('showVectors', true);

        this.initializeBalls();
    }

    initializeBalls() {
        this.balls = [
            {
                id: 1,
                x: 200,
                y: 300,
                vx: this.getParameter('ball1Velocity') * 20,
                vy: 0,
                mass: this.getParameter('ball1Mass'),
                radius: Math.max(15, this.getParameter('ball1Mass') * 8),
                color: '#e53e3e',
                trail: []
            },
            {
                id: 2,
                x: 600,
                y: 300,
                vx: this.getParameter('ball2Velocity') * 20,
                vy: 0,
                mass: this.getParameter('ball2Mass'),
                radius: Math.max(15, this.getParameter('ball2Mass') * 8),
                color: '#4299e1',
                trail: []
            }
        ];
    }

    setupControls() {
        const controlsContainer = document.getElementById('experiment-controls');
        if (controlsContainer) {
            controlsContainer.innerHTML = `
                <div class="control-panel">
                    <h3>متحكمات تجربة الزخم</h3>

                    <div class="control-group">
                        <label>كتلة الكرة الأولى (كغ)</label>
                        <input type="range" id="ball1-mass-slider" min="0.5" max="5" value="2" step="0.1"
                               oninput="physicsExperiments.currentExperiment.setParameter('ball1Mass', this.value)">
                        <span id="ball1-mass-value">2</span>
                    </div>

                    <div class="control-group">
                        <label>كتلة الكرة الثانية (كغ)</label>
                        <input type="range" id="ball2-mass-slider" min="0.5" max="5" value="1" step="0.1"
                               oninput="physicsExperiments.currentExperiment.setParameter('ball2Mass', this.value)">
                        <span id="ball2-mass-value">1</span>
                    </div>

                    <div class="control-group">
                        <label>سرعة الكرة الأولى (م/ث)</label>
                        <input type="range" id="ball1-velocity-slider" min="-10" max="10" value="5" step="0.1"
                               oninput="physicsExperiments.currentExperiment.setParameter('ball1Velocity', this.value)">
                        <span id="ball1-velocity-value">5</span>
                    </div>

                    <div class="control-group">
                        <label>سرعة الكرة الثانية (م/ث)</label>
                        <input type="range" id="ball2-velocity-slider" min="-10" max="10" value="-3" step="0.1"
                               oninput="physicsExperiments.currentExperiment.setParameter('ball2Velocity', this.value)">
                        <span id="ball2-velocity-value">-3</span>
                    </div>

                    <div class="control-group">
                        <label>معامل المرونة</label>
                        <input type="range" id="elasticity-slider" min="0" max="1" value="1" step="0.01"
                               oninput="physicsExperiments.currentExperiment.setParameter('elasticity', this.value)">
                        <span id="elasticity-value">1</span>
                    </div>

                    <div class="momentum-display">
                        <h4>الزخم</h4>
                        <div id="momentum-values">
                            <div>الزخم الأولي: <span id="initial-momentum">0</span> كغ⋅م/ث</div>
                            <div>الزخم النهائي: <span id="final-momentum">0</span> كغ⋅م/ث</div>
                            <div>الطاقة الحركية الأولية: <span id="initial-ke">0</span> J</div>
                            <div>الطاقة الحركية النهائية: <span id="final-ke">0</span> J</div>
                        </div>
                    </div>

                    <div class="control-buttons">
                        <button onclick="physicsExperiments.currentExperiment.resetExperiment()" class="btn-reset">
                            <i class="fas fa-redo"></i> إعادة تعيين
                        </button>
                        <button onclick="physicsExperiments.currentExperiment.simulateCollision()" class="btn-simulate">
                            <i class="fas fa-play"></i> محاكاة التصادم
                        </button>
                    </div>
                </div>
            `;
        }
    }

    update() {
        if (!this.isRunning) return;

        const dt = 0.016;

        this.balls.forEach(ball => {
            // تحديث الموقع
            ball.x += ball.vx * dt;
            ball.y += ball.vy * dt;

            // إضافة للمسار
            ball.trail.push({ x: ball.x, y: ball.y });
            if (ball.trail.length > 50) {
                ball.trail.shift();
            }

            // التحقق من الحدود
            if (ball.x - ball.radius < 0) {
                ball.x = ball.radius;
                ball.vx = -ball.vx * 0.8;
            }
            if (ball.x + ball.radius > this.canvas.width) {
                ball.x = this.canvas.width - ball.radius;
                ball.vx = -ball.vx * 0.8;
            }
            if (ball.y - ball.radius < 0) {
                ball.y = ball.radius;
                ball.vy = -ball.vy * 0.8;
            }
            if (ball.y + ball.radius > this.canvas.height) {
                ball.y = this.canvas.height - ball.radius;
                ball.vy = -ball.vy * 0.8;
            }
        });

        // التحقق من التصادم
        this.checkCollisions();

        // تحديث عرض الزخم
        this.updateMomentumDisplay();

        // تسجيل البيانات
        const currentTime = (Date.now() - this.startTime) / 1000;
        this.recordData(currentTime, {
            ball1_x: this.balls[0].x,
            ball1_vx: this.balls[0].vx / 20,
            ball2_x: this.balls[1].x,
            ball2_vx: this.balls[1].vx / 20,
            totalMomentum: this.calculateTotalMomentum(),
            totalKineticEnergy: this.calculateTotalKineticEnergy()
        });
    }

    checkCollisions() {
        const ball1 = this.balls[0];
        const ball2 = this.balls[1];

        const dx = ball2.x - ball1.x;
        const dy = ball2.y - ball1.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        const minDistance = ball1.radius + ball2.radius;

        if (distance < minDistance) {
            // حساب السرعات بعد التصادم
            this.resolveCollision(ball1, ball2);

            // تسجيل التصادم
            this.collisionHistory.push({
                time: (Date.now() - this.startTime) / 1000,
                ball1Velocity: ball1.vx / 20,
                ball2Velocity: ball2.vx / 20,
                momentum: this.calculateTotalMomentum(),
                kineticEnergy: this.calculateTotalKineticEnergy()
            });
        }
    }

    resolveCollision(ball1, ball2) {
        const dx = ball2.x - ball1.x;
        const dy = ball2.y - ball1.y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        if (distance === 0) return;

        // وحدة المتجه العادي
        const nx = dx / distance;
        const ny = dy / distance;

        // السرعات النسبية
        const dvx = ball2.vx - ball1.vx;
        const dvy = ball2.vy - ball1.vy;

        // السرعة النسبية في اتجاه التصادم
        const dvn = dvx * nx + dvy * ny;

        // لا تحل التصادم إذا كانت الكرات تتحرك بعيداً عن بعضها
        if (dvn > 0) return;

        // معامل المرونة
        const e = this.getParameter('elasticity');

        // الدفع
        const impulse = 2 * dvn / (ball1.mass + ball2.mass);

        // تحديث السرعات
        ball1.vx += impulse * ball2.mass * nx * (1 + e);
        ball1.vy += impulse * ball2.mass * ny * (1 + e);
        ball2.vx -= impulse * ball1.mass * nx * (1 + e);
        ball2.vy -= impulse * ball1.mass * ny * (1 + e);

        // فصل الكرات
        const overlap = ball1.radius + ball2.radius - distance;
        const separationX = nx * overlap * 0.5;
        const separationY = ny * overlap * 0.5;

        ball1.x -= separationX;
        ball1.y -= separationY;
        ball2.x += separationX;
        ball2.y += separationY;
    }

    draw() {
        if (!this.ctx) return;

        // مسح الشاشة
        this.ctx.fillStyle = '#f0f8ff';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // رسم الشبكة
        this.drawGrid();

        // رسم المسارات
        this.balls.forEach(ball => {
            this.drawTrail(ball);
        });

        // رسم الكرات
        this.balls.forEach(ball => {
            this.drawBall(ball);
        });

        // رسم متجهات السرعة
        if (this.showVectors) {
            this.balls.forEach(ball => {
                this.drawVelocityVector(ball);
            });
        }

        // رسم المعلومات
        this.drawInfo();
    }

    drawBall(ball) {
        // رسم الكرة
        this.ctx.fillStyle = ball.color;
        this.ctx.beginPath();
        this.ctx.arc(ball.x, ball.y, ball.radius, 0, 2 * Math.PI);
        this.ctx.fill();

        // رسم الحدود
        this.ctx.strokeStyle = '#2d3748';
        this.ctx.lineWidth = 2;
        this.ctx.stroke();

        // رسم الكتلة
        this.ctx.fillStyle = '#fff';
        this.ctx.font = '12px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(`${ball.mass}kg`, ball.x, ball.y + 4);
    }

    drawTrail(ball) {
        if (ball.trail.length < 2) return;

        this.ctx.strokeStyle = ball.color;
        this.ctx.globalAlpha = 0.3;
        this.ctx.lineWidth = 2;
        this.ctx.beginPath();
        this.ctx.moveTo(ball.trail[0].x, ball.trail[0].y);

        for (let i = 1; i < ball.trail.length; i++) {
            this.ctx.lineTo(ball.trail[i].x, ball.trail[i].y);
        }
        this.ctx.stroke();
        this.ctx.globalAlpha = 1;
    }

    drawVelocityVector(ball) {
        const scale = 0.1;
        const vx = ball.vx * scale;
        const vy = ball.vy * scale;

        this.ctx.strokeStyle = '#48bb78';
        this.ctx.lineWidth = 3;
        this.ctx.beginPath();
        this.ctx.moveTo(ball.x, ball.y);
        this.ctx.lineTo(ball.x + vx, ball.y + vy);
        this.ctx.stroke();

        // رأس السهم
        if (Math.abs(vx) > 2 || Math.abs(vy) > 2) {
            this.drawArrowHead(ball.x + vx, ball.y + vy, Math.atan2(vy, vx), '#48bb78');
        }
    }

    drawInfo() {
        this.ctx.fillStyle = '#2d3748';
        this.ctx.font = '14px Arial';
        this.ctx.textAlign = 'left';

        const momentum = this.calculateTotalMomentum();
        const kineticEnergy = this.calculateTotalKineticEnergy();

        const info = [
            `الزخم الكلي: ${momentum.toFixed(2)} كغ⋅م/ث`,
            `الطاقة الحركية الكلية: ${kineticEnergy.toFixed(2)} J`,
            `عدد التصادمات: ${this.collisionHistory.length}`
        ];

        info.forEach((text, index) => {
            this.ctx.fillText(text, 10, 20 + index * 20);
        });
    }

    calculateTotalMomentum() {
        return this.balls.reduce((total, ball) => {
            return total + ball.mass * (ball.vx / 20);
        }, 0);
    }

    calculateTotalKineticEnergy() {
        return this.balls.reduce((total, ball) => {
            const v = Math.sqrt(ball.vx * ball.vx + ball.vy * ball.vy) / 20;
            return total + 0.5 * ball.mass * v * v;
        }, 0);
    }

    updateMomentumDisplay() {
        const momentum = this.calculateTotalMomentum();
        const kineticEnergy = this.calculateTotalKineticEnergy();

        const momentumSpan = document.getElementById('final-momentum');
        const keSpan = document.getElementById('final-ke');

        if (momentumSpan) momentumSpan.textContent = momentum.toFixed(3);
        if (keSpan) keSpan.textContent = kineticEnergy.toFixed(3);
    }

    simulateCollision() {
        // وضع الكرات في مواقع التصادم
        this.balls[0].x = 300;
        this.balls[0].y = 300;
        this.balls[1].x = 500;
        this.balls[1].y = 300;

        // تطبيق السرعات
        this.balls[0].vx = this.getParameter('ball1Velocity') * 20;
        this.balls[1].vx = this.getParameter('ball2Velocity') * 20;

        // مسح المسارات
        this.balls.forEach(ball => {
            ball.trail = [];
        });

        this.collisionHistory = [];
        this.startTime = Date.now();
    }

    onParameterChange(name, value) {
        const valueSpan = document.getElementById(name.replace(/([A-Z])/g, '-$1').toLowerCase() + '-value');
        if (valueSpan) {
            valueSpan.textContent = value;
        }

        // تحديث خصائص الكرات
        if (name === 'ball1Mass') {
            this.balls[0].mass = parseFloat(value);
            this.balls[0].radius = Math.max(15, parseFloat(value) * 8);
        } else if (name === 'ball2Mass') {
            this.balls[1].mass = parseFloat(value);
            this.balls[1].radius = Math.max(15, parseFloat(value) * 8);
        }
    }

    resetExperiment() {
        this.initializeBalls();
        this.collisionHistory = [];
        this.startTime = Date.now();
        this.data = [];
    }

    drawArrowHead(x, y, angle, color) {
        this.ctx.fillStyle = color;
        this.ctx.save();
        this.ctx.translate(x, y);
        this.ctx.rotate(angle);
        this.ctx.beginPath();
        this.ctx.moveTo(0, 0);
        this.ctx.lineTo(-8, -4);
        this.ctx.lineTo(-8, 4);
        this.ctx.closePath();
        this.ctx.fill();
        this.ctx.restore();
    }

    drawGrid() {
        this.ctx.strokeStyle = '#e0e0e0';
        this.ctx.lineWidth = 1;

        for (let x = 0; x <= this.canvas.width; x += 20) {
            this.ctx.beginPath();
            this.ctx.moveTo(x, 0);
            this.ctx.lineTo(x, this.canvas.height);
            this.ctx.stroke();
        }

        for (let y = 0; y <= this.canvas.height; y += 20) {
            this.ctx.beginPath();
            this.ctx.moveTo(0, y);
            this.ctx.lineTo(this.canvas.width, y);
            this.ctx.stroke();
        }
    }
}

// تجربة الاهتزازات - Oscillation Experiment
class OscillationExperiment extends PhysicsExperiment {
    constructor() {
        super('oscillation', 'Simple Harmonic Motion', 'الحركة التوافقية البسيطة', 'mechanics');
        this.spring = {
            x: 400,
            y: 100,
            naturalLength: 200,
            currentLength: 200,
            k: 50, // ثابت النابض
            mass: 1,
            position: 0,
            velocity: 0,
            amplitude: 0,
            frequency: 0,
            period: 0
        };
        this.oscillationData = [];
        this.showGraph = false;
    }

    initializeParameters() {
        this.setParameter('springConstant', 50);
        this.setParameter('mass', 1);
        this.setParameter('damping', 0.01);
        this.setParameter('amplitude', 50);
        this.setParameter('showGraph', false);
    }

    setupControls() {
        const controlsContainer = document.getElementById('experiment-controls');
        if (controlsContainer) {
            controlsContainer.innerHTML = `
                <div class="control-panel">
                    <h3>متحكمات النابض</h3>

                    <div class="control-group">
                        <label>ثابت النابض (N/m)</label>
                        <input type="range" id="spring-constant-slider" min="10" max="200" value="50"
                               oninput="physicsExperiments.currentExperiment.setParameter('springConstant', this.value)">
                        <span id="spring-constant-value">50</span>
                    </div>

                    <div class="control-group">
                        <label>الكتلة (كغ)</label>
                        <input type="range" id="mass-slider" min="0.1" max="5" value="1" step="0.1"
                               oninput="physicsExperiments.currentExperiment.setParameter('mass', this.value)">
                        <span id="mass-value">1</span>
                    </div>

                    <div class="control-group">
                        <label>التخميد</label>
                        <input type="range" id="damping-slider" min="0" max="0.1" value="0.01" step="0.001"
                               oninput="physicsExperiments.currentExperiment.setParameter('damping', this.value)">
                        <span id="damping-value">0.01</span>
                    </div>

                    <div class="control-group">
                        <label>السعة الأولية (سم)</label>
                        <input type="range" id="amplitude-slider" min="10" max="100" value="50"
                               oninput="physicsExperiments.currentExperiment.setParameter('amplitude', this.value)">
                        <span id="amplitude-value">50</span>
                    </div>

                    <div class="oscillation-display">
                        <h4>خصائص الاهتزاز</h4>
                        <div id="oscillation-values">
                            <div>التردد: <span id="frequency-display">0</span> Hz</div>
                            <div>الدورة: <span id="period-display">0</span> s</div>
                            <div>السعة: <span id="amplitude-display">0</span> cm</div>
                            <div>الطاقة الكلية: <span id="total-energy-display">0</span> J</div>
                        </div>
                    </div>

                    <div class="control-buttons">
                        <button onclick="physicsExperiments.currentExperiment.resetExperiment()" class="btn-reset">
                            <i class="fas fa-redo"></i> إعادة تعيين
                        </button>
                        <button onclick="physicsExperiments.currentExperiment.toggleGraph()" class="btn-graph">
                            <i class="fas fa-chart-line"></i> رسم الإزاحة
                        </button>
                        <button onclick="physicsExperiments.currentExperiment.addImpulse()" class="btn-impulse">
                            <i class="fas fa-hand-paper"></i> دفعة
                        </button>
                    </div>
                </div>
            `;
        }
    }

    start() {
        super.start();
        this.resetSpring();
    }

    resetSpring() {
        this.spring.k = this.getParameter('springConstant');
        this.spring.mass = this.getParameter('mass');
        this.spring.position = this.getParameter('amplitude');
        this.spring.velocity = 0;
        this.spring.currentLength = this.spring.naturalLength + this.spring.position;

        // حساب التردد والدورة
        this.spring.frequency = Math.sqrt(this.spring.k / this.spring.mass) / (2 * Math.PI);
        this.spring.period = 1 / this.spring.frequency;

        this.oscillationData = [];
        this.updateDisplay();
    }

    update() {
        if (!this.isRunning) return;

        const dt = 0.016;
        const k = this.getParameter('springConstant');
        const m = this.getParameter('mass');
        const damping = this.getParameter('damping');

        // قوة النابض: F = -kx
        const springForce = -k * this.spring.position;

        // قوة التخميد: F = -bv
        const dampingForce = -damping * this.spring.velocity;

        // القوة الإجمالية
        const totalForce = springForce + dampingForce;

        // التسارع: a = F/m
        const acceleration = totalForce / m;

        // تحديث السرعة والموقع
        this.spring.velocity += acceleration * dt;
        this.spring.position += this.spring.velocity * dt;

        // تحديث طول النابض
        this.spring.currentLength = this.spring.naturalLength + this.spring.position;

        // حساب الطاقات
        const kineticEnergy = 0.5 * m * this.spring.velocity * this.spring.velocity;
        const potentialEnergy = 0.5 * k * this.spring.position * this.spring.position;
        const totalEnergy = kineticEnergy + potentialEnergy;

        // تسجيل البيانات
        const currentTime = (Date.now() - this.startTime) / 1000;
        this.oscillationData.push({
            time: currentTime,
            position: this.spring.position,
            velocity: this.spring.velocity,
            acceleration: acceleration,
            kineticEnergy: kineticEnergy,
            potentialEnergy: potentialEnergy,
            totalEnergy: totalEnergy
        });

        // الحد من طول البيانات
        if (this.oscillationData.length > 500) {
            this.oscillationData.shift();
        }

        this.recordData(currentTime, {
            position: this.spring.position,
            velocity: this.spring.velocity,
            acceleration: acceleration,
            kineticEnergy: kineticEnergy,
            potentialEnergy: potentialEnergy,
            totalEnergy: totalEnergy
        });

        this.updateDisplay();
    }

    draw() {
        if (!this.ctx) return;

        // مسح الشاشة
        this.ctx.fillStyle = '#f0f8ff';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        if (this.showGraph) {
            this.drawGraph();
        } else {
            this.drawSpring();
        }
    }

    drawSpring() {
        const springX = this.spring.x;
        const springY = this.spring.y;
        const springLength = this.spring.currentLength;
        const massY = springY + springLength;

        // رسم نقطة التثبيت
        this.ctx.fillStyle = '#2d3748';
        this.ctx.fillRect(springX - 20, springY - 10, 40, 10);

        // رسم النابض
        this.drawSpringCoil(springX, springY, springX, massY);

        // رسم الكتلة
        const massSize = Math.max(20, this.spring.mass * 15);
        this.ctx.fillStyle = '#e53e3e';
        this.ctx.fillRect(springX - massSize/2, massY - massSize/2, massSize, massSize);

        // رسم الكتلة
        this.ctx.fillStyle = '#fff';
        this.ctx.font = '12px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(`${this.spring.mass}kg`, springX, massY + 4);

        // رسم خط التوازن
        const equilibriumY = this.spring.y + this.spring.naturalLength;
        this.ctx.strokeStyle = '#48bb78';
        this.ctx.lineWidth = 2;
        this.ctx.setLineDash([10, 5]);
        this.ctx.beginPath();
        this.ctx.moveTo(springX - 50, equilibriumY);
        this.ctx.lineTo(springX + 50, equilibriumY);
        this.ctx.stroke();
        this.ctx.setLineDash([]);

        // تسمية خط التوازن
        this.ctx.fillStyle = '#48bb78';
        this.ctx.font = '12px Arial';
        this.ctx.fillText('موضع التوازن', springX + 60, equilibriumY + 4);

        // رسم متجه السرعة
        this.drawVelocityVector(springX, massY);

        // رسم متجه القوة
        this.drawForceVector(springX, massY);

        // رسم المعلومات
        this.drawSpringInfo();
    }

    drawSpringCoil(x1, y1, x2, y2) {
        const coils = 8;
        const amplitude = 15;
        const length = y2 - y1;
        const segmentLength = length / (coils * 4);

        this.ctx.strokeStyle = '#4a5568';
        this.ctx.lineWidth = 3;
        this.ctx.beginPath();
        this.ctx.moveTo(x1, y1);

        for (let i = 0; i <= coils * 4; i++) {
            const y = y1 + i * segmentLength;
            const x = x1 + (i % 4 === 1 ? amplitude : i % 4 === 3 ? -amplitude : 0);
            this.ctx.lineTo(x, y);
        }

        this.ctx.stroke();
    }

    drawVelocityVector(x, y) {
        const scale = 2;
        const vy = this.spring.velocity * scale;

        if (Math.abs(vy) > 2) {
            this.ctx.strokeStyle = '#4299e1';
            this.ctx.lineWidth = 3;
            this.ctx.beginPath();
            this.ctx.moveTo(x + 30, y);
            this.ctx.lineTo(x + 30, y + vy);
            this.ctx.stroke();

            this.drawArrowHead(x + 30, y + vy, vy > 0 ? Math.PI/2 : -Math.PI/2, '#4299e1');
        }
    }

    drawForceVector(x, y) {
        const scale = 0.5;
        const force = -this.getParameter('springConstant') * this.spring.position;
        const fy = force * scale;

        if (Math.abs(fy) > 2) {
            this.ctx.strokeStyle = '#f56565';
            this.ctx.lineWidth = 3;
            this.ctx.beginPath();
            this.ctx.moveTo(x - 30, y);
            this.ctx.lineTo(x - 30, y + fy);
            this.ctx.stroke();

            this.drawArrowHead(x - 30, y + fy, fy > 0 ? Math.PI/2 : -Math.PI/2, '#f56565');
        }
    }

    drawSpringInfo() {
        this.ctx.fillStyle = '#2d3748';
        this.ctx.font = '14px Arial';
        this.ctx.textAlign = 'left';

        const info = [
            `الإزاحة: ${this.spring.position.toFixed(1)} سم`,
            `السرعة: ${this.spring.velocity.toFixed(1)} سم/ث`,
            `التردد: ${this.spring.frequency.toFixed(2)} Hz`,
            `الدورة: ${this.spring.period.toFixed(2)} ث`
        ];

        info.forEach((text, index) => {
            this.ctx.fillText(text, 10, 20 + index * 20);
        });

        // مفاتيح المتجهات
        this.ctx.fillStyle = '#4299e1';
        this.ctx.fillText('← السرعة', 500, 20);
        this.ctx.fillStyle = '#f56565';
        this.ctx.fillText('← القوة', 500, 40);
    }

    drawGraph() {
        if (this.oscillationData.length < 2) return;

        const margin = 50;
        const graphWidth = this.canvas.width - 2 * margin;
        const graphHeight = this.canvas.height - 2 * margin;

        // رسم المحاور
        this.ctx.strokeStyle = '#2d3748';
        this.ctx.lineWidth = 2;
        this.ctx.beginPath();
        this.ctx.moveTo(margin, margin);
        this.ctx.lineTo(margin, margin + graphHeight);
        this.ctx.lineTo(margin + graphWidth, margin + graphHeight);
        this.ctx.stroke();

        // العثور على القيم القصوى
        const maxPosition = Math.max(...this.oscillationData.map(d => Math.abs(d.position)));
        const minTime = this.oscillationData[0].time;
        const maxTime = this.oscillationData[this.oscillationData.length - 1].time;

        // رسم الإزاحة
        this.drawOscillationLine(
            this.oscillationData.map(d => d.position),
            '#e53e3e',
            maxPosition,
            minTime,
            maxTime,
            margin,
            graphWidth,
            graphHeight
        );

        // رسم السرعة
        const maxVelocity = Math.max(...this.oscillationData.map(d => Math.abs(d.velocity)));
        this.drawOscillationLine(
            this.oscillationData.map(d => d.velocity),
            '#4299e1',
            maxVelocity,
            minTime,
            maxTime,
            margin,
            graphWidth,
            graphHeight
        );

        // رسم المفاتيح
        this.drawOscillationLegend(margin, margin - 30);
    }

    drawOscillationLine(data, color, maxValue, minTime, maxTime, margin, graphWidth, graphHeight) {
        this.ctx.strokeStyle = color;
        this.ctx.lineWidth = 2;
        this.ctx.beginPath();

        data.forEach((value, index) => {
            const time = this.oscillationData[index].time;
            const x = margin + (time - minTime) / (maxTime - minTime) * graphWidth;
            const y = margin + graphHeight/2 - (value / maxValue) * (graphHeight/2 - 20);

            if (index === 0) {
                this.ctx.moveTo(x, y);
            } else {
                this.ctx.lineTo(x, y);
            }
        });

        this.ctx.stroke();
    }

    drawOscillationLegend(x, y) {
        const legends = [
            { color: '#e53e3e', text: 'الإزاحة' },
            { color: '#4299e1', text: 'السرعة' }
        ];

        legends.forEach((legend, index) => {
            const legendX = x + index * 100;

            this.ctx.strokeStyle = legend.color;
            this.ctx.lineWidth = 3;
            this.ctx.beginPath();
            this.ctx.moveTo(legendX, y);
            this.ctx.lineTo(legendX + 20, y);
            this.ctx.stroke();

            this.ctx.fillStyle = '#2d3748';
            this.ctx.font = '12px Arial';
            this.ctx.fillText(legend.text, legendX + 25, y + 4);
        });
    }

    updateDisplay() {
        const frequencySpan = document.getElementById('frequency-display');
        const periodSpan = document.getElementById('period-display');
        const amplitudeSpan = document.getElementById('amplitude-display');
        const energySpan = document.getElementById('total-energy-display');

        if (frequencySpan) frequencySpan.textContent = this.spring.frequency.toFixed(3);
        if (periodSpan) periodSpan.textContent = this.spring.period.toFixed(3);
        if (amplitudeSpan) amplitudeSpan.textContent = Math.abs(this.spring.position).toFixed(1);

        if (energySpan && this.oscillationData.length > 0) {
            const lastData = this.oscillationData[this.oscillationData.length - 1];
            energySpan.textContent = lastData.totalEnergy.toFixed(3);
        }
    }

    toggleGraph() {
        this.showGraph = !this.showGraph;
    }

    addImpulse() {
        this.spring.velocity += 50; // إضافة دفعة
    }

    onParameterChange(name, value) {
        const valueSpan = document.getElementById(name.replace(/([A-Z])/g, '-$1').toLowerCase() + '-value');
        if (valueSpan) {
            valueSpan.textContent = value;
        }

        if (name === 'springConstant' || name === 'mass') {
            this.spring.frequency = Math.sqrt(this.getParameter('springConstant') / this.getParameter('mass')) / (2 * Math.PI);
            this.spring.period = 1 / this.spring.frequency;
            this.updateDisplay();
        }
    }

    resetExperiment() {
        this.resetSpring();
        this.startTime = Date.now();
        this.data = [];
    }

    drawArrowHead(x, y, angle, color) {
        this.ctx.fillStyle = color;
        this.ctx.save();
        this.ctx.translate(x, y);
        this.ctx.rotate(angle);
        this.ctx.beginPath();
        this.ctx.moveTo(0, 0);
        this.ctx.lineTo(-8, -4);
        this.ctx.lineTo(-8, 4);
        this.ctx.closePath();
        this.ctx.fill();
        this.ctx.restore();
    }
}

// تجربة انتقال الحرارة - Heat Transfer Experiment
class HeatTransferExperiment extends PhysicsExperiment {
    constructor() {
        super('heat-transfer', 'Heat Transfer', 'انتقال الحرارة', 'thermodynamics');
        this.objects = [];
        this.heatSources = [];
        this.temperatureField = [];
        this.showTemperatureField = true;
    }

    initializeParameters() {
        this.setParameter('ambientTemperature', 20);
        this.setParameter('heatSourceTemp', 100);
        this.setParameter('thermalConductivity', 0.5);
        this.setParameter('showField', true);

        this.initializeObjects();
        this.initializeTemperatureField();
    }

    initializeObjects() {
        this.objects = [
            {
                id: 1,
                x: 200,
                y: 300,
                width: 100,
                height: 50,
                temperature: 80,
                material: 'copper',
                thermalConductivity: 0.8,
                color: '#cd7f32'
            },
            {
                id: 2,
                x: 350,
                y: 300,
                width: 100,
                height: 50,
                temperature: 20,
                material: 'aluminum',
                thermalConductivity: 0.6,
                color: '#c0c0c0'
            },
            {
                id: 3,
                x: 500,
                y: 300,
                width: 100,
                height: 50,
                temperature: 20,
                material: 'steel',
                thermalConductivity: 0.4,
                color: '#708090'
            }
        ];

        this.heatSources = [
            {
                x: 150,
                y: 300,
                temperature: this.getParameter('heatSourceTemp'),
                radius: 30,
                power: 1000
            }
        ];
    }

    initializeTemperatureField() {
        this.temperatureField = [];
        const gridSize = 20;

        for (let x = 0; x < this.canvas.width; x += gridSize) {
            for (let y = 0; y < this.canvas.height; y += gridSize) {
                this.temperatureField.push({
                    x: x,
                    y: y,
                    temperature: this.getParameter('ambientTemperature')
                });
            }
        }
    }

    setupControls() {
        const controlsContainer = document.getElementById('experiment-controls');
        if (controlsContainer) {
            controlsContainer.innerHTML = `
                <div class="control-panel">
                    <h3>متحكمات انتقال الحرارة</h3>

                    <div class="control-group">
                        <label>درجة حرارة البيئة (°C)</label>
                        <input type="range" id="ambient-temp-slider" min="0" max="50" value="20"
                               oninput="physicsExperiments.currentExperiment.setParameter('ambientTemperature', this.value)">
                        <span id="ambient-temp-value">20</span>
                    </div>

                    <div class="control-group">
                        <label>درجة حرارة المصدر (°C)</label>
                        <input type="range" id="source-temp-slider" min="50" max="200" value="100"
                               oninput="physicsExperiments.currentExperiment.setParameter('heatSourceTemp', this.value)">
                        <span id="source-temp-value">100</span>
                    </div>

                    <div class="control-group">
                        <label>التوصيل الحراري</label>
                        <input type="range" id="conductivity-slider" min="0.1" max="1" value="0.5" step="0.1"
                               oninput="physicsExperiments.currentExperiment.setParameter('thermalConductivity', this.value)">
                        <span id="conductivity-value">0.5</span>
                    </div>

                    <div class="control-group">
                        <label>
                            <input type="checkbox" id="field-check" checked
                                   onchange="physicsExperiments.currentExperiment.setParameter('showField', this.checked)">
                            إظهار مجال درجة الحرارة
                        </label>
                    </div>

                    <div class="temperature-display">
                        <h4>درجات الحرارة</h4>
                        <div id="temp-values">
                            <div>النحاس: <span id="copper-temp">80</span> °C</div>
                            <div>الألمنيوم: <span id="aluminum-temp">20</span> °C</div>
                            <div>الفولاذ: <span id="steel-temp">20</span> °C</div>
                        </div>
                    </div>

                    <div class="control-buttons">
                        <button onclick="physicsExperiments.currentExperiment.resetExperiment()" class="btn-reset">
                            <i class="fas fa-redo"></i> إعادة تعيين
                        </button>
                        <button onclick="physicsExperiments.currentExperiment.addHeatSource()" class="btn-add">
                            <i class="fas fa-fire"></i> إضافة مصدر حرارة
                        </button>
                    </div>
                </div>
            `;
        }
    }

    update() {
        if (!this.isRunning) return;

        const dt = 0.016;
        const ambientTemp = this.getParameter('ambientTemperature');

        // تحديث درجات حرارة الأجسام
        this.objects.forEach(obj => {
            // انتقال الحرارة من المصادر
            this.heatSources.forEach(source => {
                const distance = Math.sqrt((obj.x - source.x) ** 2 + (obj.y - source.y) ** 2);
                if (distance < 100) {
                    const heatTransfer = (source.temperature - obj.temperature) * obj.thermalConductivity * dt * 0.01;
                    obj.temperature += heatTransfer;
                }
            });

            // انتقال الحرارة للبيئة
            const environmentalCooling = (obj.temperature - ambientTemp) * 0.001;
            obj.temperature -= environmentalCooling * dt;

            // انتقال الحرارة بين الأجسام
            this.objects.forEach(otherObj => {
                if (obj.id !== otherObj.id) {
                    const distance = Math.sqrt((obj.x - otherObj.x) ** 2 + (obj.y - otherObj.y) ** 2);
                    if (distance < 120) {
                        const avgConductivity = (obj.thermalConductivity + otherObj.thermalConductivity) / 2;
                        const heatTransfer = (otherObj.temperature - obj.temperature) * avgConductivity * dt * 0.005;
                        obj.temperature += heatTransfer;
                    }
                }
            });
        });

        // تحديث مجال درجة الحرارة
        this.updateTemperatureField();

        // تحديث العرض
        this.updateTemperatureDisplay();

        // تسجيل البيانات
        const currentTime = (Date.now() - this.startTime) / 1000;
        this.recordData(currentTime, {
            copperTemp: this.objects[0].temperature,
            aluminumTemp: this.objects[1].temperature,
            steelTemp: this.objects[2].temperature,
            avgTemp: this.objects.reduce((sum, obj) => sum + obj.temperature, 0) / this.objects.length
        });
    }

    updateTemperatureField() {
        this.temperatureField.forEach(point => {
            let totalInfluence = 0;
            let weightedTemp = 0;

            // تأثير المصادر الحرارية
            this.heatSources.forEach(source => {
                const distance = Math.sqrt((point.x - source.x) ** 2 + (point.y - source.y) ** 2);
                const influence = 1 / (1 + distance * 0.01);
                totalInfluence += influence;
                weightedTemp += source.temperature * influence;
            });

            // تأثير الأجسام
            this.objects.forEach(obj => {
                const distance = Math.sqrt((point.x - obj.x) ** 2 + (point.y - obj.y) ** 2);
                if (distance < 100) {
                    const influence = 1 / (1 + distance * 0.02);
                    totalInfluence += influence;
                    weightedTemp += obj.temperature * influence;
                }
            });

            // تأثير البيئة
            const ambientInfluence = 0.1;
            totalInfluence += ambientInfluence;
            weightedTemp += this.getParameter('ambientTemperature') * ambientInfluence;

            point.temperature = weightedTemp / totalInfluence;
        });
    }

    draw() {
        if (!this.ctx) return;

        // مسح الشاشة
        this.ctx.fillStyle = '#f0f8ff';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // رسم مجال درجة الحرارة
        if (this.getParameter('showField')) {
            this.drawTemperatureField();
        }

        // رسم المصادر الحرارية
        this.heatSources.forEach(source => {
            this.drawHeatSource(source);
        });

        // رسم الأجسام
        this.objects.forEach(obj => {
            this.drawObject(obj);
        });

        // رسم المعلومات
        this.drawHeatInfo();
    }

    drawTemperatureField() {
        this.temperatureField.forEach(point => {
            const normalizedTemp = (point.temperature - 0) / 200; // تطبيع من 0 إلى 200
            const hue = (1 - normalizedTemp) * 240; // من الأزرق إلى الأحمر

            this.ctx.fillStyle = `hsl(${hue}, 70%, 50%)`;
            this.ctx.globalAlpha = 0.3;
            this.ctx.fillRect(point.x - 10, point.y - 10, 20, 20);
        });
        this.ctx.globalAlpha = 1;
    }

    drawHeatSource(source) {
        // رسم المصدر
        this.ctx.fillStyle = '#ff4500';
        this.ctx.beginPath();
        this.ctx.arc(source.x, source.y, source.radius, 0, 2 * Math.PI);
        this.ctx.fill();

        // رسم الحدود
        this.ctx.strokeStyle = '#ff6347';
        this.ctx.lineWidth = 3;
        this.ctx.stroke();

        // رسم درجة الحرارة
        this.ctx.fillStyle = '#fff';
        this.ctx.font = '12px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(`${source.temperature.toFixed(0)}°C`, source.x, source.y + 4);
    }

    drawObject(obj) {
        // تحديد اللون بناءً على درجة الحرارة
        const normalizedTemp = Math.min(1, Math.max(0, (obj.temperature - 20) / 180));
        const red = Math.floor(255 * normalizedTemp);
        const blue = Math.floor(255 * (1 - normalizedTemp));
        const color = `rgb(${red}, 100, ${blue})`;

        // رسم الجسم
        this.ctx.fillStyle = color;
        this.ctx.fillRect(obj.x, obj.y, obj.width, obj.height);

        // رسم الحدود
        this.ctx.strokeStyle = '#2d3748';
        this.ctx.lineWidth = 2;
        this.ctx.strokeRect(obj.x, obj.y, obj.width, obj.height);

        // رسم المادة ودرجة الحرارة
        this.ctx.fillStyle = '#fff';
        this.ctx.font = '10px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(obj.material, obj.x + obj.width/2, obj.y + obj.height/2 - 5);
        this.ctx.fillText(`${obj.temperature.toFixed(1)}°C`, obj.x + obj.width/2, obj.y + obj.height/2 + 8);
    }

    drawHeatInfo() {
        this.ctx.fillStyle = '#2d3748';
        this.ctx.font = '14px Arial';
        this.ctx.textAlign = 'left';

        const info = [
            'انتقال الحرارة بين المواد المختلفة',
            'الألوان تمثل درجات الحرارة (أزرق = بارد، أحمر = ساخن)'
        ];

        info.forEach((text, index) => {
            this.ctx.fillText(text, 10, 20 + index * 20);
        });
    }

    updateTemperatureDisplay() {
        const copperSpan = document.getElementById('copper-temp');
        const aluminumSpan = document.getElementById('aluminum-temp');
        const steelSpan = document.getElementById('steel-temp');

        if (copperSpan) copperSpan.textContent = this.objects[0].temperature.toFixed(1);
        if (aluminumSpan) aluminumSpan.textContent = this.objects[1].temperature.toFixed(1);
        if (steelSpan) steelSpan.textContent = this.objects[2].temperature.toFixed(1);
    }

    addHeatSource() {
        const x = Math.random() * (this.canvas.width - 100) + 50;
        const y = Math.random() * (this.canvas.height - 100) + 50;

        this.heatSources.push({
            x: x,
            y: y,
            temperature: this.getParameter('heatSourceTemp'),
            radius: 25,
            power: 800
        });
    }

    onParameterChange(name, value) {
        const valueSpan = document.getElementById(name.replace(/([A-Z])/g, '-$1').toLowerCase() + '-value');
        if (valueSpan) {
            valueSpan.textContent = value;
        }

        if (name === 'heatSourceTemp') {
            this.heatSources.forEach(source => {
                source.temperature = parseFloat(value);
            });
        }
    }

    resetExperiment() {
        this.initializeObjects();
        this.initializeTemperatureField();
        this.startTime = Date.now();
        this.data = [];
    }
}

// تجربة قوانين الغازات - Gas Laws Experiment
class GasLawsExperiment extends PhysicsExperiment {
    constructor() {
        super('gas-laws', 'Gas Laws', 'قوانين الغازات', 'thermodynamics');
        this.container = {
            x: 200,
            y: 150,
            width: 400,
            height: 300,
            volume: 0,
            pressure: 0,
            temperature: 300, // كلفن
            gasAmount: 1 // مول
        };
        this.molecules = [];
        this.currentLaw = 'boyle'; // boyle, charles, gay-lussac, ideal
    }

    initializeParameters() {
        this.setParameter('temperature', 300);
        this.setParameter('pressure', 101325);
        this.setParameter('volume', 0.024);
        this.setParameter('gasAmount', 1);
        this.setParameter('currentLaw', 'boyle');

        this.initializeMolecules();
        this.calculateProperties();
    }

    initializeMolecules() {
        this.molecules = [];
        const numMolecules = Math.floor(this.getParameter('gasAmount') * 50);

        for (let i = 0; i < numMolecules; i++) {
            this.molecules.push({
                x: this.container.x + Math.random() * this.container.width,
                y: this.container.y + Math.random() * this.container.height,
                vx: (Math.random() - 0.5) * 200,
                vy: (Math.random() - 0.5) * 200,
                radius: 3,
                mass: 1
            });
        }
    }

    calculateProperties() {
        const R = 8.314; // ثابت الغاز العام
        const T = this.getParameter('temperature');
        const n = this.getParameter('gasAmount');

        // حساب الحجم من أبعاد الحاوية
        this.container.volume = (this.container.width * this.container.height) / 1000000; // تحويل للمتر المكعب

        // تطبيق قانون الغاز المثالي PV = nRT
        this.container.pressure = (n * R * T) / this.container.volume;

        // تحديث سرعات الجزيئات بناءً على درجة الحرارة
        const avgSpeed = Math.sqrt(3 * R * T / 0.029); // للهواء
        this.molecules.forEach(molecule => {
            const speedFactor = avgSpeed / 200;
            molecule.vx *= speedFactor;
            molecule.vy *= speedFactor;
        });
    }

    setupControls() {
        const controlsContainer = document.getElementById('experiment-controls');
        if (controlsContainer) {
            controlsContainer.innerHTML = `
                <div class="control-panel">
                    <h3>متحكمات قوانين الغازات</h3>

                    <div class="control-group">
                        <label>القانون المدروس</label>
                        <select id="law-select" onchange="physicsExperiments.currentExperiment.setParameter('currentLaw', this.value)">
                            <option value="boyle">قانون بويل (P ∝ 1/V)</option>
                            <option value="charles">قانون شارل (V ∝ T)</option>
                            <option value="gay-lussac">قانون جاي لوساك (P ∝ T)</option>
                            <option value="ideal">قانون الغاز المثالي (PV = nRT)</option>
                        </select>
                    </div>

                    <div class="control-group">
                        <label>درجة الحرارة (K)</label>
                        <input type="range" id="temperature-slider" min="200" max="500" value="300"
                               oninput="physicsExperiments.currentExperiment.setParameter('temperature', this.value)">
                        <span id="temperature-value">300</span>
                    </div>

                    <div class="control-group">
                        <label>الحجم (L)</label>
                        <input type="range" id="volume-slider" min="10" max="50" value="24"
                               oninput="physicsExperiments.currentExperiment.changeVolume(this.value)">
                        <span id="volume-value">24</span>
                    </div>

                    <div class="control-group">
                        <label>كمية الغاز (mol)</label>
                        <input type="range" id="gas-amount-slider" min="0.5" max="3" value="1" step="0.1"
                               oninput="physicsExperiments.currentExperiment.setParameter('gasAmount', this.value)">
                        <span id="gas-amount-value">1</span>
                    </div>

                    <div class="gas-properties">
                        <h4>خصائص الغاز</h4>
                        <div id="gas-values">
                            <div>الضغط: <span id="pressure-display">0</span> Pa</div>
                            <div>الحجم: <span id="volume-display">0</span> L</div>
                            <div>درجة الحرارة: <span id="temp-display">0</span> K</div>
                            <div>عدد الجزيئات: <span id="molecules-display">0</span></div>
                        </div>
                    </div>

                    <div class="control-buttons">
                        <button onclick="physicsExperiments.currentExperiment.resetExperiment()" class="btn-reset">
                            <i class="fas fa-redo"></i> إعادة تعيين
                        </button>
                        <button onclick="physicsExperiments.currentExperiment.demonstrateLaw()" class="btn-demo">
                            <i class="fas fa-play"></i> عرض القانون
                        </button>
                    </div>
                </div>
            `;
        }
    }

    update() {
        if (!this.isRunning) return;

        const dt = 0.016;

        // تحديث مواقع الجزيئات
        this.molecules.forEach(molecule => {
            molecule.x += molecule.vx * dt;
            molecule.y += molecule.vy * dt;

            // التصادم مع الجدران
            if (molecule.x <= this.container.x + molecule.radius) {
                molecule.x = this.container.x + molecule.radius;
                molecule.vx = -molecule.vx;
            }
            if (molecule.x >= this.container.x + this.container.width - molecule.radius) {
                molecule.x = this.container.x + this.container.width - molecule.radius;
                molecule.vx = -molecule.vx;
            }
            if (molecule.y <= this.container.y + molecule.radius) {
                molecule.y = this.container.y + molecule.radius;
                molecule.vy = -molecule.vy;
            }
            if (molecule.y >= this.container.y + this.container.height - molecule.radius) {
                molecule.y = this.container.y + this.container.height - molecule.radius;
                molecule.vy = -molecule.vy;
            }
        });

        // حساب الضغط من التصادمات
        this.calculatePressureFromCollisions();

        // تحديث العرض
        this.updateGasDisplay();

        // تسجيل البيانات
        const currentTime = (Date.now() - this.startTime) / 1000;
        this.recordData(currentTime, {
            pressure: this.container.pressure,
            volume: this.container.volume * 1000, // لتر
            temperature: this.getParameter('temperature'),
            gasAmount: this.getParameter('gasAmount'),
            pv_product: this.container.pressure * this.container.volume
        });
    }

    calculatePressureFromCollisions() {
        // حساب مبسط للضغط بناءً على الطاقة الحركية للجزيئات
        let totalKineticEnergy = 0;
        this.molecules.forEach(molecule => {
            const speed = Math.sqrt(molecule.vx * molecule.vx + molecule.vy * molecule.vy);
            totalKineticEnergy += 0.5 * molecule.mass * speed * speed;
        });

        const avgKineticEnergy = totalKineticEnergy / this.molecules.length;
        const volume = this.container.volume;

        // P = (2/3) * (N/V) * <KE>
        this.container.pressure = (2/3) * (this.molecules.length / volume) * avgKineticEnergy * 100;
    }

    draw() {
        if (!this.ctx) return;

        // مسح الشاشة
        this.ctx.fillStyle = '#f0f8ff';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // رسم الحاوية
        this.drawContainer();

        // رسم الجزيئات
        this.molecules.forEach(molecule => {
            this.drawMolecule(molecule);
        });

        // رسم المعلومات
        this.drawGasInfo();

        // رسم القانون الحالي
        this.drawCurrentLaw();
    }

    drawContainer() {
        // رسم الحاوية
        this.ctx.strokeStyle = '#2d3748';
        this.ctx.lineWidth = 4;
        this.ctx.strokeRect(this.container.x, this.container.y, this.container.width, this.container.height);

        // رسم الخلفية
        this.ctx.fillStyle = 'rgba(173, 216, 230, 0.3)';
        this.ctx.fillRect(this.container.x, this.container.y, this.container.width, this.container.height);

        // رسم مقياس الضغط
        this.drawPressureGauge();
    }

    drawPressureGauge() {
        const gaugeX = this.container.x + this.container.width + 20;
        const gaugeY = this.container.y + 50;
        const gaugeHeight = 200;

        // إطار المقياس
        this.ctx.strokeStyle = '#2d3748';
        this.ctx.lineWidth = 2;
        this.ctx.strokeRect(gaugeX, gaugeY, 30, gaugeHeight);

        // مستوى الضغط
        const pressureRatio = Math.min(1, this.container.pressure / 200000);
        const fillHeight = pressureRatio * gaugeHeight;

        this.ctx.fillStyle = `hsl(${(1-pressureRatio) * 120}, 70%, 50%)`;
        this.ctx.fillRect(gaugeX + 2, gaugeY + gaugeHeight - fillHeight, 26, fillHeight);

        // تسمية
        this.ctx.fillStyle = '#2d3748';
        this.ctx.font = '12px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText('الضغط', gaugeX + 15, gaugeY - 10);
    }

    drawMolecule(molecule) {
        // تحديد اللون بناءً على السرعة (درجة الحرارة)
        const speed = Math.sqrt(molecule.vx * molecule.vx + molecule.vy * molecule.vy);
        const normalizedSpeed = Math.min(1, speed / 300);
        const hue = (1 - normalizedSpeed) * 240; // من الأزرق إلى الأحمر

        this.ctx.fillStyle = `hsl(${hue}, 70%, 50%)`;
        this.ctx.beginPath();
        this.ctx.arc(molecule.x, molecule.y, molecule.radius, 0, 2 * Math.PI);
        this.ctx.fill();

        // رسم مسار الحركة
        this.ctx.strokeStyle = `hsl(${hue}, 70%, 50%)`;
        this.ctx.globalAlpha = 0.3;
        this.ctx.lineWidth = 1;
        this.ctx.beginPath();
        this.ctx.moveTo(molecule.x, molecule.y);
        this.ctx.lineTo(molecule.x - molecule.vx * 0.1, molecule.y - molecule.vy * 0.1);
        this.ctx.stroke();
        this.ctx.globalAlpha = 1;
    }

    drawGasInfo() {
        this.ctx.fillStyle = '#2d3748';
        this.ctx.font = '14px Arial';
        this.ctx.textAlign = 'left';

        const info = [
            `الضغط: ${(this.container.pressure / 1000).toFixed(1)} kPa`,
            `الحجم: ${(this.container.volume * 1000).toFixed(1)} L`,
            `درجة الحرارة: ${this.getParameter('temperature')} K`,
            `عدد الجزيئات: ${this.molecules.length}`
        ];

        info.forEach((text, index) => {
            this.ctx.fillText(text, 10, 20 + index * 20);
        });
    }

    drawCurrentLaw() {
        const laws = {
            'boyle': 'قانون بويل: P₁V₁ = P₂V₂ (عند ثبوت T)',
            'charles': 'قانون شارل: V₁/T₁ = V₂/T₂ (عند ثبوت P)',
            'gay-lussac': 'قانون جاي لوساك: P₁/T₁ = P₂/T₂ (عند ثبوت V)',
            'ideal': 'قانون الغاز المثالي: PV = nRT'
        };

        const currentLaw = this.getParameter('currentLaw');

        this.ctx.fillStyle = '#4299e1';
        this.ctx.font = '16px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(laws[currentLaw], this.canvas.width / 2, this.canvas.height - 30);
    }

    changeVolume(newVolume) {
        const volumeInM3 = newVolume / 1000; // تحويل من لتر إلى متر مكعب

        // تغيير أبعاد الحاوية
        const aspectRatio = this.container.width / this.container.height;
        const newArea = volumeInM3 * 1000000; // تحويل للبكسل المربع

        this.container.height = Math.sqrt(newArea / aspectRatio);
        this.container.width = newArea / this.container.height;

        // التأكد من أن الحاوية لا تخرج من الشاشة
        this.container.width = Math.min(this.container.width, 500);
        this.container.height = Math.min(this.container.height, 350);

        // إعادة توزيع الجزيئات
        this.molecules.forEach(molecule => {
            molecule.x = this.container.x + Math.random() * this.container.width;
            molecule.y = this.container.y + Math.random() * this.container.height;
        });

        this.calculateProperties();
    }

    demonstrateLaw() {
        const currentLaw = this.getParameter('currentLaw');

        switch(currentLaw) {
            case 'boyle':
                this.demonstrateBoyleLaw();
                break;
            case 'charles':
                this.demonstrateCharlesLaw();
                break;
            case 'gay-lussac':
                this.demonstrateGayLussacLaw();
                break;
            case 'ideal':
                this.demonstrateIdealGasLaw();
                break;
        }
    }

    demonstrateBoyleLaw() {
        // تقليل الحجم تدريجياً لإظهار زيادة الضغط
        let currentVolume = 40;
        const interval = setInterval(() => {
            if (currentVolume > 15) {
                currentVolume -= 2;
                this.changeVolume(currentVolume);
                document.getElementById('volume-slider').value = currentVolume;
                document.getElementById('volume-value').textContent = currentVolume;
            } else {
                clearInterval(interval);
            }
        }, 500);
    }

    demonstrateCharlesLaw() {
        // زيادة درجة الحرارة تدريجياً لإظهار زيادة الحجم
        let currentTemp = 250;
        const interval = setInterval(() => {
            if (currentTemp < 450) {
                currentTemp += 20;
                this.setParameter('temperature', currentTemp);
                document.getElementById('temperature-slider').value = currentTemp;
                document.getElementById('temperature-value').textContent = currentTemp;
            } else {
                clearInterval(interval);
            }
        }, 500);
    }

    demonstrateGayLussacLaw() {
        // زيادة درجة الحرارة لإظهار زيادة الضغط
        let currentTemp = 250;
        const interval = setInterval(() => {
            if (currentTemp < 450) {
                currentTemp += 20;
                this.setParameter('temperature', currentTemp);
                document.getElementById('temperature-slider').value = currentTemp;
                document.getElementById('temperature-value').textContent = currentTemp;
            } else {
                clearInterval(interval);
            }
        }, 500);
    }

    demonstrateIdealGasLaw() {
        // تغيير جميع المتغيرات لإظهار العلاقة
        alert('قانون الغاز المثالي: PV = nRT\nسيتم تغيير جميع المتغيرات لإظهار العلاقة');
    }

    updateGasDisplay() {
        const pressureSpan = document.getElementById('pressure-display');
        const volumeSpan = document.getElementById('volume-display');
        const tempSpan = document.getElementById('temp-display');
        const moleculesSpan = document.getElementById('molecules-display');

        if (pressureSpan) pressureSpan.textContent = (this.container.pressure / 1000).toFixed(1);
        if (volumeSpan) volumeSpan.textContent = (this.container.volume * 1000).toFixed(1);
        if (tempSpan) tempSpan.textContent = this.getParameter('temperature');
        if (moleculesSpan) moleculesSpan.textContent = this.molecules.length;
    }

    onParameterChange(name, value) {
        const valueSpan = document.getElementById(name.replace(/([A-Z])/g, '-$1').toLowerCase() + '-value');
        if (valueSpan) {
            valueSpan.textContent = value;
        }

        if (name === 'temperature' || name === 'gasAmount') {
            this.calculateProperties();
        }

        if (name === 'gasAmount') {
            this.initializeMolecules();
        }
    }

    resetExperiment() {
        this.container.width = 400;
        this.container.height = 300;
        this.setParameter('temperature', 300);
        this.setParameter('gasAmount', 1);
        this.initializeMolecules();
        this.calculateProperties();
        this.startTime = Date.now();
        this.data = [];
    }
}

// تجربة البندول البسيط المتقدمة - Advanced Simple Pendulum
class AdvancedPendulumExperiment extends PhysicsExperiment {
    constructor() {
        super('advanced-pendulum', 'Advanced Simple Pendulum', 'البندول البسيط المتقدم', 'mechanics');
        this.pendulum = {
            pivotX: 400,
            pivotY: 100,
            length: 250,
            angle: Math.PI / 6, // 30 درجة
            angularVelocity: 0,
            angularAcceleration: 0,
            mass: 2,
            radius: 15,
            gravity: 9.8,
            damping: 0.999,
            driving: false,
            drivingFrequency: 1,
            drivingAmplitude: 0.1
        };
        this.trail = [];
        this.energyData = [];
        this.phaseData = [];
        this.showAnalysis = false;
        this.analysisType = 'energy'; // energy, phase, frequency
    }

    initializeParameters() {
        this.setParameter('length', 250);
        this.setParameter('mass', 2);
        this.setParameter('gravity', 9.8);
        this.setParameter('damping', 0.999);
        this.setParameter('initialAngle', 30);
        this.setParameter('driving', false);
        this.setParameter('drivingFrequency', 1);
        this.setParameter('drivingAmplitude', 0.1);
        this.setParameter('showTrail', true);
        this.setParameter('analysisType', 'energy');
    }

    setupControls() {
        const controlsContainer = document.getElementById('experiment-controls');
        if (controlsContainer) {
            controlsContainer.innerHTML = `
                <div class="control-panel">
                    <h3>متحكمات البندول المتقدم</h3>

                    <div class="control-group">
                        <label>طول البندول (سم)</label>
                        <input type="range" id="length-slider" min="100" max="400" value="250"
                               oninput="physicsExperiments.currentExperiment.setParameter('length', this.value)">
                        <span id="length-value">250</span>
                    </div>

                    <div class="control-group">
                        <label>الكتلة (كغ)</label>
                        <input type="range" id="mass-slider" min="0.5" max="5" value="2" step="0.1"
                               oninput="physicsExperiments.currentExperiment.setParameter('mass', this.value)">
                        <span id="mass-value">2</span>
                    </div>

                    <div class="control-group">
                        <label>الجاذبية (م/ث²)</label>
                        <input type="range" id="gravity-slider" min="1" max="20" value="9.8" step="0.1"
                               oninput="physicsExperiments.currentExperiment.setParameter('gravity', this.value)">
                        <span id="gravity-value">9.8</span>
                    </div>

                    <div class="control-group">
                        <label>التخميد</label>
                        <input type="range" id="damping-slider" min="0.98" max="1" value="0.999" step="0.001"
                               oninput="physicsExperiments.currentExperiment.setParameter('damping', this.value)">
                        <span id="damping-value">0.999</span>
                    </div>

                    <div class="control-group">
                        <label>الزاوية الأولية (درجة)</label>
                        <input type="range" id="initial-angle-slider" min="5" max="90" value="30"
                               oninput="physicsExperiments.currentExperiment.setParameter('initialAngle', this.value)">
                        <span id="initial-angle-value">30</span>
                    </div>

                    <div class="control-group">
                        <label>
                            <input type="checkbox" id="driving-check"
                                   onchange="physicsExperiments.currentExperiment.setParameter('driving', this.checked)">
                            قوة دافعة خارجية
                        </label>
                    </div>

                    <div class="driving-controls" id="driving-controls" style="display: none;">
                        <div class="control-group">
                            <label>تردد القوة الدافعة (Hz)</label>
                            <input type="range" id="driving-freq-slider" min="0.1" max="3" value="1" step="0.1"
                                   oninput="physicsExperiments.currentExperiment.setParameter('drivingFrequency', this.value)">
                            <span id="driving-freq-value">1</span>
                        </div>

                        <div class="control-group">
                            <label>سعة القوة الدافعة</label>
                            <input type="range" id="driving-amp-slider" min="0.01" max="0.5" value="0.1" step="0.01"
                                   oninput="physicsExperiments.currentExperiment.setParameter('drivingAmplitude', this.value)">
                            <span id="driving-amp-value">0.1</span>
                        </div>
                    </div>

                    <div class="analysis-controls">
                        <h4>التحليل</h4>
                        <select id="analysis-select" onchange="physicsExperiments.currentExperiment.setParameter('analysisType', this.value)">
                            <option value="energy">تحليل الطاقة</option>
                            <option value="phase">مخطط الطور</option>
                            <option value="frequency">تحليل التردد</option>
                        </select>
                    </div>

                    <div class="pendulum-info">
                        <h4>خصائص البندول</h4>
                        <div id="pendulum-properties">
                            <div>التردد الطبيعي: <span id="natural-freq">0</span> Hz</div>
                            <div>الدورة: <span id="period">0</span> s</div>
                            <div>الطاقة الكلية: <span id="total-energy">0</span> J</div>
                            <div>السعة الحالية: <span id="current-amplitude">0</span>°</div>
                        </div>
                    </div>

                    <div class="control-buttons">
                        <button onclick="physicsExperiments.currentExperiment.resetExperiment()" class="btn-reset">
                            <i class="fas fa-redo"></i> إعادة تعيين
                        </button>
                        <button onclick="physicsExperiments.currentExperiment.toggleAnalysis()" class="btn-analysis">
                            <i class="fas fa-chart-area"></i> تبديل التحليل
                        </button>
                        <button onclick="physicsExperiments.currentExperiment.addImpulse()" class="btn-impulse">
                            <i class="fas fa-hand-paper"></i> دفعة
                        </button>
                    </div>
                </div>
            `;
        }
    }

    start() {
        super.start();
        this.resetPendulum();
        this.setupMouseInteraction();
    }

    setupMouseInteraction() {
        if (!this.canvas) return;

        this.canvas.addEventListener('mousedown', (e) => {
            const rect = this.canvas.getBoundingClientRect();
            const mouseX = e.clientX - rect.left;
            const mouseY = e.clientY - rect.top;

            const bobX = this.pendulum.pivotX + this.pendulum.length * Math.sin(this.pendulum.angle);
            const bobY = this.pendulum.pivotY + this.pendulum.length * Math.cos(this.pendulum.angle);

            const distance = Math.sqrt((mouseX - bobX) ** 2 + (mouseY - bobY) ** 2);

            if (distance < this.pendulum.radius + 10) {
                this.isDragging = true;
                this.pendulum.angularVelocity = 0;
            }
        });

        this.canvas.addEventListener('mousemove', (e) => {
            if (!this.isDragging) return;

            const rect = this.canvas.getBoundingClientRect();
            const mouseX = e.clientX - rect.left;
            const mouseY = e.clientY - rect.top;

            const dx = mouseX - this.pendulum.pivotX;
            const dy = mouseY - this.pendulum.pivotY;

            this.pendulum.angle = Math.atan2(dx, dy);
        });

        this.canvas.addEventListener('mouseup', () => {
            this.isDragging = false;
        });
    }

    resetPendulum() {
        this.pendulum.angle = (this.getParameter('initialAngle') * Math.PI) / 180;
        this.pendulum.angularVelocity = 0;
        this.pendulum.angularAcceleration = 0;
        this.pendulum.length = this.getParameter('length');
        this.pendulum.mass = this.getParameter('mass');
        this.pendulum.gravity = this.getParameter('gravity');
        this.pendulum.damping = this.getParameter('damping');

        this.trail = [];
        this.energyData = [];
        this.phaseData = [];

        this.calculateNaturalFrequency();
    }

    calculateNaturalFrequency() {
        const L = this.pendulum.length / 100; // تحويل للمتر
        const g = this.pendulum.gravity;

        // للزوايا الصغيرة: f = (1/2π) * √(g/L)
        this.naturalFrequency = Math.sqrt(g / L) / (2 * Math.PI);
        this.period = 1 / this.naturalFrequency;

        this.updatePendulumInfo();
    }

    update() {
        if (!this.isRunning || this.isDragging) return;

        const dt = 0.016;
        const L = this.pendulum.length / 100; // تحويل للمتر
        const g = this.pendulum.gravity;

        // معادلة البندول: θ'' = -(g/L) * sin(θ) - b*θ' + F_driving
        let angularAcceleration = -(g / L) * Math.sin(this.pendulum.angle);

        // إضافة التخميد
        const dampingCoeff = 1 - this.pendulum.damping;
        angularAcceleration -= dampingCoeff * this.pendulum.angularVelocity;

        // إضافة القوة الدافعة
        if (this.getParameter('driving')) {
            const currentTime = (Date.now() - this.startTime) / 1000;
            const drivingForce = this.getParameter('drivingAmplitude') *
                               Math.sin(2 * Math.PI * this.getParameter('drivingFrequency') * currentTime);
            angularAcceleration += drivingForce;
        }

        // تحديث السرعة الزاوية والزاوية
        this.pendulum.angularVelocity += angularAcceleration * dt;
        this.pendulum.angle += this.pendulum.angularVelocity * dt;
        this.pendulum.angularAcceleration = angularAcceleration;

        // حساب موقع الكتلة
        const bobX = this.pendulum.pivotX + this.pendulum.length * Math.sin(this.pendulum.angle);
        const bobY = this.pendulum.pivotY + this.pendulum.length * Math.cos(this.pendulum.angle);

        // إضافة للمسار
        if (this.getParameter('showTrail')) {
            this.trail.push({ x: bobX, y: bobY, time: Date.now() });
            if (this.trail.length > 200) {
                this.trail.shift();
            }
        }

        // حساب الطاقات
        const height = L * (1 - Math.cos(this.pendulum.angle));
        const velocity = L * Math.abs(this.pendulum.angularVelocity);

        const kineticEnergy = 0.5 * this.pendulum.mass * velocity * velocity;
        const potentialEnergy = this.pendulum.mass * g * height;
        const totalEnergy = kineticEnergy + potentialEnergy;

        // تسجيل البيانات للتحليل
        const currentTime = (Date.now() - this.startTime) / 1000;

        this.energyData.push({
            time: currentTime,
            kinetic: kineticEnergy,
            potential: potentialEnergy,
            total: totalEnergy
        });

        this.phaseData.push({
            angle: this.pendulum.angle,
            angularVelocity: this.pendulum.angularVelocity
        });

        // الحد من طول البيانات
        if (this.energyData.length > 300) {
            this.energyData.shift();
            this.phaseData.shift();
        }

        // تسجيل البيانات الرئيسية
        this.recordData(currentTime, {
            angle: this.pendulum.angle * 180 / Math.PI,
            angularVelocity: this.pendulum.angularVelocity,
            angularAcceleration: angularAcceleration,
            kineticEnergy: kineticEnergy,
            potentialEnergy: potentialEnergy,
            totalEnergy: totalEnergy,
            amplitude: Math.abs(this.pendulum.angle) * 180 / Math.PI
        });

        this.updatePendulumInfo();
    }

    draw() {
        if (!this.ctx) return;

        // مسح الشاشة
        this.ctx.fillStyle = '#f0f8ff';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        if (this.showAnalysis) {
            this.drawAnalysis();
        } else {
            this.drawPendulum();
        }
    }

    drawPendulum() {
        // رسم الشبكة
        this.drawGrid();

        // رسم نقطة التعليق
        this.ctx.fillStyle = '#2d3748';
        this.ctx.beginPath();
        this.ctx.arc(this.pendulum.pivotX, this.pendulum.pivotY, 8, 0, 2 * Math.PI);
        this.ctx.fill();

        // حساب موقع الكتلة
        const bobX = this.pendulum.pivotX + this.pendulum.length * Math.sin(this.pendulum.angle);
        const bobY = this.pendulum.pivotY + this.pendulum.length * Math.cos(this.pendulum.angle);

        // رسم المسار
        if (this.getParameter('showTrail') && this.trail.length > 1) {
            this.drawTrail();
        }

        // رسم الخيط
        this.ctx.strokeStyle = '#4a5568';
        this.ctx.lineWidth = 3;
        this.ctx.beginPath();
        this.ctx.moveTo(this.pendulum.pivotX, this.pendulum.pivotY);
        this.ctx.lineTo(bobX, bobY);
        this.ctx.stroke();

        // رسم الكتلة
        this.pendulum.radius = Math.max(10, this.pendulum.mass * 8);
        this.ctx.fillStyle = '#e53e3e';
        this.ctx.beginPath();
        this.ctx.arc(bobX, bobY, this.pendulum.radius, 0, 2 * Math.PI);
        this.ctx.fill();

        // رسم ظل الكتلة
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.2)';
        this.ctx.beginPath();
        this.ctx.arc(bobX + 3, bobY + 3, this.pendulum.radius, 0, 2 * Math.PI);
        this.ctx.fill();

        // رسم الكتلة على الكرة
        this.ctx.fillStyle = '#fff';
        this.ctx.font = '12px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(`${this.pendulum.mass}kg`, bobX, bobY + 4);

        // رسم متجه السرعة
        this.drawVelocityVector(bobX, bobY);

        // رسم متجه القوة
        this.drawForceVector(bobX, bobY);

        // رسم مؤشر الزاوية
        this.drawAngleIndicator();

        // رسم مستويات الطاقة
        this.drawEnergyLevels();

        // رسم المعلومات
        this.drawPendulumInfo();
    }

    drawTrail() {
        this.ctx.strokeStyle = 'rgba(66, 153, 225, 0.6)';
        this.ctx.lineWidth = 2;
        this.ctx.beginPath();

        for (let i = 0; i < this.trail.length; i++) {
            const point = this.trail[i];
            const alpha = i / this.trail.length;

            this.ctx.globalAlpha = alpha;

            if (i === 0) {
                this.ctx.moveTo(point.x, point.y);
            } else {
                this.ctx.lineTo(point.x, point.y);
            }
        }

        this.ctx.stroke();
        this.ctx.globalAlpha = 1;
    }

    drawVelocityVector(bobX, bobY) {
        const scale = 20;
        const L = this.pendulum.length / 100;

        // السرعة الخطية = L * ω
        const vx = -L * this.pendulum.angularVelocity * Math.cos(this.pendulum.angle) * scale;
        const vy = L * this.pendulum.angularVelocity * Math.sin(this.pendulum.angle) * scale;

        if (Math.abs(vx) > 2 || Math.abs(vy) > 2) {
            this.ctx.strokeStyle = '#48bb78';
            this.ctx.lineWidth = 3;
            this.ctx.beginPath();
            this.ctx.moveTo(bobX, bobY);
            this.ctx.lineTo(bobX + vx, bobY + vy);
            this.ctx.stroke();

            this.drawArrowHead(bobX + vx, bobY + vy, Math.atan2(vy, vx), '#48bb78');
        }
    }

    drawForceVector(bobX, bobY) {
        const scale = 5;
        const L = this.pendulum.length / 100;
        const g = this.pendulum.gravity;

        // القوة المرجعة = -mg sin(θ)
        const restoreForce = -this.pendulum.mass * g * Math.sin(this.pendulum.angle);
        const fx = restoreForce * Math.cos(this.pendulum.angle) * scale;
        const fy = -restoreForce * Math.sin(this.pendulum.angle) * scale;

        if (Math.abs(fx) > 2 || Math.abs(fy) > 2) {
            this.ctx.strokeStyle = '#f56565';
            this.ctx.lineWidth = 3;
            this.ctx.beginPath();
            this.ctx.moveTo(bobX, bobY);
            this.ctx.lineTo(bobX + fx, bobY + fy);
            this.ctx.stroke();

            this.drawArrowHead(bobX + fx, bobY + fy, Math.atan2(fy, fx), '#f56565');
        }
    }

    drawAngleIndicator() {
        const radius = 50;

        this.ctx.strokeStyle = '#9ca3af';
        this.ctx.lineWidth = 2;
        this.ctx.setLineDash([5, 5]);

        // خط عمودي مرجعي
        this.ctx.beginPath();
        this.ctx.moveTo(this.pendulum.pivotX, this.pendulum.pivotY);
        this.ctx.lineTo(this.pendulum.pivotX, this.pendulum.pivotY + radius);
        this.ctx.stroke();

        // قوس الزاوية
        this.ctx.beginPath();
        this.ctx.arc(this.pendulum.pivotX, this.pendulum.pivotY, radius,
                    Math.PI/2, Math.PI/2 + this.pendulum.angle, this.pendulum.angle > 0);
        this.ctx.stroke();

        this.ctx.setLineDash([]);

        // قيمة الزاوية
        const angleText = `${(this.pendulum.angle * 180 / Math.PI).toFixed(1)}°`;
        this.ctx.fillStyle = '#4a5568';
        this.ctx.font = '14px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(angleText, this.pendulum.pivotX + 70, this.pendulum.pivotY + 30);
    }

    drawEnergyLevels() {
        if (this.energyData.length === 0) return;

        const lastData = this.energyData[this.energyData.length - 1];
        const maxHeight = this.pendulum.length * (1 - Math.cos(Math.PI/4)); // عند 45 درجة

        // خط الطاقة الكلية
        const energyHeight = this.pendulum.pivotY + this.pendulum.length - (lastData.total * 1000);

        this.ctx.strokeStyle = '#f59e0b';
        this.ctx.lineWidth = 2;
        this.ctx.setLineDash([10, 5]);
        this.ctx.beginPath();
        this.ctx.moveTo(this.pendulum.pivotX - 100, energyHeight);
        this.ctx.lineTo(this.pendulum.pivotX + 100, energyHeight);
        this.ctx.stroke();
        this.ctx.setLineDash([]);

        // تسمية
        this.ctx.fillStyle = '#f59e0b';
        this.ctx.font = '12px Arial';
        this.ctx.fillText('مستوى الطاقة الكلية', this.pendulum.pivotX + 110, energyHeight);
    }

    drawPendulumInfo() {
        this.ctx.fillStyle = '#2d3748';
        this.ctx.font = '14px Arial';
        this.ctx.textAlign = 'left';

        const info = [
            `الزاوية: ${(this.pendulum.angle * 180 / Math.PI).toFixed(1)}°`,
            `السرعة الزاوية: ${this.pendulum.angularVelocity.toFixed(2)} راد/ث`,
            `التردد الطبيعي: ${this.naturalFrequency.toFixed(3)} Hz`,
            `الدورة: ${this.period.toFixed(2)} ث`
        ];

        info.forEach((text, index) => {
            this.ctx.fillText(text, 10, 20 + index * 20);
        });

        // مفاتيح المتجهات
        this.ctx.fillStyle = '#48bb78';
        this.ctx.fillText('← السرعة', 600, 20);
        this.ctx.fillStyle = '#f56565';
        this.ctx.fillText('← القوة المرجعة', 600, 40);
    }

    updatePendulumInfo() {
        const naturalFreqSpan = document.getElementById('natural-freq');
        const periodSpan = document.getElementById('period');
        const totalEnergySpan = document.getElementById('total-energy');
        const amplitudeSpan = document.getElementById('current-amplitude');

        if (naturalFreqSpan) naturalFreqSpan.textContent = this.naturalFrequency.toFixed(3);
        if (periodSpan) periodSpan.textContent = this.period.toFixed(2);

        if (this.energyData.length > 0) {
            const lastData = this.energyData[this.energyData.length - 1];
            if (totalEnergySpan) totalEnergySpan.textContent = lastData.total.toFixed(3);
        }

        if (amplitudeSpan) {
            amplitudeSpan.textContent = (Math.abs(this.pendulum.angle) * 180 / Math.PI).toFixed(1);
        }
    }

    toggleAnalysis() {
        this.showAnalysis = !this.showAnalysis;
    }

    addImpulse() {
        this.pendulum.angularVelocity += 2; // إضافة دفعة
    }

    onParameterChange(name, value) {
        const valueSpan = document.getElementById(name.replace(/([A-Z])/g, '-$1').toLowerCase() + '-value');
        if (valueSpan) {
            valueSpan.textContent = value;
        }

        if (name === 'driving') {
            const drivingControls = document.getElementById('driving-controls');
            if (drivingControls) {
                drivingControls.style.display = value ? 'block' : 'none';
            }
        }

        if (name === 'initialAngle') {
            this.pendulum.angle = (parseFloat(value) * Math.PI) / 180;
            this.pendulum.angularVelocity = 0;
        }

        if (name === 'length' || name === 'gravity') {
            this.calculateNaturalFrequency();
        }
    }

    resetExperiment() {
        this.resetPendulum();
        this.startTime = Date.now();
        this.data = [];
    }

    drawGrid() {
        this.ctx.strokeStyle = '#e5e7eb';
        this.ctx.lineWidth = 1;

        for (let x = 0; x <= this.canvas.width; x += 20) {
            this.ctx.beginPath();
            this.ctx.moveTo(x, 0);
            this.ctx.lineTo(x, this.canvas.height);
            this.ctx.stroke();
        }

        for (let y = 0; y <= this.canvas.height; y += 20) {
            this.ctx.beginPath();
            this.ctx.moveTo(0, y);
            this.ctx.lineTo(this.canvas.width, y);
            this.ctx.stroke();
        }
    }

    drawArrowHead(x, y, angle, color) {
        this.ctx.fillStyle = color;
        this.ctx.save();
        this.ctx.translate(x, y);
        this.ctx.rotate(angle);
        this.ctx.beginPath();
        this.ctx.moveTo(0, 0);
        this.ctx.lineTo(-10, -5);
        this.ctx.lineTo(-10, 5);
        this.ctx.closePath();
        this.ctx.fill();
        this.ctx.restore();
    }
}

// تجربة التصادمات المتقدمة - Advanced Collisions Experiment
class AdvancedCollisionsExperiment extends PhysicsExperiment {
    constructor() {
        super('advanced-collisions', 'Advanced Collisions', 'التصادمات المتقدمة', 'mechanics');
        this.balls = [];
        this.collisionHistory = [];
        this.collisionType = 'elastic'; // elastic, inelastic, perfectly-inelastic
        this.showVectors = true;
        this.showTrails = true;
        this.gravityEnabled = false;
        this.wallBounce = true;
    }

    initializeParameters() {
        this.setParameter('collisionType', 'elastic');
        this.setParameter('restitution', 1.0);
        this.setParameter('friction', 0.0);
        this.setParameter('gravity', 0);
        this.setParameter('showVectors', true);
        this.setParameter('showTrails', true);
        this.setParameter('wallBounce', true);
        this.setParameter('numBalls', 2);

        this.initializeBalls();
    }

    initializeBalls() {
        this.balls = [];
        const numBalls = this.getParameter('numBalls');

        const configurations = {
            2: [
                { x: 150, y: 300, vx: 100, vy: 0, mass: 2, radius: 20, color: '#e53e3e' },
                { x: 650, y: 300, vx: -80, vy: 0, mass: 1.5, radius: 18, color: '#4299e1' }
            ],
            3: [
                { x: 150, y: 250, vx: 120, vy: 20, mass: 2, radius: 20, color: '#e53e3e' },
                { x: 400, y: 300, vx: 0, vy: 0, mass: 1.5, radius: 18, color: '#4299e1' },
                { x: 650, y: 350, vx: -100, vy: -30, mass: 1, radius: 15, color: '#48bb78' }
            ],
            4: [
                { x: 150, y: 200, vx: 100, vy: 50, mass: 2, radius: 20, color: '#e53e3e' },
                { x: 650, y: 200, vx: -80, vy: 30, mass: 1.5, radius: 18, color: '#4299e1' },
                { x: 150, y: 400, vx: 120, vy: -40, mass: 1, radius: 15, color: '#48bb78' },
                { x: 650, y: 400, vx: -90, vy: -20, mass: 1.8, radius: 19, color: '#f59e0b' }
            ]
        };

        const config = configurations[numBalls] || configurations[2];

        config.forEach((ballConfig, index) => {
            this.balls.push({
                id: index + 1,
                x: ballConfig.x,
                y: ballConfig.y,
                vx: ballConfig.vx,
                vy: ballConfig.vy,
                mass: ballConfig.mass,
                radius: ballConfig.radius,
                color: ballConfig.color,
                trail: [],
                collisions: 0,
                lastCollisionTime: 0
            });
        });
    }

    setupControls() {
        const controlsContainer = document.getElementById('experiment-controls');
        if (controlsContainer) {
            controlsContainer.innerHTML = `
                <div class="control-panel">
                    <h3>متحكمات التصادمات المتقدمة</h3>

                    <div class="control-group">
                        <label>نوع التصادم</label>
                        <select id="collision-type-select" onchange="physicsExperiments.currentExperiment.setParameter('collisionType', this.value)">
                            <option value="elastic">مرن (Elastic)</option>
                            <option value="inelastic">غير مرن (Inelastic)</option>
                            <option value="perfectly-inelastic">غير مرن تماماً</option>
                        </select>
                    </div>

                    <div class="control-group">
                        <label>معامل الارتداد</label>
                        <input type="range" id="restitution-slider" min="0" max="1" value="1" step="0.1"
                               oninput="physicsExperiments.currentExperiment.setParameter('restitution', this.value)">
                        <span id="restitution-value">1</span>
                    </div>

                    <div class="control-group">
                        <label>الاحتكاك</label>
                        <input type="range" id="friction-slider" min="0" max="0.1" value="0" step="0.01"
                               oninput="physicsExperiments.currentExperiment.setParameter('friction', this.value)">
                        <span id="friction-value">0</span>
                    </div>

                    <div class="control-group">
                        <label>الجاذبية (م/ث²)</label>
                        <input type="range" id="gravity-slider" min="0" max="20" value="0" step="0.5"
                               oninput="physicsExperiments.currentExperiment.setParameter('gravity', this.value)">
                        <span id="gravity-value">0</span>
                    </div>

                    <div class="control-group">
                        <label>عدد الكرات</label>
                        <select id="num-balls-select" onchange="physicsExperiments.currentExperiment.setParameter('numBalls', this.value)">
                            <option value="2">كرتان</option>
                            <option value="3">ثلاث كرات</option>
                            <option value="4">أربع كرات</option>
                        </select>
                    </div>

                    <div class="control-group">
                        <label>
                            <input type="checkbox" id="vectors-check" checked
                                   onchange="physicsExperiments.currentExperiment.setParameter('showVectors', this.checked)">
                            إظهار متجهات السرعة
                        </label>
                    </div>

                    <div class="control-group">
                        <label>
                            <input type="checkbox" id="trails-check" checked
                                   onchange="physicsExperiments.currentExperiment.setParameter('showTrails', this.checked)">
                            إظهار مسارات الحركة
                        </label>
                    </div>

                    <div class="control-group">
                        <label>
                            <input type="checkbox" id="wall-bounce-check" checked
                                   onchange="physicsExperiments.currentExperiment.setParameter('wallBounce', this.checked)">
                            ارتداد من الجدران
                        </label>
                    </div>

                    <div class="collision-stats">
                        <h4>إحصائيات التصادم</h4>
                        <div id="collision-data">
                            <div>إجمالي التصادمات: <span id="total-collisions">0</span></div>
                            <div>الزخم الكلي: <span id="total-momentum">0</span> كغ⋅م/ث</div>
                            <div>الطاقة الحركية: <span id="total-ke">0</span> J</div>
                            <div>فقدان الطاقة: <span id="energy-loss">0</span> J</div>
                        </div>
                    </div>

                    <div class="control-buttons">
                        <button onclick="physicsExperiments.currentExperiment.resetExperiment()" class="btn-reset">
                            <i class="fas fa-redo"></i> إعادة تعيين
                        </button>
                        <button onclick="physicsExperiments.currentExperiment.pauseResume()" class="btn-pause">
                            <i class="fas fa-pause"></i> إيقاف مؤقت
                        </button>
                        <button onclick="physicsExperiments.currentExperiment.addRandomBall()" class="btn-add">
                            <i class="fas fa-plus"></i> إضافة كرة
                        </button>
                    </div>
                </div>
            `;
        }
    }

    update() {
        if (!this.isRunning || this.isPaused) return;

        const dt = 0.016;
        const gravity = this.getParameter('gravity');
        const friction = this.getParameter('friction');

        // تحديث مواقع الكرات
        this.balls.forEach(ball => {
            // تطبيق الجاذبية
            if (gravity > 0) {
                ball.vy += gravity * dt;
            }

            // تطبيق الاحتكاك
            if (friction > 0) {
                const speed = Math.sqrt(ball.vx * ball.vx + ball.vy * ball.vy);
                if (speed > 0) {
                    const frictionForce = friction * ball.mass * 9.8; // افتراض g = 9.8
                    const frictionAccel = frictionForce / ball.mass;
                    const frictionRatio = Math.min(1, frictionAccel * dt / speed);

                    ball.vx *= (1 - frictionRatio);
                    ball.vy *= (1 - frictionRatio);
                }
            }

            // تحديث الموقع
            ball.x += ball.vx * dt;
            ball.y += ball.vy * dt;

            // إضافة للمسار
            if (this.getParameter('showTrails')) {
                ball.trail.push({ x: ball.x, y: ball.y, time: Date.now() });
                if (ball.trail.length > 100) {
                    ball.trail.shift();
                }
            }

            // التحقق من حدود الشاشة
            if (this.getParameter('wallBounce')) {
                this.handleWallCollisions(ball);
            }
        });

        // التحقق من التصادمات بين الكرات
        this.checkBallCollisions();

        // تحديث الإحصائيات
        this.updateCollisionStats();

        // تسجيل البيانات
        const currentTime = (Date.now() - this.startTime) / 1000;
        this.recordCollisionData(currentTime);
    }

    handleWallCollisions(ball) {
        const restitution = this.getParameter('restitution');

        // الجدار الأيسر
        if (ball.x - ball.radius <= 0) {
            ball.x = ball.radius;
            ball.vx = -ball.vx * restitution;
        }

        // الجدار الأيمن
        if (ball.x + ball.radius >= this.canvas.width) {
            ball.x = this.canvas.width - ball.radius;
            ball.vx = -ball.vx * restitution;
        }

        // الجدار العلوي
        if (ball.y - ball.radius <= 0) {
            ball.y = ball.radius;
            ball.vy = -ball.vy * restitution;
        }

        // الجدار السفلي
        if (ball.y + ball.radius >= this.canvas.height) {
            ball.y = this.canvas.height - ball.radius;
            ball.vy = -ball.vy * restitution;
        }
    }

    checkBallCollisions() {
        for (let i = 0; i < this.balls.length; i++) {
            for (let j = i + 1; j < this.balls.length; j++) {
                const ball1 = this.balls[i];
                const ball2 = this.balls[j];

                const dx = ball2.x - ball1.x;
                const dy = ball2.y - ball1.y;
                const distance = Math.sqrt(dx * dx + dy * dy);
                const minDistance = ball1.radius + ball2.radius;

                if (distance < minDistance) {
                    this.resolveCollision(ball1, ball2);

                    // تسجيل التصادم
                    const currentTime = Date.now();
                    if (currentTime - ball1.lastCollisionTime > 100) { // تجنب التصادمات المتكررة
                        ball1.collisions++;
                        ball2.collisions++;
                        ball1.lastCollisionTime = currentTime;
                        ball2.lastCollisionTime = currentTime;

                        this.collisionHistory.push({
                            time: (currentTime - this.startTime) / 1000,
                            ball1Id: ball1.id,
                            ball2Id: ball2.id,
                            ball1VelocityBefore: { vx: ball1.vx, vy: ball1.vy },
                            ball2VelocityBefore: { vx: ball2.vx, vy: ball2.vy },
                            type: this.getParameter('collisionType')
                        });
                    }
                }
            }
        }
    }

    resolveCollision(ball1, ball2) {
        const dx = ball2.x - ball1.x;
        const dy = ball2.y - ball1.y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        if (distance === 0) return;

        // وحدة المتجه العادي
        const nx = dx / distance;
        const ny = dy / distance;

        // فصل الكرات
        const overlap = ball1.radius + ball2.radius - distance;
        const separationX = nx * overlap * 0.5;
        const separationY = ny * overlap * 0.5;

        ball1.x -= separationX;
        ball1.y -= separationY;
        ball2.x += separationX;
        ball2.y += separationY;

        // السرعات النسبية
        const dvx = ball2.vx - ball1.vx;
        const dvy = ball2.vy - ball1.vy;

        // السرعة النسبية في اتجاه التصادم
        const dvn = dvx * nx + dvy * ny;

        // لا تحل التصادم إذا كانت الكرات تتحرك بعيداً عن بعضها
        if (dvn > 0) return;

        const collisionType = this.getParameter('collisionType');
        const restitution = this.getParameter('restitution');

        let e = 1; // معامل الارتداد

        switch (collisionType) {
            case 'elastic':
                e = 1;
                break;
            case 'inelastic':
                e = restitution;
                break;
            case 'perfectly-inelastic':
                e = 0;
                break;
        }

        // الدفع
        const impulse = 2 * dvn / (ball1.mass + ball2.mass) * (1 + e);

        // تحديث السرعات
        ball1.vx += impulse * ball2.mass * nx;
        ball1.vy += impulse * ball2.mass * ny;
        ball2.vx -= impulse * ball1.mass * nx;
        ball2.vy -= impulse * ball1.mass * ny;

        // للتصادم غير المرن تماماً، اجعل الكرات تتحرك بنفس السرعة
        if (collisionType === 'perfectly-inelastic') {
            const totalMass = ball1.mass + ball2.mass;
            const finalVx = (ball1.mass * ball1.vx + ball2.mass * ball2.vx) / totalMass;
            const finalVy = (ball1.mass * ball1.vy + ball2.mass * ball2.vy) / totalMass;

            ball1.vx = finalVx;
            ball1.vy = finalVy;
            ball2.vx = finalVx;
            ball2.vy = finalVy;
        }
    }

    draw() {
        if (!this.ctx) return;

        // مسح الشاشة
        this.ctx.fillStyle = '#f0f8ff';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // رسم الشبكة
        this.drawGrid();

        // رسم المسارات
        if (this.getParameter('showTrails')) {
            this.balls.forEach(ball => {
                this.drawTrail(ball);
            });
        }

        // رسم الكرات
        this.balls.forEach(ball => {
            this.drawBall(ball);
        });

        // رسم متجهات السرعة
        if (this.getParameter('showVectors')) {
            this.balls.forEach(ball => {
                this.drawVelocityVector(ball);
            });
        }

        // رسم المعلومات
        this.drawCollisionInfo();
    }

    drawBall(ball) {
        // رسم الكرة
        this.ctx.fillStyle = ball.color;
        this.ctx.beginPath();
        this.ctx.arc(ball.x, ball.y, ball.radius, 0, 2 * Math.PI);
        this.ctx.fill();

        // رسم الحدود
        this.ctx.strokeStyle = '#2d3748';
        this.ctx.lineWidth = 2;
        this.ctx.stroke();

        // رسم معلومات الكرة
        this.ctx.fillStyle = '#fff';
        this.ctx.font = '10px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(`${ball.id}`, ball.x, ball.y - 5);
        this.ctx.fillText(`${ball.mass}kg`, ball.x, ball.y + 5);

        // رسم عدد التصادمات
        this.ctx.fillStyle = ball.color;
        this.ctx.font = '8px Arial';
        this.ctx.fillText(`${ball.collisions}`, ball.x + ball.radius + 5, ball.y - ball.radius);
    }

    drawTrail(ball) {
        if (ball.trail.length < 2) return;

        this.ctx.strokeStyle = ball.color;
        this.ctx.globalAlpha = 0.4;
        this.ctx.lineWidth = 2;
        this.ctx.beginPath();

        for (let i = 0; i < ball.trail.length; i++) {
            const point = ball.trail[i];
            const alpha = i / ball.trail.length;

            this.ctx.globalAlpha = alpha * 0.4;

            if (i === 0) {
                this.ctx.moveTo(point.x, point.y);
            } else {
                this.ctx.lineTo(point.x, point.y);
            }
        }

        this.ctx.stroke();
        this.ctx.globalAlpha = 1;
    }

    drawVelocityVector(ball) {
        const scale = 0.2;
        const vx = ball.vx * scale;
        const vy = ball.vy * scale;

        if (Math.abs(vx) > 2 || Math.abs(vy) > 2) {
            this.ctx.strokeStyle = '#48bb78';
            this.ctx.lineWidth = 3;
            this.ctx.beginPath();
            this.ctx.moveTo(ball.x, ball.y);
            this.ctx.lineTo(ball.x + vx, ball.y + vy);
            this.ctx.stroke();

            this.drawArrowHead(ball.x + vx, ball.y + vy, Math.atan2(vy, vx), '#48bb78');
        }
    }

    drawCollisionInfo() {
        this.ctx.fillStyle = '#2d3748';
        this.ctx.font = '14px Arial';
        this.ctx.textAlign = 'left';

        const totalMomentum = this.calculateTotalMomentum();
        const totalKE = this.calculateTotalKineticEnergy();
        const totalCollisions = this.balls.reduce((sum, ball) => sum + ball.collisions, 0) / 2;

        const info = [
            `نوع التصادم: ${this.getCollisionTypeArabic()}`,
            `إجمالي التصادمات: ${totalCollisions}`,
            `الزخم الكلي: ${totalMomentum.toFixed(2)} كغ⋅م/ث`,
            `الطاقة الحركية: ${totalKE.toFixed(2)} J`
        ];

        info.forEach((text, index) => {
            this.ctx.fillText(text, 10, 20 + index * 20);
        });
    }

    getCollisionTypeArabic() {
        const types = {
            'elastic': 'مرن',
            'inelastic': 'غير مرن',
            'perfectly-inelastic': 'غير مرن تماماً'
        };
        return types[this.getParameter('collisionType')] || 'مرن';
    }

    calculateTotalMomentum() {
        return Math.sqrt(
            Math.pow(this.balls.reduce((sum, ball) => sum + ball.mass * ball.vx, 0), 2) +
            Math.pow(this.balls.reduce((sum, ball) => sum + ball.mass * ball.vy, 0), 2)
        );
    }

    calculateTotalKineticEnergy() {
        return this.balls.reduce((sum, ball) => {
            const speed = Math.sqrt(ball.vx * ball.vx + ball.vy * ball.vy);
            return sum + 0.5 * ball.mass * speed * speed;
        }, 0);
    }

    updateCollisionStats() {
        const totalCollisionsSpan = document.getElementById('total-collisions');
        const totalMomentumSpan = document.getElementById('total-momentum');
        const totalKESpan = document.getElementById('total-ke');
        const energyLossSpan = document.getElementById('energy-loss');

        const totalCollisions = this.balls.reduce((sum, ball) => sum + ball.collisions, 0) / 2;
        const totalMomentum = this.calculateTotalMomentum();
        const totalKE = this.calculateTotalKineticEnergy();

        if (totalCollisionsSpan) totalCollisionsSpan.textContent = totalCollisions;
        if (totalMomentumSpan) totalMomentumSpan.textContent = totalMomentum.toFixed(2);
        if (totalKESpan) totalKESpan.textContent = totalKE.toFixed(2);

        // حساب فقدان الطاقة (إذا كان هناك طاقة أولية محفوظة)
        if (energyLossSpan && this.initialKE) {
            const energyLoss = this.initialKE - totalKE;
            energyLossSpan.textContent = energyLoss.toFixed(2);
        }
    }

    recordCollisionData(currentTime) {
        this.recordData(currentTime, {
            totalMomentum: this.calculateTotalMomentum(),
            totalKineticEnergy: this.calculateTotalKineticEnergy(),
            totalCollisions: this.balls.reduce((sum, ball) => sum + ball.collisions, 0) / 2,
            ball1_x: this.balls[0]?.x || 0,
            ball1_vx: this.balls[0]?.vx || 0,
            ball2_x: this.balls[1]?.x || 0,
            ball2_vx: this.balls[1]?.vx || 0
        });
    }

    pauseResume() {
        this.isPaused = !this.isPaused;
        const btn = document.querySelector('.btn-pause i');
        if (btn) {
            btn.className = this.isPaused ? 'fas fa-play' : 'fas fa-pause';
        }
    }

    addRandomBall() {
        const newBall = {
            id: this.balls.length + 1,
            x: Math.random() * (this.canvas.width - 100) + 50,
            y: Math.random() * (this.canvas.height - 100) + 50,
            vx: (Math.random() - 0.5) * 200,
            vy: (Math.random() - 0.5) * 200,
            mass: 1 + Math.random() * 2,
            radius: 12 + Math.random() * 8,
            color: `hsl(${Math.random() * 360}, 70%, 50%)`,
            trail: [],
            collisions: 0,
            lastCollisionTime: 0
        };

        this.balls.push(newBall);
    }

    onParameterChange(name, value) {
        const valueSpan = document.getElementById(name.replace(/([A-Z])/g, '-$1').toLowerCase() + '-value');
        if (valueSpan) {
            valueSpan.textContent = value;
        }

        if (name === 'numBalls') {
            this.setParameter('numBalls', parseInt(value));
            this.initializeBalls();
        }
    }

    resetExperiment() {
        this.initializeBalls();
        this.collisionHistory = [];
        this.isPaused = false;
        this.initialKE = this.calculateTotalKineticEnergy();
        this.startTime = Date.now();
        this.data = [];

        // إعادة تعيين زر الإيقاف المؤقت
        const btn = document.querySelector('.btn-pause i');
        if (btn) {
            btn.className = 'fas fa-pause';
        }
    }

    drawGrid() {
        this.ctx.strokeStyle = '#e5e7eb';
        this.ctx.lineWidth = 1;

        for (let x = 0; x <= this.canvas.width; x += 40) {
            this.ctx.beginPath();
            this.ctx.moveTo(x, 0);
            this.ctx.lineTo(x, this.canvas.height);
            this.ctx.stroke();
        }

        for (let y = 0; y <= this.canvas.height; y += 40) {
            this.ctx.beginPath();
            this.ctx.moveTo(0, y);
            this.ctx.lineTo(this.canvas.width, y);
            this.ctx.stroke();
        }
    }

    drawArrowHead(x, y, angle, color) {
        this.ctx.fillStyle = color;
        this.ctx.save();
        this.ctx.translate(x, y);
        this.ctx.rotate(angle);
        this.ctx.beginPath();
        this.ctx.moveTo(0, 0);
        this.ctx.lineTo(-8, -4);
        this.ctx.lineTo(-8, 4);
        this.ctx.closePath();
        this.ctx.fill();
        this.ctx.restore();
    }
}