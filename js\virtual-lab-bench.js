// Virtual Lab Bench System
// Interactive workspace with beakers, flasks, burettes, and lab equipment

class VirtualLabBench {
    constructor(containerId) {
        this.containerId = containerId;
        this.equipment = new Map();
        this.chemicals = new Map();
        this.activeExperiment = null;
        this.safetyMonitor = new SafetyMonitor();
        this.reactionEngine = new ReactionVisualizationEngine();

        this.benchLayout = {
            width: 1200,
            height: 800,
            zones: {
                equipment: { x: 0, y: 0, width: 300, height: 800 },
                workspace: { x: 300, y: 0, width: 700, height: 600 },
                controls: { x: 1000, y: 0, width: 200, height: 800 },
                safety: { x: 300, y: 600, width: 700, height: 200 }
            }
        };

        this.init();
    }

    init() {
        this.createBenchInterface();
        this.initializeEquipment();
        this.setupEventListeners();
        this.loadChemicalInventory();
    }

    createBenchInterface() {
        const container = document.getElementById(this.containerId);
        if (!container) {
            console.error(`Container ${this.containerId} not found`);
            return;
        }

        const benchHTML = `
            <div class="virtual-lab-bench" id="lab-bench-${this.containerId}">
                <!-- Equipment Panel -->
                <div class="equipment-panel" id="equipment-panel">
                    <h3 class="panel-title">
                        <i class="fas fa-flask"></i>
                        Laboratory Equipment
                    </h3>
                    <div class="equipment-grid" id="equipment-grid">
                        <!-- Equipment items will be populated here -->
                    </div>
                </div>

                <!-- Main Workspace -->
                <div class="lab-workspace" id="lab-workspace">
                    <div class="workspace-surface">
                        <div class="bench-grid"></div>
                        <div class="drop-zones" id="drop-zones">
                            <!-- Drop zones for equipment placement -->
                        </div>
                        <div class="active-equipment" id="active-equipment">
                            <!-- Active equipment will be placed here -->
                        </div>
                    </div>

                    <!-- Reaction Visualization Area -->
                    <div class="reaction-viewer" id="reaction-viewer">
                        <canvas id="reaction-canvas" width="400" height="200"></canvas>
                        <div class="reaction-controls">
                            <button class="reaction-btn" onclick="labBench.toggleMolecularView()">
                                <i class="fas fa-atom"></i> Molecular View
                            </button>
                            <button class="reaction-btn" onclick="labBench.resetReaction()">
                                <i class="fas fa-redo"></i> Reset
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Controls Panel -->
                <div class="controls-panel" id="controls-panel">
                    <h3 class="panel-title">
                        <i class="fas fa-sliders-h"></i>
                        Experiment Controls
                    </h3>

                    <div class="control-section">
                        <h4>Temperature Control</h4>
                        <div class="temperature-control">
                            <input type="range" id="temp-slider" min="0" max="200" value="25"
                                   oninput="labBench.setTemperature(this.value)">
                            <div class="temp-display">
                                <span id="temp-value">25</span>°C
                            </div>
                        </div>
                        <div class="heating-controls">
                            <button class="control-btn" onclick="labBench.startHeating()">
                                <i class="fas fa-fire"></i> Heat
                            </button>
                            <button class="control-btn" onclick="labBench.stopHeating()">
                                <i class="fas fa-snowflake"></i> Cool
                            </button>
                        </div>
                    </div>

                    <div class="control-section">
                        <h4>Stirring</h4>
                        <button class="control-btn" onclick="labBench.toggleStirring()">
                            <i class="fas fa-sync"></i> Stir
                        </button>
                        <input type="range" id="stir-speed" min="0" max="10" value="0"
                               oninput="labBench.setStirSpeed(this.value)">
                    </div>

                    <div class="control-section">
                        <h4>Chemical Addition</h4>
                        <select id="chemical-selector" onchange="labBench.selectChemical(this.value)">
                            <option value="">Select Chemical...</option>
                        </select>
                        <div class="addition-controls">
                            <input type="number" id="chemical-amount" min="0.1" max="100" step="0.1" value="1.0">
                            <span>mL</span>
                            <button class="control-btn" onclick="labBench.addChemical()">
                                <i class="fas fa-plus"></i> Add
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Safety Panel -->
                <div class="safety-panel" id="safety-panel">
                    <h3 class="panel-title">
                        <i class="fas fa-shield-alt"></i>
                        Safety Monitor
                        <div class="safety-status" id="safety-status">
                            <div class="status-led safe" id="safety-led"></div>
                        </div>
                    </h3>

                    <div class="safety-alerts" id="safety-alerts">
                        <!-- Safety alerts will appear here -->
                    </div>

                    <div class="emergency-controls">
                        <button class="emergency-btn" onclick="labBench.emergencyStop()">
                            <i class="fas fa-exclamation-triangle"></i>
                            EMERGENCY STOP
                        </button>
                        <button class="safety-btn" onclick="labBench.showSafetyInfo()">
                            <i class="fas fa-info-circle"></i>
                            Safety Info
                        </button>
                    </div>
                </div>
            </div>
        `;

        container.innerHTML = benchHTML;
        this.setupCanvas();
    }

    initializeEquipment() {
        const equipmentTypes = [
            {
                id: 'beaker_250ml',
                name: '250mL Beaker',
                icon: 'fas fa-flask',
                capacity: 250,
                type: 'container',
                description: 'Standard laboratory beaker for mixing and heating'
            },
            {
                id: 'beaker_500ml',
                name: '500mL Beaker',
                icon: 'fas fa-flask',
                capacity: 500,
                type: 'container',
                description: 'Large beaker for bulk reactions'
            },
            {
                id: 'erlenmeyer_250ml',
                name: '250mL Erlenmeyer Flask',
                icon: 'fas fa-vial',
                capacity: 250,
                type: 'container',
                description: 'Conical flask ideal for swirling and mixing'
            },
            {
                id: 'burette_50ml',
                name: '50mL Burette',
                icon: 'fas fa-eyedropper',
                capacity: 50,
                type: 'measuring',
                description: 'Precision volume delivery for titrations'
            },
            {
                id: 'pipette_25ml',
                name: '25mL Pipette',
                icon: 'fas fa-syringe',
                capacity: 25,
                type: 'measuring',
                description: 'Accurate volume transfer'
            },
            {
                id: 'graduated_cylinder',
                name: '100mL Graduated Cylinder',
                icon: 'fas fa-vial',
                capacity: 100,
                type: 'measuring',
                description: 'Volume measurement with graduations'
            },
            {
                id: 'stirring_rod',
                name: 'Stirring Rod',
                icon: 'fas fa-minus',
                type: 'tool',
                description: 'Glass rod for manual stirring'
            },
            {
                id: 'magnetic_stirrer',
                name: 'Magnetic Stirrer',
                icon: 'fas fa-sync',
                type: 'equipment',
                description: 'Automated stirring with heating capability'
            },
            {
                id: 'hot_plate',
                name: 'Hot Plate',
                icon: 'fas fa-fire',
                type: 'equipment',
                description: 'Controlled heating source'
            },
            {
                id: 'ph_meter',
                name: 'pH Meter',
                icon: 'fas fa-tint',
                type: 'instrument',
                description: 'Digital pH measurement'
            }
        ];

        const equipmentGrid = document.getElementById('equipment-grid');
        equipmentGrid.innerHTML = '';

        equipmentTypes.forEach(equipment => {
            this.equipment.set(equipment.id, equipment);

            const equipmentElement = document.createElement('div');
            equipmentElement.className = 'equipment-item';
            equipmentElement.draggable = true;
            equipmentElement.dataset.equipmentId = equipment.id;

            equipmentElement.innerHTML = `
                <div class="equipment-icon">
                    <i class="${equipment.icon}"></i>
                </div>
                <div class="equipment-name">${equipment.name}</div>
                <div class="equipment-info">
                    ${equipment.capacity ? `${equipment.capacity}mL` : equipment.type}
                </div>
            `;

            // Add drag event listeners
            equipmentElement.addEventListener('dragstart', (e) => {
                e.dataTransfer.setData('text/plain', equipment.id);
                e.dataTransfer.effectAllowed = 'copy';
            });

            equipmentElement.addEventListener('click', () => {
                this.showEquipmentInfo(equipment);
            });

            equipmentGrid.appendChild(equipmentElement);
        });
    }

    setupCanvas() {
        const canvas = document.getElementById('reaction-canvas');
        if (canvas) {
            this.reactionEngine.initCanvas(canvas);
        }
    }

    setupEventListeners() {
        const workspace = document.getElementById('lab-workspace');

        // Drop zone event listeners
        workspace.addEventListener('dragover', (e) => {
            e.preventDefault();
            e.dataTransfer.dropEffect = 'copy';
        });

        workspace.addEventListener('drop', (e) => {
            e.preventDefault();
            const equipmentId = e.dataTransfer.getData('text/plain');
            const rect = workspace.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            this.placeEquipment(equipmentId, x, y);
        });
    }

    placeEquipment(equipmentId, x, y) {
        const equipment = this.equipment.get(equipmentId);
        if (!equipment) return;

        const activeEquipment = document.getElementById('active-equipment');
        const equipmentInstance = document.createElement('div');
        equipmentInstance.className = 'placed-equipment';
        equipmentInstance.dataset.equipmentId = equipmentId;
        equipmentInstance.style.left = x + 'px';
        equipmentInstance.style.top = y + 'px';

        equipmentInstance.innerHTML = `
            <div class="equipment-visual ${equipment.type}">
                <i class="${equipment.icon}"></i>
                <div class="equipment-label">${equipment.name}</div>
                ${equipment.type === 'container' ? this.createContainerVisual(equipment) : ''}
            </div>
            <div class="equipment-controls">
                <button class="mini-btn" onclick="labBench.removeEquipment('${equipmentId}')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        // Make equipment draggable within workspace
        equipmentInstance.draggable = true;
        equipmentInstance.addEventListener('dragstart', (e) => {
            e.dataTransfer.setData('text/plain', 'move:' + equipmentId);
        });

        activeEquipment.appendChild(equipmentInstance);

        // Check for safety issues
        this.safetyMonitor.checkEquipmentPlacement(equipmentId, x, y);
    }

    createContainerVisual(equipment) {
        return `
            <div class="container-visual">
                <div class="liquid-level" style="height: 0%;" id="liquid-${equipment.id}"></div>
                <div class="volume-indicator">0 / ${equipment.capacity} mL</div>
            </div>
        `;
    }

    loadChemicalInventory() {
        const chemicals = [
            {
                id: 'hcl_01m',
                name: '0.1M Hydrochloric Acid',
                formula: 'HCl',
                concentration: 0.1,
                hazards: ['corrosive', 'toxic'],
                color: 'colorless',
                density: 1.003
            },
            {
                id: 'naoh_01m',
                name: '0.1M Sodium Hydroxide',
                formula: 'NaOH',
                concentration: 0.1,
                hazards: ['corrosive', 'caustic'],
                color: 'colorless',
                density: 1.004
            },
            {
                id: 'phenolphthalein',
                name: 'Phenolphthalein Indicator',
                formula: 'C20H14O4',
                concentration: 0.001,
                hazards: ['irritant'],
                color: 'colorless',
                density: 1.000
            },
            {
                id: 'distilled_water',
                name: 'Distilled Water',
                formula: 'H2O',
                concentration: 1.0,
                hazards: [],
                color: 'colorless',
                density: 1.000
            }
        ];

        const selector = document.getElementById('chemical-selector');
        chemicals.forEach(chemical => {
            this.chemicals.set(chemical.id, chemical);

            const option = document.createElement('option');
            option.value = chemical.id;
            option.textContent = chemical.name;
            selector.appendChild(option);
        });
    }

    // Equipment interaction methods
    setTemperature(temp) {
        document.getElementById('temp-value').textContent = temp;
        this.reactionEngine.setTemperature(parseFloat(temp));
        this.safetyMonitor.checkTemperature(parseFloat(temp));
    }

    startHeating() {
        this.safetyMonitor.addAlert('heating', 'Heating in progress - Monitor temperature carefully', 'warning');
        // Add heating animation
        document.querySelectorAll('.hot_plate').forEach(plate => {
            plate.classList.add('heating');
        });
    }

    stopHeating() {
        this.safetyMonitor.removeAlert('heating');
        document.querySelectorAll('.hot_plate').forEach(plate => {
            plate.classList.remove('heating');
        });
    }

    toggleStirring() {
        const stirrers = document.querySelectorAll('.magnetic_stirrer');
        stirrers.forEach(stirrer => {
            stirrer.classList.toggle('stirring');
        });
    }

    setStirSpeed(speed) {
        // Adjust stirring animation speed
        const stirrers = document.querySelectorAll('.magnetic_stirrer');
        stirrers.forEach(stirrer => {
            stirrer.style.animationDuration = speed > 0 ? (2 / speed) + 's' : 'infinite';
        });
    }

    selectChemical(chemicalId) {
        const chemical = this.chemicals.get(chemicalId);
        if (chemical) {
            this.safetyMonitor.checkChemicalSafety(chemical);
        }
    }

    addChemical() {
        const chemicalId = document.getElementById('chemical-selector').value;
        const amount = parseFloat(document.getElementById('chemical-amount').value);

        if (!chemicalId || !amount) {
            alert('Please select a chemical and specify amount');
            return;
        }

        const chemical = this.chemicals.get(chemicalId);
        if (chemical) {
            this.performChemicalAddition(chemical, amount);
            this.reactionEngine.addChemical(chemical, amount);
        }
    }

    performChemicalAddition(chemical, amount) {
        // Find active containers
        const containers = document.querySelectorAll('.placed-equipment .container');
        if (containers.length === 0) {
            alert('Please place a container on the bench first');
            return;
        }

        // Add to first available container (simplified)
        const container = containers[0];
        const liquidLevel = container.querySelector('.liquid-level');
        const volumeIndicator = container.querySelector('.volume-indicator');

        if (liquidLevel && volumeIndicator) {
            // Update visual representation
            const currentVolume = parseFloat(liquidLevel.dataset.volume || 0);
            const newVolume = currentVolume + amount;
            const capacity = parseFloat(container.dataset.capacity || 250);

            if (newVolume <= capacity) {
                const percentage = (newVolume / capacity) * 100;
                liquidLevel.style.height = percentage + '%';
                liquidLevel.style.backgroundColor = chemical.color === 'colorless' ? '#e3f2fd' : chemical.color;
                liquidLevel.dataset.volume = newVolume;
                volumeIndicator.textContent = `${newVolume.toFixed(1)} / ${capacity} mL`;

                // Check for reactions
                this.checkForReactions(chemical, amount);
            } else {
                alert('Container overflow! Reduce amount or use larger container.');
            }
        }
    }

    checkForReactions(newChemical, amount) {
        // Simple reaction detection (can be expanded)
        const existingChemicals = this.getCurrentChemicals();

        // Example: HCl + NaOH → NaCl + H2O
        if (existingChemicals.includes('hcl_01m') && newChemical.id === 'naoh_01m') {
            this.triggerReaction('neutralization', [newChemical]);
        }
    }

    getCurrentChemicals() {
        // Return list of chemicals currently in containers
        return ['hcl_01m']; // Simplified
    }

    triggerReaction(reactionType, chemicals) {
        this.reactionEngine.startReaction(reactionType, chemicals);
        this.safetyMonitor.monitorReaction(reactionType);
    }

    // Safety and utility methods
    emergencyStop() {
        this.stopHeating();
        this.setStirSpeed(0);
        this.safetyMonitor.emergencyStop();
        alert('EMERGENCY STOP ACTIVATED - All processes halted');
    }

    showSafetyInfo() {
        const modal = document.createElement('div');
        modal.className = 'safety-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <h3>Laboratory Safety Information</h3>
                <div class="safety-rules">
                    <h4>General Safety Rules:</h4>
                    <ul>
                        <li>Always wear appropriate PPE</li>
                        <li>Never mix chemicals without proper knowledge</li>
                        <li>Keep workspace clean and organized</li>
                        <li>Report all accidents immediately</li>
                        <li>Know location of safety equipment</li>
                    </ul>
                </div>
                <button onclick="this.parentElement.parentElement.remove()">Close</button>
            </div>
        `;
        document.body.appendChild(modal);
    }

    showEquipmentInfo(equipment) {
        alert(`${equipment.name}\n\n${equipment.description}\n\nType: ${equipment.type}\n${equipment.capacity ? `Capacity: ${equipment.capacity}mL` : ''}`);
    }

    removeEquipment(equipmentId) {
        const equipment = document.querySelector(`[data-equipment-id="${equipmentId}"]`);
        if (equipment) {
            equipment.remove();
        }
    }

    toggleMolecularView() {
        this.reactionEngine.toggleMolecularView();
    }

    resetReaction() {
        this.reactionEngine.reset();
        // Reset all containers
        document.querySelectorAll('.liquid-level').forEach(level => {
            level.style.height = '0%';
            level.dataset.volume = '0';
        });
        document.querySelectorAll('.volume-indicator').forEach(indicator => {
            indicator.textContent = '0 / 250 mL';
        });
    }
}

// Safety Monitor System
class SafetyMonitor {
    constructor() {
        this.alerts = new Map();
        this.safetyLevel = 'safe'; // safe, warning, danger
        this.temperatureLimit = 150; // °C
        this.emergencyActive = false;
        this.hazardousChemicals = new Set(['corrosive', 'toxic', 'flammable', 'explosive']);
    }

    checkEquipmentPlacement(equipmentId, x, y) {
        // Check for safe equipment placement
        const equipment = document.querySelector(`[data-equipment-id="${equipmentId}"]`);
        if (equipment) {
            // Example safety check: heating equipment near flammables
            this.validatePlacement(equipmentId, x, y);
        }
    }

    validatePlacement(equipmentId, x, y) {
        // Implement placement validation logic
        if (equipmentId.includes('hot_plate')) {
            this.addAlert('placement', 'Hot plate placed - Ensure adequate ventilation', 'warning');
        }
    }

    checkTemperature(temperature) {
        if (temperature > this.temperatureLimit) {
            this.addAlert('temperature', `Temperature exceeds safe limit (${this.temperatureLimit}°C)`, 'danger');
            this.safetyLevel = 'danger';
        } else if (temperature > 100) {
            this.addAlert('temperature', 'High temperature - Exercise caution', 'warning');
            this.safetyLevel = 'warning';
        } else {
            this.removeAlert('temperature');
            this.updateSafetyLevel();
        }
        this.updateSafetyDisplay();
    }

    checkChemicalSafety(chemical) {
        if (chemical.hazards && chemical.hazards.length > 0) {
            const hazardText = chemical.hazards.join(', ');
            this.addAlert('chemical', `${chemical.name} - Hazards: ${hazardText}`, 'warning');

            if (chemical.hazards.some(h => ['corrosive', 'toxic'].includes(h))) {
                this.safetyLevel = 'warning';
            }
        } else {
            this.removeAlert('chemical');
        }
        this.updateSafetyDisplay();
    }

    monitorReaction(reactionType) {
        switch (reactionType) {
            case 'neutralization':
                this.addAlert('reaction', 'Neutralization reaction in progress - Monitor pH', 'info');
                break;
            case 'exothermic':
                this.addAlert('reaction', 'Exothermic reaction - Temperature may rise rapidly', 'warning');
                break;
            case 'gas_evolution':
                this.addAlert('reaction', 'Gas evolution detected - Ensure adequate ventilation', 'warning');
                break;
        }
    }

    addAlert(id, message, type = 'info') {
        this.alerts.set(id, { message, type, timestamp: Date.now() });
        this.displayAlert(id, message, type);
    }

    removeAlert(id) {
        this.alerts.delete(id);
        const alertElement = document.getElementById(`alert-${id}`);
        if (alertElement) {
            alertElement.remove();
        }
        this.updateSafetyLevel();
    }

    displayAlert(id, message, type) {
        const alertsContainer = document.getElementById('safety-alerts');
        if (!alertsContainer) return;

        // Remove existing alert with same ID
        const existingAlert = document.getElementById(`alert-${id}`);
        if (existingAlert) {
            existingAlert.remove();
        }

        const alertElement = document.createElement('div');
        alertElement.id = `alert-${id}`;
        alertElement.className = `safety-alert ${type}`;

        const icons = {
            info: 'fas fa-info-circle',
            warning: 'fas fa-exclamation-triangle',
            danger: 'fas fa-exclamation-circle'
        };

        alertElement.innerHTML = `
            <div class="alert-content">
                <i class="${icons[type] || icons.info}"></i>
                <span class="alert-message">${message}</span>
                <button class="alert-close" onclick="labBench.safetyMonitor.removeAlert('${id}')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        alertsContainer.appendChild(alertElement);
    }

    updateSafetyLevel() {
        // Determine overall safety level based on active alerts
        let maxLevel = 'safe';

        this.alerts.forEach(alert => {
            if (alert.type === 'danger') maxLevel = 'danger';
            else if (alert.type === 'warning' && maxLevel !== 'danger') maxLevel = 'warning';
        });

        this.safetyLevel = maxLevel;
    }

    updateSafetyDisplay() {
        const safetyLed = document.getElementById('safety-led');
        if (safetyLed) {
            safetyLed.className = `status-led ${this.safetyLevel}`;
        }
    }

    emergencyStop() {
        this.emergencyActive = true;
        this.addAlert('emergency', 'EMERGENCY STOP ACTIVATED - All processes halted', 'danger');
        this.safetyLevel = 'danger';
        this.updateSafetyDisplay();
    }
}

// Reaction Visualization Engine
class ReactionVisualizationEngine {
    constructor() {
        this.canvas = null;
        this.ctx = null;
        this.molecules = [];
        this.reactions = [];
        this.animationId = null;
        this.isRunning = false;
        this.showMolecular = false;
        this.temperature = 25;
        this.time = 0;
    }

    initCanvas(canvas) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.setupCanvas();
    }

    setupCanvas() {
        if (!this.ctx) return;

        // Set up canvas styling
        this.ctx.fillStyle = '#f8f9fa';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // Draw initial state
        this.drawWelcomeMessage();
    }

    drawWelcomeMessage() {
        if (!this.ctx) return;

        this.ctx.fillStyle = '#6c757d';
        this.ctx.font = '16px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText('Add chemicals to see molecular interactions',
                         this.canvas.width / 2, this.canvas.height / 2);
    }

    addChemical(chemical, amount) {
        // Create molecules based on chemical formula
        const moleculeCount = Math.floor(amount * 10); // Simplified scaling

        for (let i = 0; i < moleculeCount; i++) {
            const molecule = this.createMolecule(chemical);
            this.molecules.push(molecule);
        }

        if (!this.isRunning) {
            this.startAnimation();
        }
    }

    createMolecule(chemical) {
        return {
            formula: chemical.formula,
            x: Math.random() * (this.canvas.width - 40) + 20,
            y: Math.random() * (this.canvas.height - 40) + 20,
            vx: (Math.random() - 0.5) * 2,
            vy: (Math.random() - 0.5) * 2,
            radius: this.getMoleculeRadius(chemical.formula),
            color: this.getMoleculeColor(chemical.formula),
            energy: this.temperature / 25, // Energy based on temperature
            bonds: []
        };
    }

    getMoleculeRadius(formula) {
        const radii = {
            'HCl': 3,
            'NaOH': 4,
            'H2O': 2,
            'NaCl': 3,
            'C20H14O4': 6 // Phenolphthalein
        };
        return radii[formula] || 3;
    }

    getMoleculeColor(formula) {
        const colors = {
            'HCl': '#ff6b6b',
            'NaOH': '#4ecdc4',
            'H2O': '#74b9ff',
            'NaCl': '#a29bfe',
            'C20H14O4': '#fd79a8'
        };
        return colors[formula] || '#95a5a6';
    }

    startReaction(reactionType, chemicals) {
        this.reactions.push({
            type: reactionType,
            chemicals: chemicals,
            progress: 0,
            startTime: this.time
        });
    }

    startAnimation() {
        if (this.isRunning) return;

        this.isRunning = true;
        this.animate();
    }

    animate() {
        if (!this.isRunning || !this.ctx) return;

        this.time += 16; // ~60fps

        // Clear canvas
        this.ctx.fillStyle = '#f8f9fa';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        if (this.showMolecular) {
            this.updateMolecules();
            this.drawMolecules();
            this.processReactions();
        } else {
            this.drawBulkView();
        }

        this.animationId = requestAnimationFrame(() => this.animate());
    }

    updateMolecules() {
        this.molecules.forEach(molecule => {
            // Update position
            molecule.x += molecule.vx * molecule.energy;
            molecule.y += molecule.vy * molecule.energy;

            // Boundary collisions
            if (molecule.x <= molecule.radius || molecule.x >= this.canvas.width - molecule.radius) {
                molecule.vx *= -1;
                molecule.x = Math.max(molecule.radius, Math.min(this.canvas.width - molecule.radius, molecule.x));
            }

            if (molecule.y <= molecule.radius || molecule.y >= this.canvas.height - molecule.radius) {
                molecule.vy *= -1;
                molecule.y = Math.max(molecule.radius, Math.min(this.canvas.height - molecule.radius, molecule.y));
            }
        });

        // Check for molecular collisions and reactions
        this.checkMolecularCollisions();
    }

    checkMolecularCollisions() {
        for (let i = 0; i < this.molecules.length; i++) {
            for (let j = i + 1; j < this.molecules.length; j++) {
                const mol1 = this.molecules[i];
                const mol2 = this.molecules[j];

                const dx = mol2.x - mol1.x;
                const dy = mol2.y - mol1.y;
                const distance = Math.sqrt(dx * dx + dy * dy);

                if (distance < mol1.radius + mol2.radius) {
                    // Check for reaction
                    this.checkReactionPossibility(mol1, mol2);

                    // Elastic collision
                    const angle = Math.atan2(dy, dx);
                    const sin = Math.sin(angle);
                    const cos = Math.cos(angle);

                    // Rotate velocities
                    const v1x = mol1.vx * cos + mol1.vy * sin;
                    const v1y = mol1.vy * cos - mol1.vx * sin;
                    const v2x = mol2.vx * cos + mol2.vy * sin;
                    const v2y = mol2.vy * cos - mol2.vx * sin;

                    // Exchange velocities
                    mol1.vx = v2x * cos - v1y * sin;
                    mol1.vy = v1y * cos + v2x * sin;
                    mol2.vx = v1x * cos - v2y * sin;
                    mol2.vy = v2y * cos + v1x * sin;

                    // Separate molecules
                    const overlap = mol1.radius + mol2.radius - distance;
                    const separationX = (overlap / 2) * cos;
                    const separationY = (overlap / 2) * sin;

                    mol1.x -= separationX;
                    mol1.y -= separationY;
                    mol2.x += separationX;
                    mol2.y += separationY;
                }
            }
        }
    }

    checkReactionPossibility(mol1, mol2) {
        // Example: HCl + NaOH → NaCl + H2O
        if ((mol1.formula === 'HCl' && mol2.formula === 'NaOH') ||
            (mol1.formula === 'NaOH' && mol2.formula === 'HCl')) {

            if (Math.random() < 0.01) { // 1% chance per collision
                this.performMolecularReaction(mol1, mol2, 'neutralization');
            }
        }
    }

    performMolecularReaction(mol1, mol2, reactionType) {
        // Remove reactants
        const index1 = this.molecules.indexOf(mol1);
        const index2 = this.molecules.indexOf(mol2);

        if (index1 > -1) this.molecules.splice(index1, 1);
        if (index2 > -1) this.molecules.splice(index2, 1);

        // Add products
        if (reactionType === 'neutralization') {
            // Create NaCl
            this.molecules.push(this.createMolecule({
                formula: 'NaCl',
                color: '#a29bfe'
            }));

            // Create H2O
            this.molecules.push(this.createMolecule({
                formula: 'H2O',
                color: '#74b9ff'
            }));
        }
    }

    drawMolecules() {
        this.molecules.forEach(molecule => {
            this.ctx.fillStyle = molecule.color;
            this.ctx.beginPath();
            this.ctx.arc(molecule.x, molecule.y, molecule.radius, 0, 2 * Math.PI);
            this.ctx.fill();

            // Draw formula label
            this.ctx.fillStyle = '#2d3748';
            this.ctx.font = '8px Arial';
            this.ctx.textAlign = 'center';
            this.ctx.fillText(molecule.formula, molecule.x, molecule.y + 2);
        });
    }

    drawBulkView() {
        // Draw bulk solution representation
        this.ctx.fillStyle = '#e3f2fd';
        this.ctx.fillRect(20, 20, this.canvas.width - 40, this.canvas.height - 40);

        this.ctx.strokeStyle = '#1976d2';
        this.ctx.lineWidth = 2;
        this.ctx.strokeRect(20, 20, this.canvas.width - 40, this.canvas.height - 40);

        // Show reaction progress
        this.reactions.forEach(reaction => {
            this.ctx.fillStyle = '#2d3748';
            this.ctx.font = '14px Arial';
            this.ctx.textAlign = 'center';
            this.ctx.fillText(`${reaction.type} reaction in progress...`,
                             this.canvas.width / 2, this.canvas.height / 2);
        });
    }

    processReactions() {
        this.reactions.forEach(reaction => {
            reaction.progress = Math.min(1, (this.time - reaction.startTime) / 5000); // 5 second reactions
        });

        // Remove completed reactions
        this.reactions = this.reactions.filter(reaction => reaction.progress < 1);
    }

    setTemperature(temp) {
        this.temperature = temp;
        // Update molecular energy
        this.molecules.forEach(molecule => {
            molecule.energy = temp / 25;
        });
    }

    toggleMolecularView() {
        this.showMolecular = !this.showMolecular;
    }

    reset() {
        this.molecules = [];
        this.reactions = [];
        this.isRunning = false;

        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
            this.animationId = null;
        }

        this.setupCanvas();
    }
}

// Global lab bench instance
let labBench;

document.addEventListener('DOMContentLoaded', function() {
    // Initialize lab bench when DOM is ready
    if (document.getElementById('lab-workspace')) {
        labBench = new VirtualLabBench('lab-workspace');
    }
});