<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Tools & Features Showcase - SimLab HUB</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="css/virtual-instruments.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #2d3748;
            line-height: 1.6;
        }

        .showcase-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .showcase-header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .showcase-header h1 {
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .showcase-header p {
            font-size: 1.2rem;
            opacity: 0.9;
            max-width: 800px;
            margin: 0 auto;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            margin-bottom: 50px;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            margin: 0 auto 20px;
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }

        .feature-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 15px;
            text-align: center;
            color: #2d3748;
        }

        .feature-description {
            color: #4a5568;
            margin-bottom: 20px;
            text-align: center;
        }

        .feature-demo {
            background: #f7fafc;
            border-radius: 10px;
            padding: 20px;
            border: 2px solid #e2e8f0;
            min-height: 200px;
        }

        .demo-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            flex-wrap: wrap;
            justify-content: center;
        }

        .demo-btn {
            background: linear-gradient(145deg, #4299e1, #3182ce);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 8px;
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(66, 153, 225, 0.3);
        }

        .demo-btn:hover {
            background: linear-gradient(145deg, #3182ce, #2c5282);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(66, 153, 225, 0.4);
        }

        .demo-btn.active {
            background: linear-gradient(145deg, #48bb78, #38a169);
        }

        .demo-area {
            background: white;
            border-radius: 8px;
            padding: 15px;
            border: 2px solid #e2e8f0;
            min-height: 150px;
            position: relative;
        }

        .navigation-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .navigation-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 20px;
            color: #2d3748;
        }

        .navigation-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .nav-card {
            background: linear-gradient(145deg, #f7fafc, #edf2f7);
            border: 2px solid #e2e8f0;
            border-radius: 15px;
            padding: 25px;
            text-decoration: none;
            color: #2d3748;
            transition: all 0.3s ease;
            display: block;
        }

        .nav-card:hover {
            border-color: #4299e1;
            background: linear-gradient(145deg, #ebf8ff, #bee3f8);
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(66, 153, 225, 0.2);
            text-decoration: none;
            color: #2d3748;
        }

        .nav-icon {
            font-size: 3rem;
            color: #4299e1;
            margin-bottom: 15px;
        }

        .nav-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .nav-description {
            font-size: 0.9rem;
            color: #4a5568;
        }

        .status-display {
            background: #1a202c;
            color: #00ff41;
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            margin-top: 10px;
            border: 1px solid #4a5568;
        }

        @media (max-width: 768px) {
            .showcase-header h1 {
                font-size: 2rem;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .feature-card {
                padding: 20px;
            }
            
            .navigation-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="showcase-container">
        <!-- Header -->
        <div class="showcase-header">
            <h1>🔬 Interactive Tools & Features</h1>
            <p>Explore our comprehensive suite of virtual instruments, data logging systems, variable controls, and animated visualizations designed for immersive scientific learning.</p>
        </div>

        <!-- Features Grid -->
        <div class="features-grid">
            <!-- Virtual Instruments -->
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-tools"></i>
                </div>
                <h2 class="feature-title">Virtual Instruments</h2>
                <p class="feature-description">
                    Realistic virtual instruments including calipers, thermometers, voltmeters, and oscilloscopes with accurate measurements and visual feedback.
                </p>
                <div class="feature-demo">
                    <div class="demo-controls">
                        <button class="demo-btn active" onclick="showInstrument('thermometer')">Thermometer</button>
                        <button class="demo-btn" onclick="showInstrument('voltmeter')">Voltmeter</button>
                        <button class="demo-btn" onclick="showInstrument('caliper')">Caliper</button>
                    </div>
                    <div class="demo-area" id="instrument-demo">
                        <!-- Instrument will be loaded here -->
                    </div>
                </div>
            </div>

            <!-- Data Logging -->
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <h2 class="feature-title">Data Logging & Graphing</h2>
                <p class="feature-description">
                    Automatic data recording with real-time graph generation, export capabilities, and comprehensive analysis tools.
                </p>
                <div class="feature-demo">
                    <div class="demo-controls">
                        <button class="demo-btn" onclick="startDataLogging()">Start Recording</button>
                        <button class="demo-btn" onclick="stopDataLogging()">Stop Recording</button>
                        <button class="demo-btn" onclick="exportData()">Export Data</button>
                    </div>
                    <div class="demo-area" id="data-demo">
                        <div id="data-chart-container"></div>
                        <div class="status-display" id="data-status">
                            Status: Ready to record data
                        </div>
                    </div>
                </div>
            </div>

            <!-- Variable Controls -->
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-sliders-h"></i>
                </div>
                <h2 class="feature-title">Variable Controls</h2>
                <p class="feature-description">
                    Comprehensive control panels with sliders, buttons, and input fields for adjusting experimental parameters in real-time.
                </p>
                <div class="feature-demo">
                    <div class="demo-controls">
                        <button class="demo-btn" onclick="addControl('slider')">Add Slider</button>
                        <button class="demo-btn" onclick="addControl('toggle')">Add Toggle</button>
                        <button class="demo-btn" onclick="resetControls()">Reset</button>
                    </div>
                    <div class="demo-area" id="controls-demo">
                        <!-- Controls will be added here -->
                    </div>
                </div>
            </div>

            <!-- Animated Visualizations -->
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-eye"></i>
                </div>
                <h2 class="feature-title">Animated Visualizations</h2>
                <p class="feature-description">
                    Visualize invisible phenomena like force fields, energy transformations, and particle motion with stunning animations.
                </p>
                <div class="feature-demo">
                    <div class="demo-controls">
                        <button class="demo-btn" onclick="showVisualization('forcefield')">Force Field</button>
                        <button class="demo-btn" onclick="showVisualization('particles')">Particles</button>
                        <button class="demo-btn" onclick="showVisualization('waves')">Waves</button>
                    </div>
                    <div class="demo-area" id="visualization-demo">
                        <!-- Visualizations will be rendered here -->
                    </div>
                </div>
            </div>

            <!-- Chemistry Lab -->
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-flask"></i>
                </div>
                <h2 class="feature-title">Chemistry Laboratory</h2>
                <p class="feature-description">
                    Complete chemistry lab with 15 experiments including titration, calorimetry, synthesis, and safety protocols.
                </p>
                <div class="feature-demo">
                    <div class="demo-controls">
                        <button class="demo-btn" onclick="startChemDemo('titration')">Titration</button>
                        <button class="demo-btn" onclick="startChemDemo('calorimetry')">Calorimetry</button>
                        <button class="demo-btn" onclick="startChemDemo('synthesis')">Synthesis</button>
                    </div>
                    <div class="demo-area" id="chemistry-demo">
                        <div style="text-align: center; padding: 40px; color: #4a5568;">
                            <i class="fas fa-vial" style="font-size: 3rem; margin-bottom: 15px; color: #4299e1;"></i>
                            <div>Select an experiment to see a preview</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Integration Features -->
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-puzzle-piece"></i>
                </div>
                <h2 class="feature-title">Integrated Experience</h2>
                <p class="feature-description">
                    All tools work together seamlessly - instruments feed data to loggers, controls adjust visualizations, and everything is recorded for analysis.
                </p>
                <div class="feature-demo">
                    <div class="demo-controls">
                        <button class="demo-btn" onclick="showIntegration()">Show Integration</button>
                        <button class="demo-btn" onclick="runFullDemo()">Full Demo</button>
                    </div>
                    <div class="demo-area" id="integration-demo">
                        <div style="text-align: center; padding: 40px; color: #4a5568;">
                            <i class="fas fa-cogs" style="font-size: 3rem; margin-bottom: 15px; color: #4299e1;"></i>
                            <div>Click "Show Integration" to see how all tools work together</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation Section -->
        <div class="navigation-section">
            <h2 class="navigation-title">🚀 Explore Our Virtual Laboratories</h2>
            <p>Ready to dive deeper? Visit our specialized laboratory environments:</p>
            
            <div class="navigation-grid">
                <a href="enhanced-virtual-lab.html" class="nav-card">
                    <div class="nav-icon">
                        <i class="fas fa-microscope"></i>
                    </div>
                    <div class="nav-title">Enhanced Physics Lab</div>
                    <div class="nav-description">
                        Complete physics laboratory with all virtual instruments, data logging, and visualization tools integrated.
                    </div>
                </a>
                
                <a href="chemistry-lab.html" class="nav-card">
                    <div class="nav-icon">
                        <i class="fas fa-flask"></i>
                    </div>
                    <div class="nav-title">Chemistry Laboratory</div>
                    <div class="nav-description">
                        Comprehensive chemistry lab with 15 experiments, safety protocols, and real-time reaction simulations.
                    </div>
                </a>
                
                <a href="experiment-showcase.html" class="nav-card">
                    <div class="nav-icon">
                        <i class="fas fa-atom"></i>
                    </div>
                    <div class="nav-title">Physics Experiments</div>
                    <div class="nav-description">
                        Interactive physics experiments covering mechanics, thermodynamics, optics, and modern physics.
                    </div>
                </a>
                
                <a href="virtual-labs.html" class="nav-card">
                    <div class="nav-icon">
                        <i class="fas fa-desktop"></i>
                    </div>
                    <div class="nav-title">Virtual Labs Hub</div>
                    <div class="nav-description">
                        Central hub for accessing all virtual laboratory environments and educational resources.
                    </div>
                </a>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/virtual-instruments.js"></script>
    <script src="js/data-logger.js"></script>
    <script src="js/variable-controls.js"></script>
    <script src="js/animated-visualizer.js"></script>
    <script src="js/chemistry-experiments.js"></script>
    
    <script>
        // Showcase Controller
        class InteractiveShowcase {
            constructor() {
                this.currentInstrument = null;
                this.currentVisualization = null;
                this.dataLogger = null;
                this.controlPanel = null;
                this.visualizer = null;
                this.isRecording = false;
                
                this.init();
            }

            init() {
                // Initialize components
                this.dataLogger = globalDataLogger;
                this.visualizer = globalVisualizer;
                
                // Show default instrument
                this.showInstrument('thermometer');
                
                // Initialize data chart
                this.initDataChart();
                
                // Initialize controls demo
                this.initControlsDemo();
            }

            showInstrument(type) {
                // Update button states
                document.querySelectorAll('#instrument-demo').forEach(demo => {
                    demo.parentElement.querySelectorAll('.demo-btn').forEach(btn => {
                        btn.classList.remove('active');
                    });
                });
                event.target.classList.add('active');

                const container = document.getElementById('instrument-demo');
                container.innerHTML = '';

                switch (type) {
                    case 'thermometer':
                        virtualThermometer.createUI('instrument-demo');
                        break;
                    case 'voltmeter':
                        virtualVoltmeter.createUI('instrument-demo');
                        break;
                    case 'caliper':
                        virtualCaliper.createUI('instrument-demo');
                        break;
                }
            }

            initDataChart() {
                const container = document.getElementById('data-chart-container');
                if (container) {
                    this.dataChart = this.dataLogger.createChart('data-chart-container', {
                        width: 300,
                        height: 120,
                        maxPoints: 50
                    });
                }
            }

            startDataLogging() {
                if (!this.isRecording) {
                    this.isRecording = true;
                    this.dataLogger.createSession('Showcase Demo');
                    this.dataLogger.startRecording();
                    
                    document.getElementById('data-status').textContent = 'Status: Recording data...';
                    
                    // Simulate data generation
                    this.generateSampleData();
                }
            }

            stopDataLogging() {
                if (this.isRecording) {
                    this.isRecording = false;
                    this.dataLogger.stopRecording();
                    document.getElementById('data-status').textContent = 'Status: Recording stopped';
                }
            }

            exportData() {
                if (this.dataLogger.currentSession) {
                    this.dataLogger.downloadData(this.dataLogger.currentSession.id, 'csv');
                } else {
                    alert('No data to export. Start recording first.');
                }
            }

            generateSampleData() {
                if (!this.isRecording) return;
                
                const time = Date.now();
                const value = 25 + 10 * Math.sin(time / 2000) + Math.random() * 2;
                
                this.dataLogger.logData(time, { temperature: value });
                
                setTimeout(() => this.generateSampleData(), 200);
            }

            initControlsDemo() {
                // Create a sample control panel
                this.controlPanel = variableControls.createControlPanel('controls-demo', {
                    title: 'Demo Controls',
                    showPresets: false,
                    showExport: false
                });

                // Add a sample variable
                variableControls.registerVariable('demo_temp', {
                    defaultValue: 25,
                    min: 0,
                    max: 100,
                    step: 0.1,
                    unit: '°C',
                    description: 'Demo temperature control'
                });

                this.controlPanel.addControl('demo_temp', 'slider');
            }

            addControl(type) {
                const varName = `demo_${type}_${Date.now()}`;
                
                variableControls.registerVariable(varName, {
                    defaultValue: type === 'toggle' ? 0 : 50,
                    min: 0,
                    max: 100,
                    step: 1,
                    unit: type === 'toggle' ? '' : 'units',
                    description: `Demo ${type} control`
                });

                this.controlPanel.addControl(varName, type);
            }

            resetControls() {
                const container = document.getElementById('controls-demo');
                container.innerHTML = '';
                this.initControlsDemo();
            }

            showVisualization(type) {
                const container = document.getElementById('visualization-demo');
                
                // Initialize visualizer if needed
                if (!this.visualizer.canvas) {
                    this.visualizer.initCanvas('visualization-demo', 300, 150);
                }

                // Remove existing visualization
                if (this.currentVisualization) {
                    this.visualizer.removeVisualization(this.currentVisualization);
                }

                // Create new visualization
                switch (type) {
                    case 'forcefield':
                        this.currentVisualization = this.visualizer.createForceField({
                            center: { x: 150, y: 75 },
                            fieldLines: 8
                        });
                        break;
                    case 'particles':
                        this.currentVisualization = this.visualizer.createParticleMotion({
                            particleCount: 20,
                            showTrails: true
                        });
                        break;
                    case 'waves':
                        this.currentVisualization = this.visualizer.createWaveVisualization({
                            amplitude: 30,
                            wavelength: 60
                        });
                        break;
                }

                this.visualizer.startAnimation();
            }

            startChemDemo(experiment) {
                const container = document.getElementById('chemistry-demo');
                
                const demos = {
                    titration: `
                        <div style="text-align: center; padding: 20px;">
                            <h4 style="color: #2d3748; margin-bottom: 15px;">Acid-Base Titration</h4>
                            <div style="background: #1a202c; color: #00ff41; padding: 10px; border-radius: 5px; font-family: monospace; margin-bottom: 15px;">
                                HCl + NaOH → NaCl + H₂O
                            </div>
                            <div style="color: #4a5568;">
                                Determine unknown acid concentration using standard base solution.
                                Features real-time pH calculation and color change indicators.
                            </div>
                        </div>
                    `,
                    calorimetry: `
                        <div style="text-align: center; padding: 20px;">
                            <h4 style="color: #2d3748; margin-bottom: 15px;">Calorimetry</h4>
                            <div style="background: #1a202c; color: #00ff41; padding: 10px; border-radius: 5px; font-family: monospace; margin-bottom: 15px;">
                                CaO + H₂O → Ca(OH)₂ + Heat
                            </div>
                            <div style="color: #4a5568;">
                                Measure heat of reaction with animated temperature changes.
                                Calculate enthalpy and compare with theoretical values.
                            </div>
                        </div>
                    `,
                    synthesis: `
                        <div style="text-align: center; padding: 20px;">
                            <h4 style="color: #2d3748; margin-bottom: 15px;">Aspirin Synthesis</h4>
                            <div style="background: #1a202c; color: #00ff41; padding: 10px; border-radius: 5px; font-family: monospace; margin-bottom: 15px;">
                                C₇H₆O₃ + (CH₃CO)₂O → C₉H₈O₄ + CH₃COOH
                            </div>
                            <div style="color: #4a5568;">
                                Synthesize aspirin with temperature control and yield calculation.
                                Includes safety protocols and reaction monitoring.
                            </div>
                        </div>
                    `
                };

                container.innerHTML = demos[experiment] || '';
            }

            showIntegration() {
                const container = document.getElementById('integration-demo');
                container.innerHTML = `
                    <div style="text-align: center; padding: 20px;">
                        <h4 style="color: #2d3748; margin-bottom: 15px;">🔗 Integrated Workflow</h4>
                        <div style="color: #4a5568; line-height: 1.6;">
                            <div style="margin-bottom: 10px;">📊 <strong>Instruments</strong> → Measure real values</div>
                            <div style="margin-bottom: 10px;">📈 <strong>Data Logger</strong> → Record measurements</div>
                            <div style="margin-bottom: 10px;">🎛️ <strong>Controls</strong> → Adjust parameters</div>
                            <div style="margin-bottom: 10px;">👁️ <strong>Visualizer</strong> → Show phenomena</div>
                            <div style="margin-bottom: 10px;">🧪 <strong>Experiments</strong> → Apply knowledge</div>
                        </div>
                        <div style="background: #e6fffa; padding: 15px; border-radius: 8px; margin-top: 15px; border-left: 4px solid #38b2ac;">
                            <strong>All components work together seamlessly!</strong><br>
                            Changes in one tool automatically update others for a cohesive learning experience.
                        </div>
                    </div>
                `;
            }

            runFullDemo() {
                alert('Full demo would showcase all tools working together in a complete experiment scenario. Visit the Enhanced Virtual Lab to experience the full integration!');
            }
        }

        // Initialize showcase
        let showcase;
        document.addEventListener('DOMContentLoaded', function() {
            showcase = new InteractiveShowcase();
        });

        // Global functions for demo buttons
        function showInstrument(type) {
            showcase.showInstrument(type);
        }

        function startDataLogging() {
            showcase.startDataLogging();
        }

        function stopDataLogging() {
            showcase.stopDataLogging();
        }

        function exportData() {
            showcase.exportData();
        }

        function addControl(type) {
            showcase.addControl(type);
        }

        function resetControls() {
            showcase.resetControls();
        }

        function showVisualization(type) {
            showcase.showVisualization(type);
        }

        function startChemDemo(experiment) {
            showcase.startChemDemo(experiment);
        }

        function showIntegration() {
            showcase.showIntegration();
        }

        function runFullDemo() {
            showcase.runFullDemo();
        }
    </script>
</body>
</html>
